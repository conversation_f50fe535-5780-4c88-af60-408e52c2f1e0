{"root": true, "parser": "babel-es<PERSON>", "extends": "airbnb", "env": {"browser": true, "node": true, "mocha": true}, "rules": {"semi": [2, "never"], "react/jsx-filename-extension": [2, {"extensions": [".js", ".jsx"]}], "react/prefer-stateless-function": [2, {"ignorePureComponents": true}]}, "plugins": ["react", "import"], "settings": {"import/parser": "babel-es<PERSON>", "import/resolve": {"moduleDirectory": ["node_modules", "src"]}}, "globals": {"__DEVELOPMENT__": true, "__CLIENT__": true, "__SERVER__": true, "__DISABLE_SSR__": true, "__DEVTOOLS__": true, "socket": true, "webpackIsomorphicTools": true}}