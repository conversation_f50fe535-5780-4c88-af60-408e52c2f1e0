import { grey50, grey300, grey800, darkBlack } from 'material-ui/styles/colors'
import { fade } from 'material-ui/utils/colorManipulator'
import spacing from 'material-ui/styles/spacing'

const primaryRed = '#FF0303'

const palette = {
  primary1Color: primaryRed,
  primary2Color: primaryRed,
  primary3Color: grey50,
  accent1Color: primaryRed,
  canvasColor: grey50,
  textColor: grey800,
  alternateTextColor: grey50,
  secondaryTextColor: grey800,
  borderColor: grey300,
  pickerHeaderColor: grey50,
  disabledColor: fade(darkBlack, 0.3),
  clockCircleColor: fade(darkBlack, 0.07),
  shadowColor: darkBlack,
}

const theme = {
  appBar: {
    color: grey50,
    textColor: primaryRed,
  },
  appBarTitle: {
    letterSpacing: '0.02em',
    fontSize: '25px',
    fontWeight: '400',
  },
  tabs: {
    backgroundColor: palette.primary3Color,
    textColor: palette.textColor,
    selectedTextColor: primaryRed,
  },
  spacing,
  fontFamily: 'Source Sans Pro, sans-serif',
  palette,
  timePicker: {
    textColor: palette.primary1Color,
    clockColor: palette.primary1Color,
    clockCircleColor: palette.clockCircleColor,
    headerColor: palette.pickerHeaderColor || palette.primary1Color,
    selectColor: palette.primary2Color,
    selectTextColor: palette.alternateTextColor,
  },
}

export default theme
