import 'babel-polyfill'
import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom'

import ApiClient from 'helpers/fetchClient'
import { makeArgumentsString } from 'helpers/graphQL'
import CompCardApp from 'containers/CompCardApp/CompCardApp'
import MediaQueryContext from 'components/MediaQueryContext/MediaQueryContext'

const apiClient = new ApiClient()

const slug = location.hash.substr(1)

apiClient
  .get('/api/graphql', {
    params: {
      query: `{
        publicCompCard${makeArgumentsString({ slug })} {
          name
          slug
          gender
          height
          waist
          inseam
          suit
          hips
          bust
          dressSize
          chest
          cup
          eyeColor
          shoeSize
          hairColor
          hairLength
          bodyType
          compCardA
          compCardB
          compCardC
          compCardD
          compCardCover
          compCardLabelPosition
        }
      }`,
    },
  })
  .then(({ data: { publicCompCard: data }, errors }) => {
    ReactDOM.render(
      <MediaQueryContext>
        <CompCardApp data={data} errors={errors} />
      </MediaQueryContext>,
      document.getElementById('root'),
    )
  })
  .catch((error) => {
    // poorly handled exception. aka.. none.
    console.log('ended up here...', error)
  })

if (process.env.NODE_ENV !== 'production') {
  window.React = React // enable debugger
}
