import React, { PureComponent, PropTypes } from 'react'
import Sugar from 'sugar-date'
import Paper from 'material-ui/Paper'
import ActionThumbsUpDownIcon from 'material-ui/svg-icons/action/thumbs-up-down'
import { white, grey100, grey200, red500 } from 'material-ui/styles/colors'

import { DEFAULT_AVATAR } from '../../constants'
import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'
import Heading from 'components/Heading/Heading'
import ListCountBadgyThing from './ListCountBadgyThing'

const { bool, number, string, object, oneOf, arrayOf } = PropTypes

const styles = {
  wrapper: {
    display: 'block',
    textDecoration: 'none',
    width: '100%',
    maxWidth: 375,
  },
  wrapperSm: {
    margin: '1em 3px',
    maxWidth: 358,
  },
  paper: {
    padding: '0.5em',
    width: '100%',
  },
  hovered: {
    // backgroundColor: grey100,
  },
  grid: {
    display: 'flex',
    flexWrap: 'wrap',
    height: 300, // 'calc(100vh / 3)',
    overflow: 'hidden',
    backgroundColor: grey200,
    // border: `3px solid ${grey200}`,
    borderRadius: 9,
  },
  talentSquare: {
    width: 'calc(100% / 3)',
    height: 'calc(100% / 2)',
    backgroundColor: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundImage: `url('${DEFAULT_AVATAR}')`,
    backgroundRepeat: 'no-repeat',
    boxSizing: 'border-box',
    border: `1px solid ${white}`,
  },
  summary: {
    padding: 3,
  },
  heading: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  },
  row1: {
    display: 'flex',
    justifyContent: 'space-between',
  },
  unapprovedAlert: {
    color: red500,
  },
}

function TalentSquare({ position, filler, profile: { avatar } = {} }) {
  const avatarImage = avatar && avatar.length && getMediaUrl(avatar)
  const avatarStyle = (avatarImage && { backgroundImage: `url('${avatarImage}')` }) || {
    backgroundSize: 'contain',
  }
  const fillerStyle = (filler && { backgroundImage: 'none' }) || {}
  let borderStyle = {}

  if (position <= 3) {
    borderStyle = { ...borderStyle, borderTop: 'none' }
  }

  if (position >= 4) {
    borderStyle = { ...borderStyle, borderBottom: 'none' }
  }

  if ([1, 4].includes(position)) {
    borderStyle = { ...borderStyle, borderLeft: 'none' }
  }

  if ([3, 6].includes(position)) {
    borderStyle = { ...borderStyle, borderRight: 'none' }
  }

  const style = sm(styles.talentSquare, avatarStyle, borderStyle, fillerStyle)

  return (
    <div style={style}>
      {filler
        ? <div
          style={{
            width: 'calc(100% - 2px)',
            height: 'calc(100% - 2px)',
            backgroundColor: white,
            margin: 1,
          }}
        />
        : null}
    </div>
  )
}

export default class TalentOverviewCard extends PureComponent {
  static propTypes = {
    projectId: string,
    type: oneOf(['casting', 'booking']),
    id: string.isRequired,
    name: string.isRequired,
    location: string.isRequired,
    dates: arrayOf(object),
    members: arrayOf(object),
    approved: bool,
    booking: object,
    rate: number,
  };

  static contextTypes = {
    mediaQueries: object,
  };

  static defaultProps = {
    type: 'castings',
    members: [],
    booking: {},
    approved: false,
  };

  state = {
    hovered: false,
  };

  componentDidMount() {
    this.node.addEventListener('mouseover', this::this.onMouseOver)
    this.node.addEventListener('mouseout', this::this.onMouseOut)
  }

  componentWillUnmount() {
    this.node.removeEventListener('mouseover', this.onMouseOver)
    this.node.removeEventListener('mouseout', this.onMouseOut)
  }

  onMouseOver() {
    this.setState({ hovered: true })
  }

  onMouseOut() {
    this.setState({ hovered: false })
  }

  makeRef = (node) => {
    this.node = node
  };

  get approved() {
    const { type, approved, booking } = this.props

    return type === 'casting' ? booking && booking.approved : approved
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const {
      projectId,
      id: castingOrBookingId,
      type,
      name,
      location,
      dates = [],
      members = [],
      rate = false,
    } = this.props
    const { hovered } = this.state
    const href = `#/projects/${projectId}/${type}/${castingOrBookingId}`
    const talentSquares = members.slice(0, 6)

    if (talentSquares.length < 6) {
      talentSquares.push(
        ...[...Array(6 % talentSquares.length || 6).keys()].map(id => ({ id, filler: true })),
      )
    }

    return (
      <a ref={this.makeRef} style={sm(styles.wrapper, !xs && styles.wrapperSm)} href={href}>
        <Paper
          rounded
          zDepth={(hovered && 1) || 0}
          style={sm(styles.paper, hovered && styles.hovered)}
        >
          <Heading
            text={`${rate ? 'Booking' : 'Casting'}: ${name}`}
            weight={400}
            size={1.25}
            style={styles.heading}
          />

          <div style={styles.grid}>
            {talentSquares.map((item, index) => (
              <TalentSquare key={item.id} position={index + 1} {...item} />
            ))}
          </div>

          <div style={styles.summary}>
            <div style={styles.row1}>
              <div>
                {location}<br />
                {dates.length ? Sugar.Date.format(new Date(dates[0].start), '{long}') : ''}
              </div>
              {this.approved
                ? <div>
                  <ListCountBadgyThing type="inline" status="invited" members={members} />
                  <ListCountBadgyThing type="inline" status="confirmed" members={members} />
                </div>
                : <div style={styles.unapprovedAlert}>
                  <ActionThumbsUpDownIcon
                    style={{ verticalAlign: 'middle', marginRight: 10 }}
                    color={red500}
                  />
                    Pending Approval
                  </div>}
            </div>
          </div>
        </Paper>
      </a>
    )
  }
}
