import React, { PureComponent, PropTypes } from 'react'
import ToggleCheckBoxIcon from 'material-ui/svg-icons/toggle/check-box'
import ContentSendIcon from 'material-ui/svg-icons/content/send'
import ContentArchiveIcon from 'material-ui/svg-icons/content/archive'
import AvNotInterestedIcon from 'material-ui/svg-icons/av/not-interested'
import { green500, yellow500, red500, grey500 } from 'material-ui/styles/colors'

import { LIST_MEMBER_STATUS } from '../../constants'
import sm from 'helpers/stylesMerge'

const { bool, string, object, oneOf, oneOfType, arrayOf } = PropTypes

const styles = {
  wrapper: {
    display: 'inline-block',
  },
  blockWrapper: {
    marginRight: '2em',
  },
  row1: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  row2: {
    textAlign: 'center',
  },
  count: {
    marginRight: 6,
  },
}

function StatusIcon({ status }) {
  switch (status) {
    case 'confirmed':
      return <ToggleCheckBoxIcon color={green500} />
    case 'invited':
      return <ContentSendIcon color={yellow500} />
    case 'declined':
      return <AvNotInterestedIcon color={red500} />
    case 'archived':
      return <ContentArchiveIcon color={grey500} />
    case 'applied':
    case 'pending':
    default:
      return <span />
  }
}

StatusIcon.propTypes = {
  status: string,
}

StatusIcon.defaultProps = {
  status: 'pending',
}

export default class ListCountBadgyThing extends PureComponent {
  static propTypes = {
    label: oneOfType([bool, string]),
    members: arrayOf(object),
    status: oneOf(LIST_MEMBER_STATUS),
    type: oneOf(['inline', 'block']),
  };

  static defaultProps = {
    label: false,
    status: 'confirmed',
    members: [],
    type: 'block',
  };

  get count() {
    const { members, status } = this.props

    return (members.find(member => member.members && !!member.members.length)
      ? members.reduce((list, member) => list.concat(member.members), [])
      : members).filter(item => item.status === status).length
  }

  render() {
    const { type, label, status } = this.props

    return (
      <div style={sm(styles.wrapper, (type === 'block' || label) && styles.blockWrapper)}>
        <div style={styles.row1}>
          <div style={styles.count}>
            {this.count}{type === 'inline' && label ? ` ${label}` : ''}
          </div>
          <div><StatusIcon status={status} /></div>
        </div>
        {type === 'block' ? <div style={styles.row2}>{label}</div> : null}
      </div>
    )
  }
}
