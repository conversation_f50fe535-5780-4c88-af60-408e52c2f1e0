import React, { PureComponent, PropTypes } from 'react'
import IconButton from 'material-ui/IconButton'
import EditorModeEditIcon from 'material-ui/svg-icons/editor/mode-edit'
import ImageAddAPhotoIcon from 'material-ui/svg-icons/image/add-a-photo'
import { white, blueGrey50, grey200, grey600 } from 'material-ui/styles/colors'

import { DEFAULT_PHOTO } from '../../constants'
import theme from 'theme/mui-theme'
import * as projectActions from 'redux/modules/project'
import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'
import Heading from 'components/Heading/Heading'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'
import MetaInfoLine from './MetaInfoLine'

const { bool, object } = PropTypes

const styles = {
  wrapper: {
    display: 'flex',
    justifyContent: 'center',
    marginBottom: '1em',
    flexWrap: 'wrap',
  },
  wrapperSm: {
    justifyContent: 'space-between',
  },
  avatar: {
    width: '100%',
    height: 180,
    backgroundColor: white,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center center',
    backgroundImage: `url('${DEFAULT_PHOTO}')`,
    display: 'flex',
    margin: '8px 0',
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  avatarSm: {
    width: 125,
    height: 90,
    backgroundColor: grey200,
    border: `3px solid ${blueGrey50}`,
    margin: 0,
  },
  fileStyle: {
    position: 'relative',
    top: -20,
    left: -20,
    opacity: 0,
  },
  photoButton: {
    background: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 3,
    margin: 6,
  },
  middle: {
    width: '100%',
  },
  middleSm: {
    width: 'initial',
    flex: 1,
    padding: '0 1em',
  },
  right: {
    marginTop: '1em',
  },
}

export default class ProjectHeading extends PureComponent {
  static propTypes = {
    project: object.isRequired,
    mutable: bool,
    isDashboard: bool,
  };

  static defaultProps = {
    mutable: false,
  };

  static contextTypes = {
    mediaQueries: object,
  };

  render() {
    const { mediaQueries: { xs } } = this.context
    const { project, mutable, isDashboard } = this.props
    const { id, name, avatar } = project
    const avatarImage = avatar && avatar.length && getMediaUrl(avatar, 256)
    const avatarStyle = (avatarImage && { backgroundImage: `url('${avatarImage}')` }) || {
      backgroundColor: grey200,
    }

    return (
      <div style={sm(styles.wrapper, !xs && styles.wrapperSm)}>
        <div style={sm(styles.avatar, !xs && styles.avatarSm, avatarStyle)}>
          {mutable && project && id
            ? <IconButton
              tooltip="Select project photo"
              touch
              tooltipPosition="bottom-right"
              style={sm(styles.photoButton, { margin: 4 })}
            >
              <ImageAddAPhotoIcon color={grey600} />
              <Form layout={false} action={projectActions.attachMedia} data={project}>
                <Field
                  mutable
                  type="file"
                  name="avatar"
                  layout={false}
                  style={styles.fileStyle}
                  accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff"
                />
              </Form>
            </IconButton>
            : null}
        </div>

        <div style={sm(styles.middle, !xs && styles.middleSm)}>
          <Heading text={name} weight={400} size={2} />
          <MetaInfoLine {...project} />

          {isDashboard
            ? <IconButton
              href={`#/projects/${id}/edit`}
              iconStyle={{
                width: 20,
                height: 20,
              }}
            >
              <EditorModeEditIcon />
            </IconButton>
            : null}
        </div>
      </div>
    )
  }
}
