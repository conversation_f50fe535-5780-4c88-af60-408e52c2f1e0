import React, { PureComponent, PropTypes } from 'react'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import IconButton from 'material-ui/IconButton'
import ActionCheckCircleIcon from 'material-ui/svg-icons/action/check-circle'
import ActionEjectIcon from 'material-ui/svg-icons/action/eject'
import ActionPageviewIcon from 'material-ui/svg-icons/action/pageview'
import ActionDeleteForeverIcon from 'material-ui/svg-icons/action/delete-forever'
import ContentSendIcon from 'material-ui/svg-icons/content/send'
import ContentArchiveIcon from 'material-ui/svg-icons/content/archive'
import ContentUnarchiveIcon from 'material-ui/svg-icons/content/unarchive'
import AvNotInterestedIcon from 'material-ui/svg-icons/av/not-interested'
import AvPlaylistAddIcon from 'material-ui/svg-icons/av/playlist-add'
import SocialPlusOneIcon from 'material-ui/svg-icons/social/plus-one'
import {
  white,
  grey200,
  grey500,
  red500,
  yellow500,
  green500,
  lightBlue500,
} from 'material-ui/styles/colors'

import { DEFAULT_AVATAR, LIST_MEMBER_STATUS } from '../../constants'
import * as castingActions from 'redux/modules/casting'
import * as bookingActions from 'redux/modules/booking'
import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'
import noop from 'helpers/noop'

const { bool, string, object, func, oneOf } = PropTypes

const styles = {
  wrapper: {
    width: 169,
    padding: 5,
    boxSizing: 'content-box',
  },
  wrapperSm: {
    width: 175,
    margin: 'calc(1em - 10px)',
  },
  hovered: {
    opacity: 1,
    filter: '',
  },
  avatar: {
    width: '100%',
    height: 169 / (2 / 3),
    backgroundColor: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundImage: `url('${DEFAULT_AVATAR}')`,
    backgroundRepeat: 'no-repeat',
    boxSizing: 'border-box',
    border: `1px solid ${white}`,
    position: 'relative',
    overflow: 'hidden',
  },
  avatarSm: {
    height: 175 / (2 / 3),
  },
  avatarHoverOverlay: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    display: 'none',
  },
  faded: {
    opacity: 0.65,
    filter: 'grayscale(50%)',
  },
  row1: {
    display: 'flex',
    justifyContent: 'space-between',
    padding: 3,
  },
  heading: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  },
  action: {
    position: 'absolute',
  },
  actionTop: {
    top: 3,
  },
  actionBottom: {
    bottom: 3,
  },
  actionLeft: {
    left: 3,
  },
  actionRight: {
    right: 3,
  },
  actionMiddle: {
    top: 'calc(50% - 48px)',
    left: 'calc(50% - 48px)',
  },
  mediumIcon: {
    width: 48,
    height: 48,
  },
  mediumButton: {
    width: 96,
    height: 96,
    padding: 24,
  },
  ribbon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    padding: 3,
    width: '100%',
    height: 20,
    textAlign: 'right',
    color: white,
    opacity: 0.7,
  },
  ribbonText: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    padding: 3,
    width: '100%',
    textAlign: 'right',
    color: white,
    textTransform: 'capitalize',
  },
  statusIcon: {
    verticalAlign: 'text-bottom',
    width: 20,
    height: 20,
  },
}

function getStatusColor(status) {
  switch (status) {
    case 'confirmed':
      return green500
    case 'invited':
      return yellow500
    case 'declined':
      return red500
    case 'archived':
      return grey500
    case 'released':
      return grey500
    case 'applied':
      return lightBlue500
    case 'pending':
    default:
      return white
  }
}

function StatusIcon({ status }) {
  let Icon = noop

  switch (status) {
    case 'confirmed':
      Icon = ActionCheckCircleIcon
      break
    case 'invited':
      Icon = ContentSendIcon
      break
    case 'declined':
      Icon = AvNotInterestedIcon
      break
    case 'archived':
      Icon = ContentArchiveIcon
      break
    case 'released':
      Icon = ActionEjectIcon
      break
    case 'applied':
      Icon = SocialPlusOneIcon
      break
    case 'pending':
    default:
      Icon = noop
  }

  return <Icon color={white} style={styles.statusIcon} />
}

@connect(() => ({}), {
  updateCastingMembership: castingActions.updateMembership,
  removeCastingMembership: castingActions.removeMember,
  updateBookingMembership: bookingActions.updateMembership,
  removeBookingMembership: bookingActions.removeMember,
})
export default class TalentListCard extends PureComponent {
  static propTypes = {
    projectId: string.isRequired,
    listId: string.isRequired,
    type: oneOf(['casting', 'booking']).isRequired,
    id: string,
    profile: object.isRequired,
    status: oneOf(LIST_MEMBER_STATUS).isRequired,
    approved: bool.isRequired,
    updateCastingMembership: func.isRequired,
    removeCastingMembership: func.isRequired,
    updateBookingMembership: func.isRequired,
    removeBookingMembership: func.isRequired,
    onAddToListPromptOpen: func.isRequired,
    setConfirmReleaseDialog: func.isRequired,
  };

  static contextTypes = {
    mediaQueries: object,
  };

  state = {
    hovered: false,
  };

  componentDidMount() {
    const { mediaQueries: { xs } } = this.context

    if (!xs) {
      this.node.addEventListener('mouseover', this::this.onMouseOver)
      this.node.addEventListener('mouseout', this::this.onMouseOut)
    }
  }

  componentWillUnmount() {
    const { mediaQueries: { xs } } = this.context

    if (!xs) {
      this.node.removeEventListener('mouseover', this.onMouseOver)
      this.node.removeEventListener('mouseout', this.onMouseOut)
    }
  }

  onMouseOver() {
    this.setState({ hovered: true })
  }

  onMouseOut() {
    this.setState({ hovered: false })
  }

  makeRef = (node) => {
    this.node = node
  };

  toggleHovered = () => this.setState(({ hovered }) => ({ hovered: !hovered }));

  updateMembership = (newStatus) => {
    const {
      listId,
      type,
      id: membershipId,
      updateCastingMembership,
      updateBookingMembership,
    } = this.props

    switch (type) {
      case 'booking':
        return updateBookingMembership(listId, membershipId, newStatus)
      case 'casting':
      default:
        return updateCastingMembership(listId, membershipId, newStatus)
    }
  };

  removeMembership = () => {
    const {
      listId,
      type,
      id: membershipId,
      removeCastingMembership,
      removeBookingMembership,
    } = this.props

    switch (type) {
      case 'booking':
        return removeBookingMembership(listId, membershipId)
      case 'casting':
      default:
        return removeCastingMembership(listId, membershipId)
    }
  };

  handleInviteAction = () => this.updateMembership('invited');

  handleArchiveAction = () => this.updateMembership('archived');

  handleUnarchiveAction = () => this.updateMembership('pending');

  handleReleaseAction = () => {
    const { type, profile: { name }, setConfirmReleaseDialog } = this.props

    setConfirmReleaseDialog(
      'Confirm release',
      <span>
        Are you sure you want to release <i>{name}</i> from this {type}?
        <i> {name}</i> will be notified that they have been released.
      </span>,
      () => this.updateMembership('released'),
    )
  };

  handleRemoveAction = () => {
    const { type, profile: { name }, setConfirmReleaseDialog } = this.props

    setConfirmReleaseDialog(
      'Confirm delete',
      <span>Are you sure you want to delete <i>{name}</i> from this {type}?</span>,
      this.removeMembership,
    )
  };

  handleAddToListAction = (event) => {
    this.props.onAddToListPromptOpen()
    event.stopPropagation()
  };

  render() {
    const { mediaQueries: { xs } } = this.context
    const {
      projectId,
      listId,
      type,
      status,
      approved,
      profile: { avatar, name, slug },
    } = this.props
    const { hovered } = this.state

    const href = `/projects/${projectId}/${type}/${listId}`
    const profileHref = `#/${slug}/portfolio/?returnTo=${href}`

    const avatarImage = avatar && avatar.length && getMediaUrl(avatar)
    const avatarStyle = (avatarImage && { backgroundImage: `url('${avatarImage}')` }) || {
      backgroundSize: 'contain',
    }

    return (
      <div ref={this.makeRef} onTouchTap={xs ? this.toggleHovered : noop}>
        <Paper
          zDepth={(hovered && 1) || 0}
          style={sm(
            styles.wrapper,
            !xs && styles.wrapperSm,
            ['declined', 'released'].includes(status) && styles.faded,
            hovered && styles.hovered,
          )}
        >
          <div style={sm(styles.avatar, avatarStyle, !xs && styles.avatarSm)}>
            <div style={sm(styles.avatarHoverOverlay, hovered && { display: 'block' })}>
              {approved && ['pending', 'applied'].includes(status)
                ? <div style={sm(styles.action, styles.actionTop, styles.actionLeft)}>
                  <IconButton
                    touch
                    onTouchTap={this.handleInviteAction}
                    tooltip="Invite"
                    _tooltipPosition=""
                  >
                    <ContentSendIcon color={white} />
                  </IconButton>
                </div>
                : null}

              <div style={sm(styles.action, styles.actionTop, styles.actionRight)}>
                <IconButton touch onTouchTap={this.handleAddToListAction} tooltip="Add to">
                  <AvPlaylistAddIcon color={white} />
                </IconButton>
              </div>

              <div style={sm(styles.action, styles.actionMiddle)}>
                <IconButton
                  touch
                  href={profileHref}
                  iconStyle={styles.mediumIcon}
                  style={styles.mediumButton}
                >
                  <ActionPageviewIcon color={white} />
                </IconButton>
              </div>

              {!['released', 'declined'].includes(status)
                ? <div style={sm(styles.action, styles.actionBottom, styles.actionLeft)}>
                  <IconButton
                    touch
                    onTouchTap={this.handleReleaseAction}
                    tooltip="Release"
                    tooltipPosition="top-right"
                  >
                    <ActionEjectIcon color={white} />
                  </IconButton>
                </div>
                : null}

              {status === 'released'
                ? <div style={sm(styles.action, styles.actionBottom, styles.actionLeft)}>
                  <IconButton
                    touch
                    onTouchTap={this.handleRemoveAction}
                    tooltip="Delete"
                    tooltipPosition="top-right"
                  >
                    <ActionDeleteForeverIcon color={white} />
                  </IconButton>
                </div>
                : null}
            </div>

            {!['pending'].includes(status)
              ? <div style={sm({}, hovered && { display: 'none' })}>
                <div style={sm(styles.ribbon, { backgroundColor: getStatusColor(status) })} />
                <div style={styles.ribbonText}>{status} <StatusIcon status={status} /></div>
              </div>
              : null}
          </div>

          <div style={styles.row1}>
            <div style={styles.heading}>{name}</div>
          </div>
        </Paper>
      </div>
    )
  }
}
