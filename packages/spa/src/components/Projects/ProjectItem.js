import React, { PureComponent, PropTypes } from 'react'
import { ListItem } from 'material-ui/List'
import Avatar from 'material-ui/Avatar'
import IconButton from 'material-ui/IconButton'
import IconMenu from 'material-ui/IconMenu'
import MenuItem from 'material-ui/MenuItem'
import Toggle from 'material-ui/Toggle'
import NavigationMoreVertIcon from 'material-ui/svg-icons/navigation/more-vert'
import ActionAssignmentIndIcon from 'material-ui/svg-icons/action/assignment-ind'
import ActionWorkIcon from 'material-ui/svg-icons/action/work'
import ActionThumbsUpDownIcon from 'material-ui/svg-icons/action/thumbs-up-down'
import ActionDoneIcon from 'material-ui/svg-icons/action/done'
import { blueGrey50, grey200 } from 'material-ui/styles/colors'

import { DEFAULT_PHOTO } from '../../constants'
import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'
import { getProjectMetaInfo } from './MetaInfoLine'
import ListCountBadgyThing from './ListCountBadgyThing'

const { string, object, arrayOf } = PropTypes

const styles = {
  listCountBadges: {
    float: 'right',
    marginTop: -4,
  },
  avatar: {
    width: 47,
    height: 37,
    backgroundColor: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundImage: `url('${DEFAULT_PHOTO}')`,
    border: `3px solid ${blueGrey50}`,
  },
}

export default class ProjectItem extends PureComponent {
  static propTypes = {
    name: string,
    id: string,
    listType: string,
    returnTo: string,

    avatar: string,
    type: arrayOf(string),
    usage: arrayOf(string),
    otherUsage: string,
    castings: arrayOf(object).isRequired,
    bookings: arrayOf(object).isRequired,
  };

  static defaultProps = {
    returnTo: null,
  };

  static contextTypes = {
    router: object,
    mediaQueries: object,
  };

  handleItemTouchTap = () => {
    const { id, returnTo } = this.props
    this.context.router.push(`/projects/${id}/${returnTo ? `?returnTo=${returnTo}` : ''}`)
  };

  render() {
    const { mediaQueries: { xs } } = this.context
    const {
      name,
      avatar,
      type,
      usage,
      otherUsage,
      castings,
      bookings,
    } = this.props
    const avatarImage = avatar && avatar.length && getMediaUrl(avatar, 128)
    const avatarStyle = (avatarImage && { backgroundImage: `url('${avatarImage}')` }) || {}

    const baseProps = {
      leftAvatar: <div style={sm(styles.avatar, avatarStyle)} />,
      primaryText: name,
      secondaryText: getProjectMetaInfo({ type, usage, otherUsage }),
    }

    const props = {
      primaryText: (
        <div>
          {name}
          {/* <small>{!approved ? ' — Pending approval' : ''}</small>*/}
          {!xs
            ? <div style={styles.listCountBadges}>
              <ListCountBadgyThing label="Castings" members={castings} />
              <ListCountBadgyThing label="Bookings" members={bookings} />
            </div>
            : null}
        </div>
      ),
      onTouchTap: this.handleItemTouchTap,
    }

    return <ListItem {...baseProps} {...props} />
  }
}
