import React, { PureComponent, PropTypes } from 'react'
import FlatButton from 'material-ui/FlatButton'

import { LIST_MEMBER_STATUS } from '../../constants'
import sm from 'helpers/stylesMerge'
import If from 'components/If'

const { string, object, arrayOf, oneOf } = PropTypes

const styles = {}

export function countMembersByStatus(members = []) {
  return members.reduce(
    (counts, member) => ({ ...counts, [member.status]: counts[member.status] + 1 }),
    {
      ...LIST_MEMBER_STATUS.reduce((statuses, status) => ({ ...statuses, [status]: 0 }), {}),
      total: members.length,
    },
  )
}

export default class ListPills extends PureComponent {
  static propTypes = {
    projectId: string.isRequired,
    listId: string.isRequired,
    type: oneOf(['casting', 'booking']),
    selection: oneOf([false, ...LIST_MEMBER_STATUS]),
    members: arrayOf(object),
    visibility: oneOf(['private', 'public']),
  };

  render() {
    const { projectId, listId, type, selection, members, visibility } = this.props
    const href = `#/projects/${projectId}/${type}/${listId}/`
    const counts = countMembersByStatus(members)

    return (
      <div>
        <FlatButton
          label={`All${counts.total ? ` (${counts.total})` : ''}`}
          disabled={!selection}
          href={href}
        />
        <FlatButton
          label={`Invited${counts.invited ? ` (${counts.invited})` : ''}`}
          disabled={selection === 'invited'}
          href={`${href}invited`}
        />
        <FlatButton
          label={`Confirmed${counts.confirmed ? ` (${counts.confirmed})` : ''}`}
          disabled={selection === 'confirmed'}
          href={`${href}confirmed`}
        />
        <FlatButton
          label={`Declined${counts.declined ? ` (${counts.declined})` : ''}`}
          disabled={selection === 'declined'}
          href={`${href}declined`}
        />
        <FlatButton
          label={`Released${counts.released ? ` (${counts.released})` : ''}`}
          disabled={selection === 'released'}
          href={`${href}released`}
        />
        <If
          if={type === 'booking' && (visibility === 'public' || counts.applied)}
          then={FlatButton}
          label={`Applied${counts.applied ? ` (${counts.applied})` : ''}`}
          disabled={selection === 'applied'}
          href={`${href}applied`}
        />
      </div>
    )
  }
}
