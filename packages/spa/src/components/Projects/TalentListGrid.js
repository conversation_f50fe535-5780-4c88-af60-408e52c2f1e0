import React, { PureComponent, PropTypes } from 'react'
import { connect } from 'react-redux'

import * as projectActions from 'redux/modules/project'
import * as castingActions from 'redux/modules/casting'
import * as bookingActions from 'redux/modules/booking'
import sm from 'helpers/stylesMerge'
import noop from 'helpers/noop'
import AddToListPrompt from 'components/Dialogs/AddToListPrompt'
import ConfirmPrompt from 'components/Dialogs/Confirm'
import TalentListCard from './TalentListCard'

const { bool, string, object, func, arrayOf, oneOf } = PropTypes

const styles = {
  grid: {
    display: 'flex',
    flexWrap: 'wrap',
  },
}

@connect(() => ({}), {
  refreshProject: projectActions.refresh,
  castingAdd: castingActions.addMember,
  bookingAdd: bookingActions.addMember,
})
export default class TalentListGrid extends PureComponent {
  static propTypes = {
    projectId: string.isRequired,
    listId: string.isRequired,
    type: oneOf(['casting', 'booking']).isRequired,
    data: arrayOf(object).isRequired,
    approved: bool.isRequired,
    refreshProject: func.isRequired,
    castingAdd: func.isRequired,
    bookingAdd: func.isRequired,
  };

  state = {
    openAddToListPrompt: false,
    addProfile: {},
    confirmReleaseDialog: {},
  };

  setConfirmReleaseDialog = (title, message, onConfirm = noop) =>
    this.setState({ confirmReleaseDialog: { title, message, onConfirm } });

  handleAddToListPromptOpen = profile =>
    () => this.setState({ addProfile: profile, openAddToListPrompt: true });

  handleAddToListPromptClose = () => this.setState({ openAddToListPrompt: false });

  handleAddToListPromptConfirm = (profileId, listType, listId) => {
    const { refreshProject } = this.props
    const actionKey = `${listType}Add`

    this.props[actionKey](listId, profileId).then(refreshProject)
  };

  handleConfirmReleaseDialog = (confirmed) => {
    if (confirmed) this.state.confirmReleaseDialog.onConfirm()
    this.setState({ confirmReleaseDialog: {} })
  };

  render() {
    const { projectId, type, listId, data, approved } = this.props
    const {
      addProfile,
      openAddToListPrompt,
      confirmReleaseDialog,
    } = this.state

    return (
      <div style={styles.grid}>
        {data.map(item => (
          <TalentListCard
            key={item.id}
            projectId={projectId}
            listId={listId}
            type={type}
            approved={approved}
            onAddToListPromptOpen={this.handleAddToListPromptOpen(item.profile)}
            setConfirmReleaseDialog={this.setConfirmReleaseDialog}
            {...item}
          />
        ))}

        <AddToListPrompt
          open={openAddToListPrompt}
          onAddConfirm={this.handleAddToListPromptConfirm}
          onCloseCancel={this.handleAddToListPromptClose}
          profile={addProfile}
        />

        <ConfirmPrompt
          open={!!confirmReleaseDialog.onConfirm}
          onUserResponse={this.handleConfirmReleaseDialog}
          title={confirmReleaseDialog.title}
          message={confirmReleaseDialog.message}
        />
      </div>
    )
  }
}
