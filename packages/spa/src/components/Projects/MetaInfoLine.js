import React, { PureComponent, PropTypes } from 'react'

const { number, string, arrayOf } = PropTypes

const styles = {}

export const getProjectMetaInfo = ({ type, usage, otherUsage }, limit = 1) =>
  [type.slice(0, limit).join(', '), usage.slice(0, limit).join(', '), otherUsage]
    .filter(item => item && item.length)
    .join(', ')

export default class MetaInfoLine extends PureComponent {
  static propTypes = {
    type: arrayOf(string),
    usage: arrayOf(string),
    otherUsage: string,
    limit: number,
  };

  static defaultProps = {
    type: [],
    usage: [],
    otherUsage: '',
    limit: 1,
  };

  render() {
    return (
      <span>
        {getProjectMetaInfo(this.props, this.props.limit)}
      </span>
    )
  }
}
