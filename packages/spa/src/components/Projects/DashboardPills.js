import React, { PureComponent, PropTypes } from 'react'
import FlatButton from 'material-ui/FlatButton'

import sm from 'helpers/stylesMerge'

const { string, oneOf } = PropTypes

const styles = {}

export default class ProjectDashboardPills extends PureComponent {
  static propTypes = {
    projectId: string.isRequired,
    selection: oneOf([false, 'castings', 'bookings']),
  };

  static defaultProps = {
    selection: false,
  };

  render() {
    const { projectId, selection } = this.props
    const href = `#/projects/${projectId}/`

    return (
      <div>
        <FlatButton label="All" disabled={!selection} href={href} />
        <FlatButton label="Castings" disabled={selection === 'castings'} href={`${href}castings`} />
        <FlatButton label="Bookings" disabled={selection === 'bookings'} href={`${href}bookings`} />
      </div>
    )
  }
}
