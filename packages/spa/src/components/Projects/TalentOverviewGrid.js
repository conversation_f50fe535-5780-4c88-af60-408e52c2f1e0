import React, { PureComponent, PropTypes } from 'react'

import sm from 'helpers/stylesMerge'
import Heading from 'components/Heading/Heading'
import TalentOverviewCard from './TalentOverviewCard'

const { string, object, oneOf } = PropTypes

const styles = {
  wrapper: {
    clear: 'both',
    display: 'flex',
    flexWrap: 'wrap',
    // justifyContent: 'space-between',
  },
  heading: {
    textTransform: 'capitalize',
  },
}

export default class TalentOverviewGrid extends PureComponent {
  static propTypes = {
    projectId: string.isRequired,
    type: oneOf([false, 'castings', 'bookings']),
    project: object.isRequired,
  };

  static defaultProps = {
    type: false,
  };

  render() {
    const { projectId, type, project } = this.props
    const data = type ? project[type] || [] : [...project.castings, ...project.bookings]

    return (
      <div style={styles.wrapper}>
        {data.map(list => (
          <TalentOverviewCard
            key={list.id}
            projectId={projectId}
            type={list.rate ? 'booking' : 'casting'}
            {...list}
          />
        ))}
      </div>
    )
  }
}
