import React, { PureComponent, PropTypes } from 'react'
import { RadioButton, RadioButtonGroup } from 'material-ui/RadioButton'
import { grey500 } from 'material-ui/styles/colors'

import noop from 'helpers/noop'
import sm from 'helpers/stylesMerge'

const { bool, number, string, func, object, arrayOf, oneOfType } = PropTypes

const styles = {
  wrapper: {
    marginTop: 14,
  },
  fullWidth: {
    width: '100%',
  },
  label: {
    color: grey500,
    fontWeight: 300,
    fontSize: '90%',
    lineHeight: '22px',
  },
  fields: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  field: {
    display: 'block',
    width: 'initial',
    minWidth: 178,
  },
}

export default class RadioField extends PureComponent {
  static propTypes = {
    name: string.isRequired,
    label: oneOfType([bool, string]),
    value: oneOfType([bool, number, string]),
    options: arrayOf(object).isRequired,
    onChange: func,
    disabled: bool,
    fullWidth: bool,
  };

  static defaultProps = {
    label: false,
    value: [],
    onChange: noop,
    disabled: true,
    fullWidth: false,
  };

  state = {
    value: this.props.value,
  };

  componentWillReceiveProps(nextProps) {
    const { value } = this.props
    const { value: nextValue } = nextProps

    if (value !== nextValue) {
      this.setState({ value: nextValue })
    }
  }

  handleChange = (event, value) => this.setState(() => {
    this.props.onChange(value)
    return { value }
  });

  render() {
    const { name, fullWidth, disabled, label, options } = this.props
    const { value } = this.state

    return (
      <div style={sm(styles.wrapper, fullWidth && styles.fullWidth)}>
        {label ? <div style={styles.label}>{label}</div> : null}

        <RadioButtonGroup
          name={name}
          valueSelected={value}
          onChange={this.handleChange}
          style={styles.fields}
        >
          {options.map(option => (
            <RadioButton
              key={option.value}
              disabled={disabled}
              label={option.label}
              style={styles.field}
              value={option.value}
            />
          ))}
        </RadioButtonGroup>
      </div>
    )
  }
}
