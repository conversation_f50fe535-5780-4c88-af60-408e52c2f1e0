import React, { PureComponent, PropTypes } from 'react'
import Checkbox from 'material-ui/CheckBox'
import { grey500 } from 'material-ui/styles/colors'

import noop from 'helpers/noop'
import sm from 'helpers/stylesMerge'

const { bool, number, string, func, object, arrayOf, oneOfType } = PropTypes

const styles = {
  wrapper: {
    marginTop: 14,
  },
  fullWidth: {
    width: '100%',
  },
  label: {
    color: grey500,
    fontWeight: 300,
    fontSize: '90%',
    lineHeight: '22px',
  },
  fields: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  field: {
    display: 'block',
    width: 'initial',
    minWidth: 178,
  },
}

export default class SetField extends PureComponent {
  static propTypes = {
    label: oneOfType([bool, string]),
    value: arrayOf(oneOfType([bool, number, string])),
    options: arrayOf(object).isRequired,
    onChange: func,
    disabled: bool,
    fullWidth: bool,
  };

  static defaultProps = {
    label: false,
    value: [],
    onChange: noop,
    disabled: true,
    fullWidth: false,
  };

  state = {
    value: this.props.value,
  };

  componentWillReceiveProps(nextProps) {
    const { value } = this.props
    const { value: nextValue } = nextProps

    if (value !== nextValue) {
      this.setState({ value: nextValue })
    }
  }

  handleCheck = optionValue => (event, checked) => this.setState((state) => {
    const value = checked
      ? [...state.value, optionValue]
      : state.value.filter(item => item !== optionValue)

    this.props.onChange(value)
    return { value }
  });

  handleClearField = () => {
    this.setState({ value: [] })
    this.props.onChange([])
  };

  render() {
    const { fullWidth, disabled, label, options } = this.props
    const { value } = this.state

    return (
      <div style={sm(styles.wrapper, fullWidth && styles.fullWidth)}>
        {label ? <div style={styles.label}>{label}</div> : null}

        <div style={styles.fields}>
          {options.map(option => (
            <Checkbox
              key={option.value}
              disabled={disabled}
              label={option.label}
              checked={value.includes(option.value)}
              style={styles.field}
              onCheck={this.handleCheck(option.value)}
            />
          ))}
        </div>
      </div>
    )
  }
}
