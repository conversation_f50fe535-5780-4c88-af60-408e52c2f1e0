import React, { PureComponent, PropTypes } from 'react'
import IconButton from 'material-ui/IconButton'
import ActionDeleteIcon from 'material-ui/svg-icons/action/delete'
import { grey500 } from 'material-ui/styles/colors'

import noop from 'helpers/noop'
import sm from 'helpers/stylesMerge'
import <PERSON>Field from './EventField'

const { bool, number, string, object, func, arrayOf, oneOf, oneOfType } = PropTypes

const styles = {
  wrapper: {
    marginTop: 14,
  },
  fullWidth: {
    width: '100%',
  },
  label: {
    color: grey500,
    fontWeight: 300,
    fontSize: '90%',
    lineHeight: '22px',
  },
  fields: {},
  field: {
    display: 'flex',
    justifyContent: 'flex-start',
  },
  remove: {
    marginTop: 24,
  },
}

const Field = ({ type, disabled, label, position, value, onChange, ...rest }) => {
  switch (type) {
    case 'event':
    default:
      return (
        <EventField
          disabled={disabled}
          label={`${label || 'Date'} ${position}`}
          value={value}
          onChange={onChange}
          clearable={false}
          fullWidth
          {...rest}
        />
      )
  }
}

export default class ListField extends PureComponent {
  static propTypes = {
    of: oneOf(['event']),
    label: oneOfType([bool, string]),
    value: arrayOf([number, string, object]),
    disabled: bool,
    onChange: func,
    max: number,
    fullWidth: bool,
  };

  static defaultProps = {
    of: 'event',
    value: [],
    label: false,
    disabled: false,
    onChange: noop,
    max: 10,
    fullWidth: false,
  };

  state = {
    value: this.props.value,
  };

  componentWillReceiveProps(nextProps) {
    const { value } = this.props
    const { value: nextValue } = nextProps

    if (nextValue.length && value !== nextValue) {
      this.setState({ value: nextValue })
    }
  }

  handleChange = index => newValue => this.setState((state) => {
    const value = [...state.value]
    value[index] = newValue

    this.props.onChange(value)
    return { value }
  });

  handleRemoveField = index => () => this.setState((state) => {
    const value = [...state.value]
    value.splice(index, 1)

    this.props.onChange(value)
    return { value }
  });

  render() {
    const { fullWidth, of: type, disabled, max, label, ...rest } = this.props
    const { value } = this.state

    return (
      <div style={sm(styles.wrapper, fullWidth && styles.fullWidth)}>
        {label ? <div style={styles.label}>{label}</div> : null}

        <div style={styles.fields}>
          {value.map((item, index) => (
            <div style={styles.field}>
              <Field
                {...rest}
                type={type}
                disabled={disabled}
                position={index + 1}
                value={item}
                onChange={this.handleChange(index)}
              />
              <IconButton touch onTouchTap={this.handleRemoveField(index)} style={styles.remove}>
                <ActionDeleteIcon color={grey500} />
              </IconButton>
            </div>
          ))}
          {!disabled && value.length < max
            ? <div style={styles.field}>
              {React.cloneElement(<Field />, {
                ...rest,
                type,
                disabled: false,
                position: value.length + 1,
                value: {},
                onChange: this.handleChange(value.length),
              })}
              <IconButton disabled />
            </div>
            : null}
        </div>
      </div>
    )
  }
}
