import React, { PropTypes } from 'react'
import { white } from 'material-ui/styles/colors'

import sm from 'helpers/stylesMerge'
import Heading from 'components/Heading/Heading'
import Fieldset from './Fieldset'
import Field from './Field'

const { bool, string, object, node } = PropTypes

const divStyle = {
  backgroundColor: white,
  marginTop: 24,
  padding: '1em',
}

// todo: make this a pure component
export default function Form(
  {
    layout = true,
    legend = '',
    style,
    fieldSetStyle = {},
    fieldStyle = {},
    action,
    shouldDispatch = true,
    data,
    children,
  },
  { store: { dispatch } },
) {
  const fieldset = React.Children.map(children, (child) => {
    if (!child || child.type !== Field) return child

    const { name, onChange } = child.props

    if (data && child.type === Field && name) {
      const value = data[name]
      const handleFieldChange = (newValue) => {
        onChange()

        return shouldDispatch
          ? dispatch(action({ id: data.id, [name]: newValue }))
          : action({ id: data.id, [name]: newValue })
      }

      return React.cloneElement(child, {
        name,
        value,
        layout,
        style: fieldStyle,
        ...child.props,
        onChange: handleFieldChange,
      })
    }

    return child
  })

  const wrapStyle = layout ? divStyle : {}

  return (
    <div style={style ? sm(wrapStyle, style) : wrapStyle}>
      {legend ? <Heading text={legend} size={1.25} weight={400} /> : ''}

      <Fieldset layout={layout} style={fieldSetStyle}>
        {fieldset}
      </Fieldset>
    </div>
  )
}

Form.propTypes = {
  layout: bool,
  legend: string,
  namespace: string,
  data: object,
  children: node.isRequired,
}

Form.contextTypes = {
  store: object,
}
