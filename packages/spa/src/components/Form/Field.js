import React, { PropTypes } from 'react'
import emptyFunction from 'fbjs/lib/emptyFunction'
import TextField from 'material-ui/TextField'
import SelectField from 'material-ui/SelectField'
import MenuItem from 'material-ui/MenuItem'
import Toggle from 'material-ui/Toggle'
import DatePicker from 'material-ui/DatePicker'
import TimePicker from 'material-ui/TimePicker'
import Checkbox from 'material-ui/CheckBox'

import sm from 'helpers/stylesMerge'
import { convertUnits } from 'helpers/measurement'
import DateTimeField from 'components/DateTimeField/DateTimeField'
import SetField from './SetField'
import RadioField from './RadioField'
import EventField from './EventField'
import ListField from './ListField'

const { bool, number, string, array, object, func, oneOf, oneOfType } = PropTypes

const getStyle = (fullWidth = false) => ({ width: fullWidth ? '100%' : 'calc(50% - 1em)' })

const inputStyle = {
  color: 'inherit',
}

const underlineDisabledStyle = {
  display: 'none',
}

const cleanValue = (type, event, valueOne, valueTwo) => {
  switch (type) {
    case 'number':
      return Number(event.target.value)
    case 'toggle':
      return valueOne
    case 'checkbox':
      return valueOne
    case 'select':
      return valueTwo
    case 'date':
    case 'time':
      return valueOne
    case 'datetime':
    case 'set':
    case 'radio':
    case 'event':
    case 'list':
      return event
    case 'file':
      return event.target.files
    default:
      return event.target.value
  }
}

// todo: make this a pure component
export default function Field(
  {
    id,
    name,
    layout = true,
    type = 'text',
    mutable = false,
    dataUnits = false,
    displayUnits = false,
    label,
    value: _value,
    onChange,
    options = [],
    fullWidth = false,
    rows = 10,
    accept = false,
    style = {},
    ...otherProps
  },
) {
  const fieldStyle = sm((layout || fullWidth) && getStyle(fullWidth), style)
  let value = _value

  if (dataUnits && displayUnits) {
    value = convertUnits(dataUnits, displayUnits, value)
  }

  const handleChange = (event, valueOne, valueTwo) => {
    let clean = cleanValue(type, event, valueOne, valueTwo)

    if (dataUnits && displayUnits) {
      clean = convertUnits(displayUnits, dataUnits, clean)
    }

    if (value !== clean) onChange(clean)
  }

  switch (type) {
    case 'select':
      return (
        <SelectField
          disabled={!mutable}
          underlineDisabledStyle={underlineDisabledStyle}
          value={value}
          floatingLabelText={label}
          style={fieldStyle}
          onChange={handleChange}
          {...otherProps}
        >
          {options.map(option => (
            <MenuItem value={option.value} primaryText={option.label} key={option.value} />
          ))}
        </SelectField>
      )

    case 'toggle':
      return (
        <Toggle
          disabled={!mutable}
          label={label}
          defaultToggled={value}
          style={fieldStyle}
          onToggle={handleChange}
          {...otherProps}
        />
      )

    case 'checkbox':
      return (
        <Checkbox
          disabled={!mutable}
          label={label}
          defaultChecked={value}
          style={fieldStyle}
          onCheck={handleChange}
          {...otherProps}
        />
      )

    case 'date':
      return (
        <DatePicker
          disabled={!mutable}
          underlineDisabledStyle={underlineDisabledStyle}
          style={fieldStyle}
          inputStyle={inputStyle}
          hintText={label}
          floatingLabelText={label}
          value={typeof value === 'string' ? new Date(value) : value}
          onChange={handleChange}
          {...otherProps}
        />
      )

    case 'time':
      return (
        <TimePicker
          format="24hr"
          disabled={!mutable}
          underlineDisabledStyle={underlineDisabledStyle}
          style={fieldStyle}
          inputStyle={inputStyle}
          hintText={label}
          floatingLabelText={label}
          value={typeof value === 'string' ? new Date(value) : value}
          onChange={handleChange}
          {...otherProps}
        />
      )

    case 'datetime':
      return (
        <DateTimeField
          disabled={!mutable}
          label={label}
          onChange={handleChange}
          value={typeof value === 'string' ? new Date(value) : value}
          {...otherProps}
        />
      )

    case 'set':
      return (
        <SetField
          disabled={!mutable}
          label={label}
          value={value}
          options={options}
          onChange={handleChange}
          fullWidth={fullWidth}
          {...otherProps}
        />
      )

    case 'radio':
      return (
        <RadioField
          name={name}
          disabled={!mutable}
          label={label}
          value={value}
          options={options}
          onChange={handleChange}
          fullWidth={fullWidth}
          {...otherProps}
        />
      )

    case 'event':
      return (
        <EventField
          disabled={!mutable}
          label={label}
          value={value}
          onChange={handleChange}
          fullWidth={fullWidth}
          {...otherProps}
        />
      )

    case 'list':
      return (
        <ListField
          disabled={!mutable}
          label={label}
          value={value}
          onChange={handleChange}
          fullWidth={fullWidth}
          {...otherProps}
        />
      )

    case 'textarea':
      return (
        <TextField
          disabled={!mutable}
          underlineDisabledStyle={underlineDisabledStyle}
          style={fieldStyle}
          inputStyle={inputStyle}
          hintText={label}
          floatingLabelText={label}
          defaultValue={value}
          multiLine
          rows={rows}
          onBlur={handleChange}
          {...otherProps}
        />
      )

    case 'file':
      return (
        <input
          type="file"
          disabled={!mutable}
          style={fieldStyle}
          onChange={handleChange}
          accept={accept}
          id={id}
          {...otherProps}
        />
      )

    default:
      return (
        <TextField
          disabled={!mutable}
          underlineDisabledStyle={underlineDisabledStyle}
          style={fieldStyle}
          inputStyle={inputStyle}
          hintText={label}
          floatingLabelText={label}
          defaultValue={value}
          onBlur={handleChange}
          type={type}
          {...otherProps}
        />
      )
  }
}

Field.propTypes = {
  layout: bool,
  type: oneOf([
    'select',
    'toggle',
    'checkbox',
    'text',
    'textarea',
    'date',
    'time',
    'datetime',
    'number',
    'file',
    'set',
    'radio',
    'event',
    'list',
  ]),
  mutable: bool,
  label: string,
  value: oneOfType([bool, number, string]),
  options: array,
  onChange: func,
  accept: string,
  style: object,
}

Field.defaultProps = {
  onChange: emptyFunction,
  type: 'text',
}
