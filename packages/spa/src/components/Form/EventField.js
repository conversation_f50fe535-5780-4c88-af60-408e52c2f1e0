import React, { PureComponent, PropTypes } from 'react'
import Sugar from 'sugar-date' // meh.
import DatePicker from 'material-ui/DatePicker'
import TimePicker from 'material-ui/TimePicker'
import TextField from 'material-ui/TextField'
import IconButton from 'material-ui/IconButton'
import EditorFormatClearIcon from 'material-ui/svg-icons/editor/format-clear'
import { grey200, grey300, grey500 } from 'material-ui/styles/colors'

import noop from 'helpers/noop'
import sm from 'helpers/stylesMerge'

const { bool, number, string, object, func, oneOfType, shape } = PropTypes

const styles = {
  wrapper: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  fullWidth: {
    width: '100%',
  },
  field: {
    flex: 1,
  },
  clear: {
    marginTop: 24,
  },
}

const cleanValue = (value) => {
  const start = value.start && typeof value.start !== 'object'
    ? new Date(value.start)
    : value.start
  const end = value.end && typeof value.end !== 'object' ? new Date(value.end) : value.end

  return { start, end }
}

export default class EventField extends PureComponent {
  static propTypes = {
    label: oneOfType([bool, string]),
    value: shape({
      start: oneOfType([number, string, object]),
      end: oneOfType([number, string, object]),
    }),
    onChange: func,
    showDuration: bool,
    clearable: bool,
    fullWidth: bool,
    // multiday: bool,  // TODO: support multiple days (with two Date fields, date-start, date-end)
  };

  static defaultProps = {
    value: { start: null, end: null },
    label: 'Date',
    onChange: noop,
    showDuration: true,
    clearable: true,
    fullWidth: false,
  };

  state = {
    value: cleanValue(this.props.value),
  };

  componentWillReceiveProps(nextProps) {
    const { value } = this.props
    const { value: nextValue } = nextProps

    if (value !== nextValue) {
      this.setState({ value: cleanValue(nextValue) })
    }
  }

  handleDateChange = key => (event, newValue) => this.setState(({ value: { start, end } }) => {
    const value = { start, end, [key]: newValue }

    if (key === 'start') {
      if (!end) value.end = Sugar.Date.addHours(new Date(newValue), 3)
      else value.end = Sugar.Date.addHours(new Date(newValue), Sugar.Date.hoursUntil(start, end))
    } else if (!start) value.start = Sugar.Date.reset(new Date(newValue))

    this.props.onChange(value)
    return { value }
  });

  handleClearFields = () => {
    this.setState({ value: { start: null, end: null } })
    this.props.onChange(undefined)
  };

  render() {
    const { label, fullWidth, showDuration, clearable } = this.props
    const { value: { start, end } } = this.state

    const duration = start && end ? Sugar.Date.relativeTo(start, end) : ''

    return (
      <div style={sm(styles.wrapper, fullWidth && styles.fullWidth)}>
        <DatePicker
          DateTimeFormat={Intl.DateTimeFormat}
          hintText={label}
          floatingLabelText={label}
          value={start}
          onChange={this.handleDateChange('start')}
          fullWidth
          style={styles.field}
        />
        <TimePicker
          format="24hr"
          hintText="Time start"
          floatingLabelText="Time start"
          pedantic
          value={start}
          onChange={this.handleDateChange('start')}
          fullWidth
          style={styles.field}
        />
        <TimePicker
          format="24hr"
          hintText="Time end"
          floatingLabelText="Time end"
          pedantic
          value={end}
          onChange={this.handleDateChange('end')}
          fullWidth
          style={styles.field}
        />
        {showDuration
          ? <TextField
            disabled
            hintText="Duration"
            floatingLabelText="Duration"
            value={duration}
            type="text"
            fullWidth
            style={styles.field}
          />
          : null}
        {clearable
          ? <IconButton touch onTouchTap={this.handleClearFields} style={styles.clear}>
            <EditorFormatClearIcon color={start || end ? grey500 : grey200} />
          </IconButton>
          : null}
      </div>
    )
  }
}
