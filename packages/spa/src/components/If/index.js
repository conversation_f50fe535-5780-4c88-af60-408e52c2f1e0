import React, { PropTypes } from 'react'

const { any, node, oneOfType, string, array, bool } = PropTypes

/**
 * Logic-only component for simplified conditional statements
 * By default renders a <div> and passes all supplied properties to it,
 * including children
 * Change the default element by supplying
 *   then={React.Component} - component to render if condition true
 *   else={React.Component} - component to render if condition false
 *
 *
 * Examples
 *    <If if={true}>It is truthful</If>
 *    <If if={true} className={styles.success}>It is truthful</If>
 *    <If if={true} then={Text}>It is truthful</If>
 *    <If if={true} then={SuccessComponent} else={FailureComponent} />
 *    <If if={true} then={ProductGrid} data={{ foo: 1 }} />
 */
export default function If(props) {
  const { children, ...other } = props
  const Component = props.then || 'div'
  const ElseComponent = props.else
  delete other.if
  delete other.then
  delete other.else
  const elseReturn = ElseComponent ? <ElseComponent {...other}>{children}</ElseComponent> : null
  const thenReturn = children ? <Component {...other}>{children}</Component> : <Component {...other} />
  return props.if ? thenReturn : elseReturn
}

// Using reserved names as properties so we are quoting them
// jscs:disable disallowQuotedKeysInObjects
/* eslint-disable quote-props */
If.propTypes = {
  'if': bool,
  'then': any,
  'else': any,
  'children': oneOfType([node, string, array]),
}

// jscs:enable disallowQuotedKeysInObjects
/* eslint-enable quote-props */
