import React from 'react'
import PropTypes from 'prop-types'
import CircularProgress from 'material-ui/CircularProgress'

import theme from 'theme/mui-theme'
import sm from 'helpers/stylesMerge'

const { number, object } = PropTypes

const { primary2Color } = theme.palette

const wrapperStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100%',
  width: '100%',
  textAlign: 'center',
}

export default function UploadProgress({ progress, style }, { mediaQueries: { xs } }) {
  if (progress < 100) {
    return (
      <div style={sm(wrapperStyle, style)}>
        <div>
          <CircularProgress
            mode="determinate"
            size={80}
            thickness={5}
            color={primary2Color}
            max={100}
            value={progress}
          />
          <div>{progress.toFixed(0)}%</div>
        </div>
      </div>
    )
  }

  return (
    <div style={sm(wrapperStyle, style)}>
      <CircularProgress mode="indeterminate" size={80} thickness={5} color={primary2Color} />
    </div>
  )
}

UploadProgress.propTypes = {
  progress: number.isRequired,
  style: object,
}

UploadProgress.defaultProps = {
  style: {},
}

UploadProgress.contextTypes = {
  mediaQueries: object,
}
