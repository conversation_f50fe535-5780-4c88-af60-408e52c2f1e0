import React from 'react'
import PropTypes from 'prop-types'
import CircularProgress from 'material-ui/CircularProgress'

import theme from 'theme/mui-theme'
import Noop from 'helpers/noop'
import sm from 'helpers/stylesMerge'

const { bool, object } = PropTypes

const { accent3Color, primary2Color } = theme.palette

const wrapperStyle = {
  display: 'flex',
  justifyContent: 'space-around',
  alignItems: 'center',
  height: 'calc(100vh - 64px)',
}

export default function Progress({ show, style }) {
  if (show) {
    return (
      <div style={sm(wrapperStyle, style)}>
        <CircularProgress size={80} thickness={5} color={primary2Color} />
      </div>
    )
  }

  return Noop
}

Progress.propTypes = {
  show: bool.isRequired,
  style: object,
}

Progress.defaultProps = {
  show: false,
  style: {},
}
