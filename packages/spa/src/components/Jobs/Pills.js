import React, { PureComponent, PropTypes } from 'react'
import FlatButton from 'material-ui/FlatButton'

import { LIST_MEMBER_STATUS } from '../../constants'

const { object, oneOf, arrayOf } = PropTypes

export function countJobsByStatus(jobs = []) {
  return jobs.reduce(
    (counts, job) => {
      const status = job.membership ? job.membership.status : 'not-applied'
      return { ...counts, [status]: counts[status] + 1 }
    },
    {
      ...LIST_MEMBER_STATUS.reduce((statuses, status) => ({ ...statuses, [status]: 0 }), {}),
      'not-applied': 0,
      total: jobs.length,
    },
  )
}

export default class JobPills extends PureComponent {
  static propTypes = {
    type: oneOf(['my-jobs', 'jobs']).isRequired,
    selection: oneOf([false, 'not-applied', ...LIST_MEMBER_STATUS]),
    jobs: arrayOf(object),
  }

  static defaultProps = {
    selection: false,
    jobs: [],
  }

  render() {
    const { type, selection, jobs } = this.props
    const href = `#/${type}/`
    const counts = countJobsByStatus(jobs)

    if (type === 'my-jobs') {
      return (
        <div>
          <FlatButton
            label={`All${counts.total ? ` (${counts.total})` : ''}`}
            disabled={!selection}
            href={href}
          />
          <FlatButton
            label={`Confirmed${counts.confirmed ? ` (${counts.confirmed})` : ''}`}
            disabled={selection === 'confirmed'}
            href={`${href}confirmed`}
          />
          <FlatButton
            label={`Declined${counts.declined ? ` (${counts.declined})` : ''}`}
            disabled={selection === 'declined'}
            href={`${href}declined`}
          />
          <FlatButton
            label={`Released${counts.released ? ` (${counts.released})` : ''}`}
            disabled={selection === 'released'}
            href={`${href}released`}
          />
        </div>
      )
    }

    return (
      <div>
        <FlatButton
          label={`All${counts.total ? ` (${counts.total})` : ''}`}
          disabled={!selection}
          href={href}
        />
        <FlatButton
          label={`Applied${counts.applied ? ` (${counts.applied})` : ''}`}
          disabled={selection === 'applied'}
          href={`${href}applied`}
        />
        {type === 'jobs'
          ? <FlatButton
            label={`Not Applied${counts['not-applied'] ? ` (${counts['not-applied']})` : ''}`}
            disabled={selection === 'not-applied'}
            href={`${href}not-applied`}
          />
          : null}
      </div>
    )
  }
}
