import React, { Component, PropTypes } from 'react'
import Sugar from 'sugar-date'
import { ListItem } from 'material-ui/List'
import { blueGrey50, grey200 } from 'material-ui/styles/colors'

import { DEFAULT_PHOTO, JOB_STATUS_TEXT_MAP, JOB_CASTING_STATUS_TEXT_MAP } from '../../constants'
import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'

const { bool, string, object, arrayOf, shape } = PropTypes

const styles = {
  content: {
    display: 'flex',
  },
  inner: {
    flex: 1,
  },
  right: {
    textAlign: 'right',
  },
  avatar: {
    width: 47,
    height: 37,
    backgroundColor: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundImage: `url('${DEFAULT_PHOTO}')`,
    border: `3px solid ${blueGrey50}`,
  },
  faded: {
    opacity: 0.6,
    filter: 'grayscale(50%)',
  },
}

const JobMembershipText = ({ status }) => <span>{JOB_STATUS_TEXT_MAP[status]}</span>

const CastingMembershipText = ({ name, location, dates = [], membership: { status } }) => (
  <div>
    <strong>Casting {name}: </strong>
    {[location, dates.length ? Sugar.Date.format(new Date(dates[0].start), '{full}') : false]
      .filter(item => !!item)
      .join(',')}
    <div>{JOB_CASTING_STATUS_TEXT_MAP[status]}</div>
  </div>
)

export default class JobItem extends Component {
  static propTypes = {
    publicCard: bool.isRequired,
    returnTo: string,

    id: string,
    name: string,
    projectName: string,
    avatar: string,
    membership: shape({ id: string, status: string, profile: string }),
    castings: arrayOf(object),
  }

  static defaultProps = {
    returnTo: null,
  }

  static contextTypes = {
    router: object,
  }

  state = {
    displayAlertDialog: false,
  }

  render() {
    const { publicCard, id, returnTo, name, projectName, avatar, membership, castings } = this.props
    const status = membership && membership.status

    const avatarImage = avatar && avatar.length && getMediaUrl(avatar, 128)
    const avatarStyle = (avatarImage && { backgroundImage: `url('${avatarImage}')` }) || {}

    const itemProps = {
      style: sm(['applied', 'declined', 'released'].includes(status) && styles.faded),
      leftAvatar: <div style={sm(styles.avatar, avatarStyle)} />,
      primaryText: (
        <div style={styles.content}>
          <div style={styles.inner}>{projectName}: {name}</div>

        </div>
      ),
      secondaryText: (
        <div>
          {membership
            ? <JobMembershipText {...membership} />
            : castings &&
                castings.map(casting => <CastingMembershipText key={casting.id} {...casting} />)}

        </div>
      ),
      secondaryTextLines: 2,
      href: `#/job/${id}/${returnTo ? `?returnTo=${returnTo}` : `${!publicCard ? '?returnTo=/my-jobs' : ''}`}`,
      //href: `#/job/${id}/${publicCard ? '?returnTo=/my-jobs' : `${returnTo ? `?returnTo=${returnTo}` : ''}`}`,
    }

    return <ListItem {...itemProps} />
  }
}
