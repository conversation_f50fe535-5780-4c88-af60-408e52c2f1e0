import React, { PureComponent, PropTypes } from 'react'
import { connect } from 'react-redux'
import Sugar from 'sugar-date'
import { Card, CardActions, CardMedia, CardTitle, CardText } from 'material-ui/Card'
import FlatButton from 'material-ui/FlatButton'
import { white, grey400, grey800 } from 'material-ui/styles/colors'

import { DEFAULT_PHOTO, JOB_STATUS_TEXT_MAP, JOB_CASTING_STATUS_TEXT_MAP } from '../../constants'
import * as confirmPromptActions from 'redux/modules/confirmPrompt'
import * as jobActions from 'redux/modules/job'
import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import { getProjectMetaInfo } from '../Projects/MetaInfoLine'

const { bool, string, object, func, arrayOf, shape } = PropTypes

const styles = {
  card: {
    width: '100%',
    margin: 12,
    maxWidth: 375,
  },
  cardSm: {
    margin: 16,
    maxWidth: 348,
    flex: 1,
    minWidth: 293,
  },
  cardActions: {
    // position: 'absolute',
    bottom: 0,
  },
  cardMedia: {
    height: 200,
    display: 'flex',
    width: '100%',
    backgroundColor: grey400,
  },
  cardMediaSm: {
    height: 200,
  },
  projectTitle: {
    padding: '8px 16px',
    backgroundColor: grey800,
  },
  jobTitle: {
    padding: '0 16px',
  },
  jobTitleSpan: {
    fontSize: 18,
  },
  content: {
    display: 'flex',
  },
  inner: {
    flex: 1,
  },
  right: {
    textAlign: 'right',
  },
  ul: {
    margin: 0,
    padding: 0,
    listStyle: 'none',
  },
  faded: {
    opacity: 0.6,
    filter: 'grayscale(50%)',
  },
}

const getJobMembershipText = status => JOB_STATUS_TEXT_MAP[status]
const getCastingMembershipText = status => JOB_CASTING_STATUS_TEXT_MAP[status]

@connect(
  state => ({
    account: state.account.data,
  }),
  {
    confirmJob: jobActions.confirm,
    declineJob: jobActions.decline,
    applyForJob: jobActions.applyFor,
    confirmCasting: jobActions.confirmCasting,
    declineCasting: jobActions.declineCasting,
    openConfirmPrompt: confirmPromptActions.open,
    closeConfirmPrompt: confirmPromptActions.close,
  },
)
export default class JobCard extends PureComponent {
  static propTypes = {
    publicCard: bool.isRequired,
    account: object.isRequired,
    id: string,
    name: string,
    projectName: string,
    avatar: string,
    membership: shape({ id: string, status: string, profile: string }),
    castings: arrayOf(object),
    casting: bool,
    refresh: func.isRequired,
    confirmJob: func.isRequired,
    declineJob: func.isRequired,
    applyForJob: func.isRequired,
    confirmCasting: func.isRequired,
    declineCasting: func.isRequired,
    openConfirmPrompt: func.isRequired,
    closeConfirmPrompt: func.isRequired,
  }

  static contextTypes = {
    router: object,
    mediaQueries: object,
  }

  static defaultProps = {
    casting: false,
  }

  state = {
    displayAlertDialog: false,
  }

  handleConfirmButton = () => {
    const { refresh, confirmJob, confirmCasting, casting, id, membership } = this.props
    const membershipId = membership.id

    if (casting) return confirmCasting(id, membershipId).then(refresh)
    return confirmJob(id, membershipId).then(refresh)
  }

  handleDeclineButton = () => {
    const {
      refresh,
      openConfirmPrompt,
      closeConfirmPrompt,
      declineJob,
      declineCasting,
      casting,
      id,
      membership,
    } = this.props
    const membershipId = membership.id

    if (casting) return declineCasting(id, membershipId).then(refresh)

    return openConfirmPrompt(
      casting ? 'Decline casting?' : 'Decline job?',
      `Are you sure you want to decline this ${casting ? 'casting' : 'job'}? This cannot be undone.`,
      (confirmed) => {
        closeConfirmPrompt()

        if (confirmed) {
          declineJob(id, membershipId).then(refresh)
        }
      },
    )
  }

  handleApplyForButton = () => {
    const { refresh, account, id, applyForJob } = this.props
    const profileId = account && account.profiles[0] && account.profiles[0].id

    if (profileId) {
      applyForJob(id, profileId).then(refresh)
    }
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const {
      publicCard,
      id,
      name,
      projectName,
      avatar,
      membership,
      castings = [],
      rateType,
      rate,
      currency,
      travel,
      objectives,
      type,
      usage,
      otherUsage,
      location,
      notes,
      dates = [],
      casting,
      job = {},
    } = this.props
    const status = membership && membership.status
    const jobId = casting ? job.id : id

    return (
      <Card
        style={sm(
          styles.card,
          !xs && styles.cardSm,
          ['applied', 'declined', 'released'].includes(status) && styles.faded,
        )}
        containerStyle={{
          position: 'relative',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <a href={`#/job/${jobId}/${!publicCard ? '?returnTo=/my-jobs' : ''}`}>
          <CardMedia
            style={{ overflow: 'hidden' }}
            mediaStyle={sm(styles.cardMedia, !xs && styles.cardMediaSm)}
          >
            <img
              src={avatar ? getMediaUrl(avatar, 512) : DEFAULT_PHOTO}
              alt={projectName}
              style={{ objectFit: 'cover', objectPosition: '50% 50%' }}
            />
          </CardMedia>
        </a>
        <CardTitle style={styles.projectTitle} title={projectName} titleColor={white} />
        <CardTitle
          style={styles.jobTitle}
          titleStyle={styles.jobTitleSpan}
          title={
            <a
              href={`#/job/${jobId}/${casting ? `casting/${id}/` : ''}${!publicCard ? '?returnTo=/my-jobs' : ''}`}
            >
              {casting ? 'Casting' : 'Job'}: {name}
            </a>
          }
        />
        <CardText style={{ padding: '8px 16px' }}>
          <ul style={styles.ul}>
            <li><strong>{casting ? 'Casting' : 'Job'} Location</strong>: {location}</li>
            <li>
              <strong>Usage / Type</strong>: {getProjectMetaInfo({ type, usage, otherUsage }, 5)}
            </li>
            <li><strong>Looking for</strong>: {objectives.join(', ')}</li>
            <li><strong>Rate</strong>: {currency.toUpperCase()} {rate} / {rateType}</li>
            <li>
              <strong>Date</strong>: {dates.map(date => (
                <span key={`${date.start}-${date.end}`}>
                  {Sugar.Date.format(new Date(date.start), '{full}')}
                  <br />
                </span>
              ))}
            </li>
          </ul>
        </CardText>
        <div
          style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'flex-end' }}
        >
          <CardText>
            {!casting && membership && getJobMembershipText(status)}
            {casting && getCastingMembershipText(status)}
            &nbsp;
          </CardText>

          {!publicCard && !casting && membership && status === 'invited'
            ? <CardActions style={styles.cardActions}>
              <FlatButton
                primary={status === 'confirmed'}
                disabled={status === 'confirmed'}
                label="Confirm"
                onTouchTap={this.handleConfirmButton}
              />
              <FlatButton
                primary={status === 'declined'}
                disabled={status === 'declined'}
                label="Decline"
                onTouchTap={this.handleDeclineButton}
              />
            </CardActions>
            : null}

          {!publicCard && !casting && membership && status === 'confirmed'
            ? <CardActions style={styles.cardActions}>
              <FlatButton label="Cancel this job" onTouchTap={this.handleDeclineButton} />
            </CardActions>
            : null}

          {casting && status !== 'released'
            ? <CardActions style={styles.cardActions}>
              <FlatButton
                primary={status === 'confirmed'}
                disabled={status === 'confirmed'}
                label="Confirm"
                onTouchTap={this.handleConfirmButton}
              />
              <FlatButton
                primary={status === 'declined'}
                disabled={status === 'declined'}
                label="Decline"
                onTouchTap={this.handleDeclineButton}
              />
            </CardActions>
            : null}

          {!membership && !casting && !['released'].includes(status)
            ? <CardActions style={styles.cardActions}>
              <FlatButton
                primary={status === 'applied'}
                disabled={status === 'applied'}
                label="Apply for this job"
                onTouchTap={this.handleApplyForButton}
              />
            </CardActions>
            : ''}
        </div>
      </Card>
    )
  }
}
