import React, { PureComponent, PropTypes } from 'react'
import Sugar from 'sugar-date'
import { connect } from 'react-redux'
import RaisedButton from 'material-ui/RaisedButton'

import { JOB_CASTING_STATUS_TEXT_MAP } from '../../constants'
import * as jobActions from 'redux/modules/job'
import sm from 'helpers/stylesMerge'
import Heading from 'components/Heading/Heading'

const { bool, string, object, func, arrayOf } = PropTypes

const styles = {
  wrapper: {},
  heading: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    textTransform: 'capitalize',
  },
  confirmDecline: {
    margin: '1em',
  },
  statusText: {
    margin: '1em',
  },
  ul: {
    margin: 0,
    padding: 0,
    listStyle: 'none',
  },
}

@connect(state => ({}), {
  confirm: jobActions.confirmCasting,
  decline: jobActions.declineCasting,
})
export default class JobCastingDetails extends PureComponent {
  static propTypes = {
    id: string,
    name: string,
    location: string,
    notes: string,
    dates: arrayOf(object),
    membership: object.isRequired,
    confirm: func.isRequired,
    decline: func.isRequired,
  };

  static contextTypes = {
    mediaQueries: object,
  };

  static defaultProps = {
    dates: [],
  };

  handleConfirmButton = () => {
    const { id, membership: { id: membershipId }, confirm } = this.props

    confirm(id, membershipId)
  };

  handleDeclineButton = () => {
    const { id, membership: { id: membershipId }, decline } = this.props

    decline(id, membershipId)
  };

  render() {
    const { mediaQueries: { xs } } = this.context
    const { name, location, notes, dates, membership: { status } } = this.props

    return (
      <div style={styles.wrapper}>
        <Heading text={`Casting: ${name}`} weight={400} size={1.25} style={styles.heading} />

        <ul style={styles.ul}>
          <li><strong>Casting Location</strong>: {location}</li>
          <li>
            <strong>Call Time</strong>:
            {dates.length ? Sugar.Date.format(new Date(dates[0].start), '{full}') : ''}
          </li>
          {notes && notes.length ? <li><strong>Notes</strong>: {notes}</li> : null}
        </ul>

        <div style={styles.statusText}>
          {JOB_CASTING_STATUS_TEXT_MAP[status]}
        </div>

        {status !== 'released'
          ? <div style={styles.confirmDecline}>
            <RaisedButton
              primary={status === 'confirmed'}
              disabled={status === 'confirmed'}
              label="Confirm"
              onTouchTap={this.handleConfirmButton}
            />
            <RaisedButton
              primary={status === 'declined'}
              disabled={status === 'declined'}
              label="Decline"
              onTouchTap={this.handleDeclineButton}
            />
          </div>
          : ''}
      </div>
    )
  }
}
