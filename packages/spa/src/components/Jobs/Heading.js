import React, { PureComponent, PropTypes } from 'react'
import IconButton from 'material-ui/IconButton'
import EditorModeEditIcon from 'material-ui/svg-icons/editor/mode-edit'
import { blueGrey50, grey200 } from 'material-ui/styles/colors'

import { DEFAULT_PHOTO } from '../../constants'
import theme from 'theme/mui-theme'
import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'
import Heading from 'components/Heading/Heading'
import MetaInfoLine from '../Projects/MetaInfoLine'

const { string, object } = PropTypes

const { primary2Color } = theme.palette

const styles = {
  wrapper: {
    display: 'flex',
    justifyContent: 'space-between',
  },
  avatar: {
    width: 125,
    height: 90,
    backgroundColor: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    border: `3px solid ${blueGrey50}`,
    backgroundImage: `url('${DEFAULT_PHOTO}')`,
  },
  middle: {
    flex: 1,
    padding: '0 1em',
  },
  right: {
    marginTop: '1em',
  },
  repUrl: {
    textDecoration: 'underline',
  },
}

export default class ProjectHeading extends PureComponent {
  static propTypes = {
    job: object.isRequired,
    returnTo: string,
  }

  static defaultProps = {
    returnTo: false,
  }

  render() {
    const { job, returnTo } = this.props
    const { id, projectName, avatar, representative } = job
    const avatarImage = avatar && avatar.length && getMediaUrl(avatar, 256)
    const avatarStyle = (avatarImage && { backgroundImage: `url('${avatarImage}')` }) || {}

    return (
      <div style={styles.wrapper}>
        <div style={sm(styles.avatar, avatarStyle)} />

        <div style={styles.middle}>
          <Heading text={projectName} weight={400} size={2} />
          <MetaInfoLine limit={100} {...job} />
          <div>
            Job offered by
            {' '}
            <a
              href={`#/${representative.slug}/?returnTo=${returnTo || `/job/${id}/`}`}
              style={styles.repUrl}
            >
              {representative.companyName}, {representative.name}
            </a>
          </div>

        </div>
      </div>
    )
  }
}
