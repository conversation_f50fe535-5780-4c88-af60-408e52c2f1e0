import { Component, PropTypes, Children, createElement } from 'react'

const { string, object, node } = PropTypes

const defaultQueries = {
  xs: 'screen and (max-width: 543px)',
  sm: 'screen and (min-width: 544px)',
  md: 'screen and (min-width: 768px)',
  lg: 'screen and (min-width: 992px)',
  xl: 'screen and (min-width: 1200px)',
  retina: '(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)',
}

// TODO: SSR support via fakeMedia
const matchMedia = window.matchMedia

class MediaQueriesMatcher {
  state = {}
  listeners = []

  constructor(queries = {}, fakeMedia) {
    this.queries = this.matchQueries(queries, fakeMedia)
    this.map = Object.entries(queries).reduce((map, [key, value]) => ({ ...map, [value]: key }), {})
    this.state = this.getNewState()

    this.addListeners()
  }

  matchQueries(queries = {}, fakeMedia) {
    return Object.entries(queries).map(([, query]) => matchMedia(query, fakeMedia))
  }

  addListeners() {
    this.queries.forEach(mql => mql.addListener(this::this.listener))
  }

  removeListeners() {
    this.queries.forEach(mql => mql.removeListener(this.listener))
  }

  addListener(listener) {
    if (typeof listener === 'function' && !this.listeners.find(item => item === listener)) {
      this.listeners.push(listener)
    }
  }

  removeListener(listener) {
    const index = this.listeners.findIndex(item => item === listener)

    if (index >= 0) {
      this.listeners.splice(index, 1)
    }
  }

  listener(mql) {
    this.state = this.getNewState(mql)
    this.listeners.forEach(listener => listener(mql, this.matches))
  }

  // TODO: relying on item.media to build the new object sucks because the original query may not match what the browser has in mql.media
  getNewState(mql = this.queries) {
    if (mql.length) {
      return {
        ...this.state,
        ...mql.reduce((result, item) => ({ ...result, [item.media]: item.matches }), {}),
      }
    }

    return {
      ...this.state,
      [mql.media]: mql.matches,
    }
  }

  destroy() {
    this.removeListeners()
  }

  get matches() {
    return Object.entries(this.map).reduce((matches, [key, value]) => ({ ...matches, [value]: this.state[key] }), {})
  }
}

export default class MediaQueryContext extends Component {
  static propTypes = {
    children: node.isRequired,
    wrapWith: string,
    queries: object,
    fakeMedia: object,
  }

  static childContextTypes = {
    mediaQueries: object,
    mediaMatcher: object,
  }

  static defaultProps = {
    wrapWith: 'div',
    queries: defaultQueries,
    fakeMedia: {},
  }

  state = {
    matches: {},
    matcher: {},
  }

  getChildContext() {
    return {
      mediaQueries: this.state.matches,
      mediaMatcher: this.state.matcher,
    }
  }

  componentWillMount() {
    const { queries, fakeMedia } = this.props
    const matcher = new MediaQueriesMatcher(queries, fakeMedia)

    matcher.addListener(this.updateMatcherState)

    this.setState({ matcher, matches: matcher.matches })
  }

  componentWillUnmount() {
    const { matcher } = this.state

    if (matcher) {
      matcher.destroy()
    }
  }

  updateMatcherState = (mql, matches) => this.setState({ matches })

  render() {
    const { children, wrapWith } = this.props
    const childrenCount = Children.count(children)

    if (childrenCount > 1) {
      return createElement(wrapWith, {}, children)
    }

    return children
  }
}
