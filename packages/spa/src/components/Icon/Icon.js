import React, { Component, PropTypes } from 'react'

export default class Icon extends Component {
  static propTypes = {
    type: PropTypes.string,
    name: PropTypes.string.isRequired,
    fw: PropTypes.bool,
    color: PropTypes.string,
    marginRight: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
  }

  static defaultProps = {
    type: 'fa',
    name: 'fort-awesome',
    fw: false,
    marginRight: 0,
  }

  render() {
    const { type, name, fw, color, marginRight } = this.props
    const classes = `${type} ${fw ? 'fa-fw' : ''} ${type}-${name}`
    const style = {}

    if (marginRight) {
      style.marginRight = marginRight
    }

    if (color) {
      style.color = color
    }

    return (
      <i className={classes} style={{ ...style }}/>
    )
  }
}
