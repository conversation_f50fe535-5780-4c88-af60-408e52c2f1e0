import React, { Component } from 'react'
import FlatButton from 'material-ui/FlatButton'
import ProfileModal from 'components/ProfileDialog/ProfileDialog'
import ModeEditAppIcon from 'material-ui/svg-icons/editor/mode-edit'

const color = '#BB3053'

export default class ProfileDialogButton extends Component {
  state = {
    open: false,
  }

  handleOpen = () => {
    this.setState({ open: true })
  }

  handleClose = () => {
    this.setState({ open: false })
  }

  render() {
    return (
      <div>
        <FlatButton
          label={'Edit Profile'}
          labelStyle={{ textTransform: 'capitalize', color }}
          style={{ boxShadow: 'none', backgroundColor: 'rgba(0,0,0,0)' }}
          color={'none'}
          icon={<ModeEditAppIcon color={color} />}
          onTouchTap={this.handleOpen}
        />
        <ProfileModal
          title='Edit Profile'
          modal={false}
          open={this.state.open}
          mutable={this.props.mutable}
          onRequestClose={this.handleClose}
          autoScrollBodyContent
          contentStyle={{ maxWidth: '75%' }}
          titleStyle={{ display: 'none' }}
          tabItemContainerStyle={{ display: 'none' }}
          profile={this.props.profile}
        />
      </div>
    )
  }
}
