import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import Dialog from 'material-ui/Dialog'
import FlatButton from 'material-ui/FlatButton'
import { Tabs, Tab } from 'material-ui/Tabs'
import { white } from 'material-ui/styles/colors'

import theme from 'theme/mui-theme'
import * as profileActions from 'redux/modules/profile'
import Profile from 'components/Profile/Profile'
import MediaGrid from 'components/Media/Grid'
import CompCardSideA from 'components/CompCard/SideA'
import CompCardSideB from 'components/CompCard/SideB'

const { bool, string, object, func, arrayOf } = PropTypes

const { primary1Color } = theme.palette

const tabSlugToId = (slug) => {
  switch (slug) {
    case 'portfolio':
      return 1
    case 'comp-card':
      return 2
    default:
      return 0
  }
}

const styles = {
  wrapper: {
    display: 'flex',
    flexWrap: 'wrap',
  },
}

@connect(
  state => ({
    profile: state.profile.data,
    loading: state.profile.loading,
    loaded: state.profile.loaded,
    action: state.profile.action,
    error: state.profile.error,
  }),
  { ...profileActions },
)
export default class ProfileModal extends Component {
  static propTypes = {
    open: bool,
    id: string,
    tabSlug: string,
    onRequestClose: func,
    profile: object,
    mediaUploads: arrayOf(object),
    loading: bool,
    loaded: bool,
    load: func,
  }

  static defaultProps = {
    open: false,
    tabSlug: 'profile',
  }

  state = {
    activeTab: tabSlugToId(this.props.tabSlug),
  }

  componentWillMount() {
    const { id, load } = this.props

    if (id) {
      load(id)
    }
  }

  componentWillReceiveProps(nextProps) {
    const { id, load } = this.props

    if (id !== nextProps.id) {
      load(nextProps.id)
    }
  }

  handleTabChange = activeTab => this.setState({ activeTab })

  render() {
    const { profile, open, id, tabSlug, onRequestClose, loaded, loading, mutable } = this.props
    const { activeTab } = this.state
    const profileName = profile && profile.name
    const media = (profile && profile.media) || []

    if (tabSlug === 'comp-card') {
      return (
        <Dialog
          actions={[<FlatButton label="Close" primary onTouchTap={onRequestClose} />]}
          open={open}
          modal={false}
          onRequestClose={onRequestClose}
          autoScrollBodyContent
          titleStyle={{ display: 'none' }}
          bodyStyle={{ padding: 0 }}
          contentStyle={{ minWidth: '95%', width: 'inherit', padding: '1em' }}
          repositionOnUpdate
          autoDetectWindowHeight
        >
          <div style={styles.wrapper}>
            <CompCardSideA {...profile} />
            <CompCardSideB {...profile} />
          </div>
        </Dialog>
      )
    }

    return (
      <Dialog
        title={profileName}
        actions={[<FlatButton label="Close" secondary onTouchTap={onRequestClose} />]}
        open={open}
        onRequestClose={onRequestClose}
        autoScrollBodyContent
        titleStyle={{ backgroundColor: primary1Color, color: white, ...this.props.titleStyle }}
        bodyStyle={{ padding: 0 }}
        contentStyle={{
          minWidth: '95%',
          width: 'inherit',
          padding: '1em',
          ...this.props.contentStyle,
        }}
        repositionOnUpdate
        autoDetectWindowHeight
      >
        <Tabs
          value={activeTab}
          onChange={this.handleTabChange}
          tabItemContainerStyle={this.props.tabItemContainerStyle}
        >
          <Tab label="Profile" value={0}>
            <Profile mutable={mutable} {...profile} />
          </Tab>
          <Tab label="Portfolio" value={1}>
            <MediaGrid mutable={mutable} {...profile} profile={profile} media={media} />
          </Tab>
          <Tab label="Comp Card" value={2}>
            <div style={styles.wrapper}>
              <CompCardSideA mutable={mutable} {...profile} />
              <CompCardSideB mutable={mutable} {...profile} />
            </div>
          </Tab>
        </Tabs>
      </Dialog>
    )
  }
}
