import React, { PropTypes } from 'react'
import sm from 'helpers/stylesMerge'

const { string, number, bool, object, oneOfType } = PropTypes

const baseStyle = {
  fontSize: 16,
  letterSpacing: '0.03em',
}

export default function Heading(props) {
  const { text, size, weight, margin, padding, color, opacity } = props
  let { style } = props

  style = sm(baseStyle, style)

  style.fontSize = style.fontSize * size
  style.fontWeight = weight
  style.color = color
  if (opacity) style.opacity = opacity

  if (margin) style.marginBottom = style.fontSize / 2

  if (typeof padding === 'number') style.padding = style.padding = padding
  else if (padding) style.padding = style.padding = style.fontSize / 2

  return (
    <div style={style}>
      {text}
    </div>
  )
}

Heading.propTypes = {
  text: oneOfType([string, object]),
  size: number,
  weight: number,
  margin: bool,
  padding: oneOfType([bool, number]),
  style: object,
  color: string,
  opacity: number,
}

Heading.defaultProps = {
  text: '',
  size: 1,
  weight: 200,
  margin: false,
  padding: false,
  style: {},
  color: 'inherit',
}
