import React, { PropTypes } from 'react'
import { grey400 } from 'material-ui/styles/colors'

const { number, string } = PropTypes

const style = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: 'calc(100vh - 100px)',
  color: grey400,
  padding: '2em',
}

export default function ZeroResults({ text = 'There are no results', results }) {
  if (!results) {
    return <div style={style}>{text}</div>
  }

  return <div />
}

ZeroResults.propTypes = {
  text: string,
  results: number,
}

ZeroResults.defaultProps = {
  text: 'There are no results',
  results: 0,
}
