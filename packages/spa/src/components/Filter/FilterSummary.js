import React from 'react'

import { pluralise } from 'helpers/string'

const style = {
  textAlign: 'right',
  marginRight: 16,
  color: 'rgba(0, 0, 0, 0.541176)',
  fontSize: 14,
}

export default function FilterSummary({ filters = 0, results = 0 }) {
  return (
    <div style={style}>
      {filters} {pluralise('filter', filters)} applied with {results}{results >= 100 ? '+' : ''} {pluralise('result', results)}
    </div>
  )
}
