import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import ReactDOM from 'react-dom'
import { connect as reduxConnect } from 'react-redux'
import Paper from 'material-ui/Paper'
import IconButton from 'material-ui/IconButton'
import ImageAddAPhotoIcon from 'material-ui/svg-icons/image/add-a-photo'
import { white, grey600 } from 'material-ui/styles/colors'

import theme from 'theme/mui-theme'
import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import * as profileActions from 'redux/modules/profile'
import Heading from 'components/Heading/Heading'
import InsigniaIcon from 'components/Icons/Insignia'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'
import UploadProgress from 'components/Progress/Upload'

const { bool, string, func, object, oneOf, arrayOf } = PropTypes

const { primary2Color } = theme.palette

const styles = {
  front: {
    flex: 1,
    width: '100%',

    // minHeight: '70vh',
    minHeight: 'calc(100vh - 112px)',
    background: white,
    padding: 20,
    display: 'flex',
    position: 'relative',
  },
  frontSm: {
    width: '50%',
    minWidth: 280,
    margin: 8,
  },
  frontCover: {
    display: 'flex',
    flex: 1,
    alignItems: 'center',
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    position: 'relative',
  },
  frontCoverHeading: {
    textAlign: 'center',
    background: 'rgba(255, 255, 255, 0.7)',
    padding: '2px 0',
    flex: 1,
    opacity: 0.5,
    position: 'absolute',
    width: '100%',
    cursor: 'pointer',
  },
  insignia: {
    fontSize: 24,
    width: '2.5em',
    height: '2.5em',
    display: 'block',
    margin: '0 auto',
  },
  insigniaTop: {
    position: 'absolute',
    right: 11,
    top: 10,
    zIndex: 1,
  },
  insigniaBottom: {
    position: 'absolute',
    left: 10,
    bottom: 11,
    zIndex: 1,
  },
  monochrome: {
    filter: 'contrast(0.9) grayscale(1)',
    WebkitFilter: 'contrast(0.9) grayscale(1)',
  },
  uploadButton: {
    position: 'absolute',
    zIndex: 1,
  },
  fileStyle: {
    position: 'relative',
    top: -20,
    left: -20,
    opacity: 0,
  },
  topLabelSlot: {
    top: '12%',
  },
  middleLabelSlot: {
    marginTop: -12,
  },
  bottomLabelSlot: {
    top: '82%',
  },
  labelCurrent: {
    opacity: 1,
  },
  labelHovered: {
    opacity: 1,
  },
  photoButton: {
    background: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 3,
    margin: 6,
    top: 0,
  },
}

@reduxConnect(() => ({}), {
  profileSave: profileActions.save,
})
class LabelPositionSelectorItem extends PureComponent {
  static propTypes = {
    text: string,
    profileId: string,
    position: string,
    selected: bool,
    profileSave: func,
  }

  static defaultProps = {
    text: 'Your name here',
    position: 'middle',
    selected: false,
  }

  state = {
    hovered: false,
  }

  componentDidMount() {
    ReactDOM.findDOMNode(this).addEventListener('mouseover', this.onMouseOver.bind(this))
    ReactDOM.findDOMNode(this).addEventListener('mouseout', this.onMouseOut.bind(this))
    ReactDOM.findDOMNode(this).addEventListener('click', this.onClick.bind(this))
  }

  componentWillUnmount() {
    ReactDOM.findDOMNode(this).removeEventListener('mouseover', this.onMouseOver)
    ReactDOM.findDOMNode(this).removeEventListener('mouseout', this.onMouseOut)
    ReactDOM.findDOMNode(this).removeEventListener('click', this.onClick)
  }

  onMouseOver() {
    this.setState({ hovered: true })
  }

  onMouseOut() {
    this.setState({ hovered: false })
  }

  onClick() {
    const { profileId: id, position: compCardLabelPosition, profileSave } = this.props
    profileSave({ id, compCardLabelPosition })
  }

  render() {
    const { text, position, selected } = this.props
    const { hovered } = this.state
    const name = selected ? text : '　'

    return (
      <Heading
        text={hovered ? 'Click to move your name label here' : name}
        style={sm(
          styles.frontCoverHeading,
          styles[`${position}LabelSlot`],
          selected && styles.labelCurrent,
          hovered && styles.labelHovered,
        )}
        size={1.5}
        weight={400}
      />
    )
  }
}

const CompCardSideB = (
  { mutable = false, id, name, compCardCover, compCardLabelPosition = 'middle', mediaUploads = [] },
  { mediaQueries: { xs } },
) => (
  <Paper style={sm(styles.front, !xs && styles.frontSm)} zDepth={xs ? 0 : 1} rounded={!xs}>
    <div style={styles.insigniaTop}>
      <InsigniaIcon style={styles.insignia} color={primary2Color} corner="top-right" />
    </div>
    <div style={styles.insigniaBottom}>
      <InsigniaIcon style={styles.insignia} color={primary2Color} corner="bottom-left" />
    </div>
    {mutable
      ? <div
        style={
            mediaUploads.length
              ? styles.frontCover
              : sm(
                  styles.frontCover,
                  { backgroundImage: `url('${getMediaUrl(compCardCover, 768)}')` },
                  styles.monochrome,
                )
          }
      >
        <IconButton
          tooltip="Upload photo"
          touch
          tooltipPosition="bottom-right"
          style={sm(styles.photoButton, styles.uploadButton)}
        >
          <ImageAddAPhotoIcon color={grey600} />
          <Form layout={false} action={profileActions.attachMedia} data={{ id, compCardCover }}>
            <Field
              mutable
              type="file"
              name="compCardCover"
              layout={false}
              style={styles.fileStyle}
              accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff"
            />
          </Form>
        </IconButton>

        {mediaUploads.length ? <UploadProgress {...mediaUploads[0]} /> : null}

        <LabelPositionSelectorItem
          profileId={id}
          text={name}
          position="bottom"
          selected={compCardLabelPosition === 'bottom'}
        />
      </div>
      : <div
        style={sm(
            styles.frontCover,
            { backgroundImage: `url('${getMediaUrl(compCardCover)}')` },
            styles.monochrome,
          )}
      >
        <Heading
          text={name}
          style={sm(styles.frontCoverHeading, styles[`${compCardLabelPosition}LabelSlot`])}
          size={1.5}
          weight={400}
        />
      </div>}
  </Paper>
)

CompCardSideB.propTypes = {
  mutable: bool,
  id: string,
  name: string,
  compCardCover: string,
  compCardLabelPosition: oneOf(['top', 'middle', 'bottom']),
  mediaUploads: arrayOf(object),
}

CompCardSideB.contextTypes = {
  mediaQueries: object,
}

export default CompCardSideB
