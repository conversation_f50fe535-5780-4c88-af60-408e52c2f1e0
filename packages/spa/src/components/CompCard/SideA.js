import React from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import IconButton from 'material-ui/IconButton'
import ImageAddAPhotoIcon from 'material-ui/svg-icons/image/add-a-photo'
import { white, grey600 } from 'material-ui/styles/colors'

import Theme from 'theme/mui-theme'
import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import * as profileActions from 'redux/modules/profile'
import Heading from 'components/Heading/Heading'
import LogoIcon from 'components/Icons/Logo'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'
import { eyeColorMap, hairColorMap } from 'components/Profile/Profile'
import UploadProgress from 'components/Progress/Upload'

const { string, number, array, object, oneOf, arrayOf } = PropTypes

const { primary1Color } = Theme.palette

const styles = {
  back: {
    flex: 1,
    position: 'relative',
    width: '100%',
  },
  backSm: {
    width: '50%',
    minWidth: 280,
    margin: 8,
  },
  gridWrapper: {
    position: 'relative',
  },
  gridList: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',

    // height: 600,
    // height: 'calc(100vh - 112px)',
    height: 'calc(100vh - 84px)',
  },
  gridListItem: {
    flex: '1 0 50%',
    height: '50%',
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
  },
  gridListImage: {
    height: '100%',
    transform: 'translateX(-50%)',
    position: 'relative',
    left: '50%',
  },
  logoWrapper: {
    backgroundColor: primary1Color,
    position: 'absolute',
    zIndex: 1,
    padding: '2%',
    height: '10%',
    width: '10%',
    left: 'calc(50% - 16% / 2)',

    // top: 'calc(200px + 8%)',
    // top: 'calc(calc(100vh - 112px)/3 + 8%)',
    top: 'calc(calc(100vh - 84px)/3 + 8%)',
  },
  logo: {
    display: 'block',
    width: '100%',
    height: '100%',
  },
  gridLabel: {
    position: 'absolute',
    width: '100%',
    zIndex: 1,
    bottom: 0,
  },
  gridLabelSm: {
    bottom: 0,
  },
  gridLabelHeading: {
    background: 'rgba(255, 255, 255, 0.8)',
    padding: '2px 1em',
    color: '#888',
  },
  detailBar: {
    display: 'flex',
    flexWrap: 'wrap',
    backgroundColor: primary1Color,
    justifyContent: 'space-between',
    padding: '1em 1.5em',
  },
  detailBarItem: {
    color: white,
    flex: 0,
    fontWeight: 200,
    whiteSpace: 'nowrap',
  },
  uploadButton: {
    position: 'absolute',
  },
  uploadRight: {
    position: 'absolute',
    right: 0,
  },
  fileStyle: {
    position: 'relative',
    top: -20,
    left: -20,
    opacity: 0,
  },
  photoButton: {
    background: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 3,
    margin: 6,
  },
}

const pickUploads = (name, uploads) => uploads.filter(item => item.fieldName === name)[0]

const DetailBarItem = ({ label, value }) => (
  <div style={styles.detailBarItem}>
    <Heading size={0.8} text={label} weight={400} />
    <span style={{ fontSize: 12.8, opacity: 0.9 }}>{value}</span>
  </div>
)

const CompCardSideA = (
  {
    mutable,
    id,
    compCardA,
    compCardB,
    compCardC,
    compCardD,
    slug,
    gender,
    height,
    bust,
    waist,
    hips,
    suit,
    inseam,
    shoeSize,
    eyeColor,
    hairColor,
    mediaUploads = [],
  },
  { mediaQueries: { xs } },
) => {
  const mediaUploadsA = pickUploads('compCardA', mediaUploads)
  const mediaUploadsB = pickUploads('compCardB', mediaUploads)
  const mediaUploadsC = pickUploads('compCardC', mediaUploads)
  const mediaUploadsD = pickUploads('compCardD', mediaUploads)

  return (
    <Paper style={sm(styles.back, !xs && styles.backSm)} zDepth={xs ? 0 : 1} rounded={!xs}>
      <div style={styles.gridWrapper}>
        <div style={styles.logoWrapper}>
          <LogoIcon style={styles.logo} color={white} />
        </div>
        <div style={styles.gridList}>
          <div
            style={
              mediaUploadsA
                ? styles.gridListItem
                : sm(styles.gridListItem, {
                  backgroundImage: `url('${getMediaUrl(compCardA, 512)}')`,
                })
            }
          >
            {!mediaUploadsA && mutable
              ? <IconButton
                tooltip="Upload photo"
                touch
                tooltipPosition="bottom-right"
                style={sm(styles.photoButton, styles.uploadButton)}
              >
                <ImageAddAPhotoIcon color={grey600} />
                <Form layout={false} action={profileActions.attachMedia} data={{ id, compCardA }}>
                  <Field
                    mutable
                    type="file"
                    name="compCardA"
                    layout={false}
                    style={styles.fileStyle}
                    accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff"
                  />
                </Form>
              </IconButton>
              : ''}
            {mediaUploadsA ? <UploadProgress {...mediaUploadsA} /> : null}
          </div>
          <div
            style={
              mediaUploadsB
                ? styles.gridListItem
                : sm(styles.gridListItem, {
                  backgroundImage: `url('${getMediaUrl(compCardB, 512)}')`,
                })
            }
          >
            {!mediaUploadsB && mutable
              ? <IconButton
                tooltip="Upload photo"
                touch
                tooltipPosition="bottom-left"
                style={sm(styles.photoButton, styles.uploadRight)}
              >
                <ImageAddAPhotoIcon color={grey600} />
                <Form layout={false} action={profileActions.attachMedia} data={{ id, compCardB }}>
                  <Field
                    mutable
                    type="file"
                    name="compCardB"
                    layout={false}
                    style={styles.fileStyle}
                    accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff"
                  />
                </Form>
              </IconButton>
              : ''}
            {mediaUploadsB ? <UploadProgress {...mediaUploadsB} /> : null}
          </div>
          <div
            style={
              mediaUploadsC
                ? styles.gridListItem
                : sm(styles.gridListItem, {
                  backgroundImage: `url('${getMediaUrl(compCardC, 512)}')`,
                })
            }
          >
            {!mediaUploadsC && mutable
              ? <IconButton
                tooltip="Upload photo"
                touch
                tooltipPosition="bottom-right"
                style={sm(styles.photoButton, styles.uploadButton)}
              >
                <ImageAddAPhotoIcon color={grey600} />
                <Form layout={false} action={profileActions.attachMedia} data={{ id, compCardC }}>
                  <Field
                    mutable
                    type="file"
                    name="compCardC"
                    layout={false}
                    style={styles.fileStyle}
                    accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff"
                  />
                </Form>
              </IconButton>
              : ''}
            {mediaUploadsC ? <UploadProgress {...mediaUploadsC} /> : null}
          </div>
          <div
            style={
              mediaUploadsD
                ? styles.gridListItem
                : sm(styles.gridListItem, {
                  backgroundImage: `url('${getMediaUrl(compCardD, 512)}')`,
                })
            }
          >
            {!mediaUploadsD && mutable
              ? <IconButton
                tooltip="Upload photo"
                touch
                tooltipPosition="bottom-left"
                style={sm(styles.photoButton, styles.uploadRight)}
              >
                <ImageAddAPhotoIcon color={grey600} />
                <Form layout={false} action={profileActions.attachMedia} data={{ id, compCardD }}>
                  <Field
                    mutable
                    type="file"
                    name="compCardD"
                    layout={false}
                    style={styles.fileStyle}
                    accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff"
                  />
                </Form>
              </IconButton>
              : ''}
            {mediaUploadsD ? <UploadProgress {...mediaUploadsD} /> : null}
          </div>

          <div style={sm(styles.gridLabel, !xs && styles.gridLabelSm)}>
            <Heading
              text={
                <span>
                  <strong style={{ fontWeight: 600, color: '#000' }}>lookbook.io/</strong>
                  {slug}
                </span>
              }
              style={styles.gridLabelHeading}
              size={xs ? 1 : 1.5}
              weight={300}
            />
          </div>
        </div>

      </div>
      {gender === 'female'
        ? <div style={styles.detailBar}>
          <DetailBarItem label="Height" value={`${height} cm`} />
          <DetailBarItem label="Bust" value={`${bust} cm`} />
          <DetailBarItem label="Waist" value={`${waist} cm`} />
          <DetailBarItem label="Hips" value={`${hips} cm`} />
          <DetailBarItem label="Shoe" value={`${shoeSize} US`} />
          <DetailBarItem label="Eyes" value={eyeColorMap[eyeColor]} />
          <DetailBarItem label="Hair" value={hairColorMap[hairColor]} />
        </div>
        : <div style={styles.detailBar}>
          <DetailBarItem label="Height" value={`${height} cm`} />
          <DetailBarItem label="Waist" value={`${waist} cm`} />
          <DetailBarItem label="Suit" value={`${suit} US`} />
          <DetailBarItem label="Inseam" value={`${inseam} cm`} />
          <DetailBarItem label="Shoe" value={`${shoeSize} US`} />
          <DetailBarItem label="Eyes" value={eyeColorMap[eyeColor]} />
          <DetailBarItem label="Hair" value={hairColorMap[hairColor]} />
        </div>}
    </Paper>
  )
}

CompCardSideA.propTypes = {
  sessionUnits: oneOf(['metric', 'imperial']),
  slug: string,
  height: number,
  waist: number,
  bust: number,
  hips: number,
  suit: number,
  shirt: number,
  inseam: number,
  shoeSize: number,
  eyeColor: string,
  hairColor: string,
  media: array,
  mediaUploads: arrayOf(object),
}

CompCardSideA.defaultProps = {
  sessionUnits: 'metric',
  slug: '',
  height: 0,
  waist: 0,
  bust: 0,
  hips: 0,
  suit: 0,
  shirt: 0,
  inseam: 0,
  shoeSize: 0,
  eyeColor: '',
  hairColor: '',
  media: [],
}

CompCardSideA.contextTypes = {
  mediaQueries: object,
}

/*
export default connect(
  state => ({
    sessionUnits: state.session.units,
  }),
  { })(CompCardSideA)
  */
export default CompCardSideA
