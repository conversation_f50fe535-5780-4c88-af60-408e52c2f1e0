import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import { darken } from 'material-ui/utils/colorManipulator'
import Drawer from 'material-ui/Drawer'
import { ListItem } from 'material-ui/List'
import Menu from 'material-ui/Menu'
import Divider from 'material-ui/Divider'
import ActionSettingsIcon from 'material-ui/svg-icons/action/settings'
import ActionExitToAppIcon from 'material-ui/svg-icons/action/exit-to-app'
import ActionDashboardIcon from 'material-ui/svg-icons/action/dashboard'
import ActionFaceIcon from 'material-ui/svg-icons/action/face'
import ActionWorkIcon from 'material-ui/svg-icons/action/work'
import ActionSearchIcon from 'material-ui/svg-icons/action/search'
import ImagePortraitIcon from 'material-ui/svg-icons/image/portrait'
import ImageStyleIcon from 'material-ui/svg-icons/image/style'
import SocialPeopleIcon from 'material-ui/svg-icons/social/people'
import AvRecentActorsIcon from 'material-ui/svg-icons/av/recent-actors'
import ActionSupervisorAccountIcon from 'material-ui/svg-icons/action/supervisor-account'

import theme from 'theme/mui-theme'
import * as sessionActions from 'redux/modules/session'
import * as accountActions from 'redux/modules/account'
import * as profileActions from 'redux/modules/profile'
import LogoIcon from 'components/Icons/Logo'

const { bool, object, func } = PropTypes

const { primary1Color, primary2Color } = theme.palette

const styles = {
  heading: {
    height: '10em',
    background: `linear-gradient(to bottom, ${primary1Color} 60%, ${darken(primary1Color, 0.3)} 100%)`,
    display: 'flex',
    alignItems: 'center',
  },
  logo: {
    fontSize: 24,
    width: '4.5em',
    height: '4.5em',
    display: 'block',
    margin: '2em auto',
  },
  menu: {
    position: 'relative',
  },
}

@connect(
  state => ({
    session: state.session.data,
    account: state.account.data,
  }),
  {
    sessionDestroy: sessionActions.destroy,
    accountReset: accountActions.reset,
    profileReset: profileActions.reset,
  },
)
export default class NavDrawer extends Component {
  static propTypes = {
    open: bool.isRequired,
    onRequestChange: func.isRequired,
    sessionDestroy: func,
    account: object,
    accountReset: func,
    profileReset: func,
  };

  handleLogout = () => {
    this.props.onRequestChange()
  };

  render() {
    const { account } = this.props
    const isAdmin = account && account.permissions && account.permissions.admin
    const isRepresentative = account && account.permissions && account.permissions.representative
    const isTalent = !isAdmin && !isRepresentative

    return (
      <Drawer docked={false} open={this.props.open} onRequestChange={this.props.onRequestChange}>
        <div style={styles.heading}>
          <LogoIcon style={styles.logo} secondaryColor={primary2Color} />
        </div>

        <Menu desktop width={320} style={styles.menu}>
          <ListItem
            primaryText="Dashboard"
            leftIcon={<ActionDashboardIcon />}
            href="#/"
            onTouchTap={this.props.onRequestChange}
          />

          {(isTalent || isRepresentative) &&
            <ListItem
              primaryText="My Profile"
              leftIcon={<ActionFaceIcon />}
              href="#/profile"
              onTouchTap={this.props.onRequestChange}
            />}

          {isTalent &&
            <ListItem
              primaryText="My Portfolio"
              leftIcon={<ImagePortraitIcon />}
              href="#/profile/portfolio"
              onTouchTap={this.props.onRequestChange}
            />}

          {isTalent &&
            <ListItem
              primaryText="My Comp Card"
              leftIcon={<ImageStyleIcon />}
              href="#/profile/comp-card"
              onTouchTap={this.props.onRequestChange}
            />}

          {isTalent &&
            <ListItem
              primaryText="My Jobs"
              leftIcon={<ActionWorkIcon />}
              href="#/my-jobs"
              onTouchTap={this.props.onRequestChange}
            />}

          {isTalent &&
            <ListItem
              primaryText="Public Jobs"
              leftIcon={<ActionSearchIcon />}
              href="#/jobs"
              onTouchTap={this.props.onRequestChange}
            />}

          {(isRepresentative || isAdmin) &&
            <ListItem
              primaryText="Search Talent"
              leftIcon={<SocialPeopleIcon />}
              href="#/search/talent"
              onTouchTap={this.props.onRequestChange}
            />}

          {isRepresentative &&
            <ListItem
              primaryText="Projects"
              leftIcon={<AvRecentActorsIcon />}
              href="#/projects"
              onTouchTap={this.props.onRequestChange}
            />}

          {isAdmin &&
            <div>
              <Divider />
              <ListItem
                primaryText="Admin"
                leftIcon={<ActionSettingsIcon />}
                primaryTogglesNestedList
                initiallyOpen
                nestedItems={[
                  <ListItem
                    key={1}
                    primaryText="Model Approval"
                    leftIcon={<ActionFaceIcon />}
                    href="#/admin/approve/profiles/model"
                    onTouchTap={this.props.onRequestChange}
                  />,
                  <ListItem
                    key={2}
                    primaryText="Representative Approval"
                    leftIcon={<ActionSupervisorAccountIcon />}
                    href="#/admin/approve/profiles/representative"
                    onTouchTap={this.props.onRequestChange}
                  />,
                  <ListItem
                    key={3}
                    primaryText="Jobs Approval"
                    leftIcon={<AvRecentActorsIcon />}
                    href="#/admin/approve/jobs"
                    onTouchTap={this.props.onRequestChange}
                  />,
                ]}
              />
            </div>}

          <Divider />

          {/* <ListItem primaryText="Settings" leftIcon={<ActionSettingsIcon />} href="#/settings" onTouchTap={this.props.onRequestChange} /> */}
          <ListItem
            primaryText="Logout"
            leftIcon={<ActionExitToAppIcon />}
            href="#/logout"
            onTouchTap={this.handleLogout}
          />
        </Menu>
      </Drawer>
    )
  }
}
