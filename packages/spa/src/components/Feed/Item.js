import React from 'react'
import striptags from 'striptags'
import { white, blueGrey50 } from 'material-ui/styles/colors'

import theme from 'theme/mui-theme'
import sm from 'helpers/stylesMerge'
import { formatDate } from 'helpers/date'
import Heading from 'components/Heading/Heading'

const { primary2Color } = theme.palette
const defaultCover = require(`../../../static/images/ic_landscape_white_48px.svg`)

const styles = {
  newsItem: {
    // display: 'flex',
    background: white,
    margin: '3em 0 3em',
    position: 'relative',
    padding: 16,
  },
  newsAvatar: {
    width: 120,
    height: 120,
    backgroundColor: primary2Color,
    backgroundSize: 'cover',
    backgroundPosition: 'top center',
    border: `8px solid ${blueGrey50}`,
    borderRadius: '100%',
    zIndex: 1,
    position: 'relative',
    top: -20 - 16,
    left: -16,
    float: 'left',
    marginBottom: -34,
  },
  newsItemDetails: {
  },
}

export default function Item({ title, summary, link, pubDate }) {
  const cleanSummary = striptags(summary, ['a'])
  const div = document.createElement('div')
  div.innerHTML = summary
  const img = div.querySelector('img[src]')
  const src = img ? img.src : false

  return (
    <div style={styles.newsItem}>
      <div style={sm(styles.newsAvatar, { backgroundImage: `url('${src ? src : defaultCover}')` })} />
      <div style={styles.newsItemDetails}>
        <Heading size={1.1} text={title} weight={200} href={link} target="_new" />
        <br />
        <div>
          <div dangerouslySetInnerHTML={{ __html: cleanSummary }} />
          <br />
          <a href={link} target="_blank">Read the full article</a>
        </div>
        <br />
        <strong>{formatDate(pubDate)}</strong>
      </div>
    </div>
  )
}
