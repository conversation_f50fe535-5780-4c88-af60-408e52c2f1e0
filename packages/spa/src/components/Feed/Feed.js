import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'

import * as feedActions from 'redux/modules/feed'
import FeedItem from './Item'

const { object, func } = PropTypes

const styles = {
  newsWrapper: {},
}

@connect(
  state => ({
    feed: state.feed.data.feed,
  }),
  feedActions
)
export default class Dashboard extends Component {
  static propTypes = {
    feed: object,
    load: func,
  }

  static contextTypes = {
    mediaQueries: object,
  }

  componentWillMount() {
    this.props.load()
  }

  render() {
    const { feed } = this.props
    // const { mediaQueries: { xs } } = this.context

    return (
      <div style={styles.newsWrapper}>
        { feed.items.map((item, index) => <FeedItem key={index} { ...item } />) }
      </div>
    )
  }
}
