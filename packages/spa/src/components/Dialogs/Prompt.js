import React, { Component, PropTypes } from 'react'
import Dialog from 'material-ui/Dialog'
import FlatButton from 'material-ui/FlatButton'
import TextField from 'material-ui/TextField'

const { bool, number, string, func, object } = PropTypes

const styles = {
  contentStyle: {
    width: '30%',
    minWidth: 300,
  },
}

export default class PromptDialog extends Component {
  static propTypes = {
    children: object,
    show: bool.isRequired,
    title: string.isRequired,
    hintText: string,
    labelText: string,
    defaultText: string,
    rows: number,
    onCloseConfirm: func,
    onCloseCancel: func,
  }

  static defaultProps = {
    show: false,
    defaultText: '',
    rows: 1,
  }

  state = {
    value: '',
    show: this.props.show,
  }

  componentWillReceiveProps(nextProps) {
    const { defaultText } = nextProps
    this.setState({ show: nextProps.show, value: defaultText })
  }

  handleCloseConfirm = () => {
    const { onCloseConfirm } = this.props

    this.setState({ show: false })
    if (onCloseConfirm) onCloseConfirm(this.state.value)
  }

  handleClose = () => {
    const { onCloseCancel } = this.props

    this.setState({ show: false })
    if (onCloseCancel) onCloseCancel()
  }

  handleChange = event => this.setState({ value: event.target.value })

  handleFieldEnterKey = event => event.keyCode === 13 && this.state.value.length ? this.handleCloseConfirm() : false

  handleFocus = input => setTimeout(() => input && input.focus(), 500) // your wondering, "wtf?" right? yea, me too. go ask material-ui.

  render() {
    const { title, hintText, labelText, defaultText, rows } = this.props
    const { value, show } = this.state

    return (
      <Dialog
        title={title}
        actions={[
          <FlatButton
            label="Cancel"
            secondary
            onTouchTap={this.handleClose}
          />,
          <FlatButton
            label="Okay"
            secondary
            onTouchTap={this.handleCloseConfirm}
            disabled={!value}
          />,
        ]}
        modal
        open={show}
        contentStyle={styles.contentStyle}
      >
        <TextField
          ref={this.handleFocus}
          floatingLabelText={labelText}
          hintText={hintText}
          name="prompt"
          id="promptDialogInput"
          fullWidth
          multiLine={rows > 1}
          rows={rows}
          defaultValue={defaultText}
          onChange={this.handleChange}
          onKeyDown={this.handleFieldEnterKey}
          autoFocus
        />
      </Dialog>
    )
  }
}
