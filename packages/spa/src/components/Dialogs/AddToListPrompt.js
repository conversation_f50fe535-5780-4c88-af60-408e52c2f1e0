import React, { PureComponent, PropTypes } from 'react'
import { connect } from 'react-redux'
import Dialog from 'material-ui/Dialog'
import RaisedButton from 'material-ui/RaisedButton'
import FlatButton from 'material-ui/FlatButton'
import { List, ListItem } from 'material-ui/List'
import Avatar from 'material-ui/Avatar'
import { white, grey200 } from 'material-ui/styles/colors'

import * as castingsActions from 'redux/modules/castings'
import * as bookingsActions from 'redux/modules/bookings'
import { DEFAULT_AVATAR } from '../../constants'
import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import noop from 'helpers/noop'
import Heading from 'components/Heading/Heading'

const { bool, object, func, arrayOf } = PropTypes

const styles = {
  dialogStyle: {},
  contentStyle: {},
  bodyStyle: {
    padding: 2,
  },
  flex: {
    display: 'flex',
    flexWrap: 'wrap',
  },
  avatar: {
    width: 'calc(100% / 3)',
    backgroundColor: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundImage: `url('${DEFAULT_AVATAR}')`,
    backgroundRepeat: 'no-repeat',
    boxSizing: 'border-box',
    border: `1px solid ${white}`,
    position: 'relative',
  },
  avatarSm: {
    width: '33.5%',
  },
  avatarOverlay: {
    width: '100%',
    height: 125,
    background: 'rgba(255, 3, 3, 0.65)',
    opacity: 0.8,
    position: 'absolute',
    bottom: 0,
    padding: 10,
    boxSizing: 'border-box',
    color: white,
    textShadow: '0px 0px 1px #000',
  },
  left: {},
  leftXs: {
    display: 'flex',
    width: '100%',
    paddingBottom: 5,
    borderBottom: `1px solid ${grey200}`,
  },
  right: {
    height: 'calc(75vh - 64px)',
    overflow: 'scroll',
  },
  rightSm: {
    flex: 1,
  },
  addButton: {
    minWidth: 'none',
  },
  heading: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    marginLeft: 3,
  },
}

function ProjectListItem(props) {
  const {
    id,
    type,
    name,
    project: {
      avatar = DEFAULT_AVATAR,
      name: projectName,
    },
    onAdd,
  } = props

  const handleAdd = () => onAdd(type, id)

  return (
    <ListItem
      primaryText={name}
      secondaryText={projectName}
      leftAvatar={<Avatar src={getMediaUrl(avatar, 128)} />}
      rightIconButton={
        <RaisedButton label="Add" primary style={styles.addButton} onTouchTap={handleAdd} />
      }
    />
  )
}

@connect(
  state => ({
    castings: state.castings.data,
    bookings: state.bookings.data,
  }),
  {
    castingsLoad: castingsActions.load,
    bookingsLoad: bookingsActions.load,
  },
)
export default class AddToListPrompt extends PureComponent {
  static propTypes = {
    open: bool.isRequired,
    profile: object.isRequired,
    onAddConfirm: func.isRequired,
    onCloseCancel: func,
    castings: arrayOf(object),
    bookings: arrayOf(object),
    castingsLoad: func.isRequired,
    bookingsLoad: func.isRequired,
  };

  static contextTypes = {
    mediaQueries: object,
  };

  static get defaultProps() {
    return {
      show: false,
      castings: [],
      bookings: [],
      profile: {},
      onCloseConfirm: noop,
      onCloseCancel: noop,
    }
  }

  state = {
    open: this.props.open,
  };

  componentWillMount() {
    this.props.castingsLoad()
    this.props.bookingsLoad()
  }

  componentWillReceiveProps(nextProps) {
    this.setState({ open: nextProps.open })
  }

  handleAdd = (listType, listId) => {
    const { onAddConfirm, profile } = this.props

    onAddConfirm(profile.id, listType, listId)
  };

  handleClose = () => {
    const { onCloseCancel } = this.props

    this.setState({ open: false })

    onCloseCancel()
  };

  render() {
    const { mediaQueries: { xs } } = this.context

    const {
      castings,
      bookings,
      profile: { avatar, name, height, hairColor, eyeColor },
    } = this.props
    const { open } = this.state
    const avatarStyle = avatar ? { backgroundImage: `url('${getMediaUrl(avatar, 512)}')` } : {}

    return (
      <Dialog
        actions={[<FlatButton label="Done" primary onTouchTap={this.handleClose} />]}
        open={open}
        style={styles.dialogStyle}
        contentStyle={styles.contentStyle}
        bodyStyle={styles.bodyStyle}
        onRequestClose={this.handleClose}
        autoScrollBodyContent={false}
        autoDetectWindowHeight={false}
      >
        <div style={styles.flex}>
          {xs
            ? <div style={styles.leftXs}>
              <div style={sm(styles.avatar, avatarStyle)} />
              <div>
                <strong>{name}</strong><br />
                {height}cm<br />
                  Hair: {hairColor} <br />
                  Eyes: {eyeColor}<br />
              </div>
            </div>
            : <div style={sm(styles.left, styles.avatar, styles.avatarSm, avatarStyle)}>
              <div style={styles.avatarOverlay}>
                <strong>{name}</strong><br />
                {height}cm<br />
                  Hair: {hairColor} <br />
                  Eyes: {eyeColor}<br />
              </div>
            </div>}
          <div style={sm(styles.right, !xs && styles.rightSm)}>

            <Heading text="Add to casting" weight={400} size={1.25} style={styles.heading} />

            <List>
              {castings.map(casting => (
                <ProjectListItem
                  key={casting.id}
                  type="casting"
                  onAdd={this.handleAdd}
                  {...casting}
                />
              ))}
            </List>

            <Heading text="Add to booking" weight={400} size={1.25} style={styles.heading} />

            <List>
              {bookings.map(booking => (
                <ProjectListItem
                  key={booking.id}
                  type="booking"
                  onAdd={this.handleAdd}
                  {...booking}
                />
              ))}
            </List>
          </div>
        </div>

      </Dialog>
    )
  }
}
