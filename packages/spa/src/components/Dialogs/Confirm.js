import React, { PropTypes } from 'react'
import Dialog from 'material-ui/Dialog'
import FlatButton from 'material-ui/FlatButton'

import noop from 'helpers/noop'

const { bool, string, func } = PropTypes

const wrapper = (method, value) => () => method(value)

export default function ConfirmDialog({ open, title, message, onUserResponse }) {
  return (
    <Dialog
      title={title}
      actions={[
        <FlatButton label="Cancel" secondary onTouchTap={wrapper(onUserResponse, false)} />,
        <FlatButton label="Okay" secondary onTouchTap={wrapper(onUserResponse, true)} />,
      ]}
      modal
      open={open}
      contentStyle={{ width: '30%', minWidth: 300 }}
    >
      <div>{message}</div>
    </Dialog>
  )
}

ConfirmDialog.propTypes = {
  open: bool,
  title: string,
  message: string,
  onUserResponse: func,
}

ConfirmDialog.defaultProps = {
  open: false,
  title: 'Confirm',
  message: 'Confirm the action',
  onUserResponse: noop,
}
