import React, { PureComponent, PropTypes } from 'react'
import mousetrap from 'mousetrap'
import Dialog from 'material-ui/Dialog'
import FlatButton from 'material-ui/FlatButton'

import noop from 'helpers/noop'

const { bool, string, array, object, func } = PropTypes

const styles = {
  contentStyle: {
    width: '30%',
    minWidth: 300,
  },
}

export default class AlertDialog extends PureComponent {
  static propTypes = {
    children: object,
    show: bool.isRequired,
    title: string,
    message: string,
    errors: array,
    onClose: func,
  };

  static get defaultProps() {
    return {
      show: false,
      title: 'Alert',
      message: '',
      onClose: noop,
    }
  }

  state = {
    show: this.props.show,
  };

  componentDidMount() {
    mousetrap.bind('enter', this.handleClose)
  }

  componentWillReceiveProps(nextProps) {
    this.setState({ show: nextProps.show })
  }

  componentWillUnmount() {
    mousetrap.unbind('enter')
  }

  handleClose = () => {
    this.props.onClose()
    this.setState({ show: false })
  };

  render() {
    const { title, message, errors, children } = this.props
    const { show } = this.state
    let errorList = ''

    if (errors && errors.length) {
      errorList = errors.map(error => error.message).join(' ')
    }

    return (
      <Dialog
        title={title}
        actions={[<FlatButton label="Okay" secondary onTouchTap={this.handleClose} />]}
        modal
        open={show}
        contentStyle={styles.contentStyle}
      >
        {children || <div><p>{message}</p><p>{errorList}</p></div>}
      </Dialog>
    )
  }
}
