import React, { PropTypes } from 'react'
import CircularProgress from 'material-ui/CircularProgress'

const { bool } = PropTypes

const wrapperStyle = {
  display: 'flex',
  justifyContent: 'space-around',
  alignItems: 'center',
  padding: '20vh',
}

const progressStyle = {

}

export default function Loading({ loading, children }) {
  if (loading) {
    return (
      <div style={wrapperStyle}>
        <div style={progressStyle}>
          <CircularProgress size={80} thickness={5} />
        </div>
      </div>
    )
  }

  return children || null
}

Loading.propTypes = {
  loading: bool,
}

Loading.defaultProps = {
  loading: false,
}
