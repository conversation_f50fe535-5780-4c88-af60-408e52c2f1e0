import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'

import sm from 'helpers/stylesMerge'
import {
  femaleHeightMap, femaleBustMap, femaleWaistMap, femaleHipsMap, femaleShoeMap,
  maleHeightMap, maleWaistMap, maleSuitMap, maleInseamMap, maleShoeMap,
  eyeColorMap, hairColorMap,
} from 'components/Profile/Profile'

import { grey50, grey500 } from 'material-ui/styles/colors'

import ProfileDialogButton from 'components/ProfileDialog/ProfileDialogButton'

import getMediaUrl from 'helpers/getMediaUrl'

const { object } = PropTypes

const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'row',
    backgroundColor: grey50,
    paddingTop: 40,
  },
  containerSm: {
    flexDirection: 'column',
  },
  leftContent: {
    display: 'flex',
  },
  leftContentSm: {
    flexDirection: 'column',
  },
  rightContent: {
    display: 'flex',
  },
  rightContentSm: {
    flexDirection: 'column',
  },
  outerSection: {
    width: 200,
    height: 200,
    backgroundSize: 'cover',
    backgroundPosition: 'top center',
    borderRadius: '100%',
    zIndex: 1,
    position: 'relative',
    margin: '1.5em auto',
    marginRight: '3em',
  },
  outerSectionSm: {
    marginRight: 'auto',
    marginLeft: 'auto',
    margin: 'auto',
  },
  gridListItem: {
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    boxSizing: 'border-box',
    margin: 3,
    position: 'relative',
    border: '1px solid #888',
  },
  portrait: {
    width: 'calc(25% - 6px)',
  },
  landscape: {
    width: '100%',
  },
  infoItem: {
    border: 0,
    fontSize: 20,
    fontFamily: "'Source Sans Pro', 'sans-serif'",
  },
  infoItemSm: {
    textAlign: 'center',
  },
  infoItemList: {
    listStyle: 'none',
    margin: '0 auto',
    padding: 0,
    paddingLeft: '25px',
    paddingRight: '25px',
  },
  infoItemListSm: {
    display: 'flex',
    flexDirection: 'column',
    padding: 0,
    margin: 0,
    flexWrap: 'wrap',
  },
  infoItemH2: {
    paddingLeft: '25px',
    fontFamily: '"Source Sans Pro", "sans-serif"',
    fontWeight: 'bold',
    fontSize: '32px',
  },
  infoItemH2Sm: {
    textAlign: 'center',
    paddingLeft: 0,
  },
  infoItemDivLeft: {
    color: grey500,
    display: 'inline-block',
    textAlign: 'left',
    paddingRight: '0.25em',
  },
  infoItemDivRight: {
    color: grey500,
    display: 'inline-block',
    textAlign: 'left',
    paddingLeft: '0.25em',
  },
  infoItemSubtitle: {
    fontSize: 22,
    paddingBottom: 5,
  },
  listItemContainerSm: {
    textAlign: 'center',
  },
  profileDialogButton: {
    textAlign: 'center',
    padding: '5em',
    height: 'inherit',
  },
}

const defaultAvatar = require('../../../static/images/ic_face_white_24px.svg')

export class MiniProfile extends Component {
  static contextTypes = {
    mediaMatcher: object,
  }

  constructor(props) {
    super(props)

    this.state = {
      nameHeight: false,
    }
  }

  componentDidMount() {
    window.addEventListener('resize', this.onResize.bind(this))
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.onResize)
  }

  onResize() {
    if (this.div) {
      this.setState({ height: this.div.clientHeight })
    }
    // If you're doing nothing here then force update to trigger responsiveness
    // this.forceUpdate()
  }

  nameRef = div => {
    if (!this.div && div) {
      this.div = div

      this.setState({ nameHeight: div.clientHeight })
    }
  }

  render() {
    const { mediaMatcher: { matches: { xs } } } = this.context
    const { name, gender, height, bust, waist, hips, suit, inseam, shoeSize, eyeColor, hairColor, avatar, city, profile, mutable, type } = this.props

    const infoItemLeft = sm(styles.infoItem, xs && styles.infoItemSm, styles.infoItemDivLeft)
    const infoItemRight = sm(styles.infoItem, xs && styles.infoItemSm, styles.infoItemDivRight)

    const infoItemList = sm(styles.infoItemList, xs && styles.infoItemListSm)
    const listItemContainer = xs ? styles.listItemContainerSm : null

    const isFemale = gender === 'female'

    return (
      <div style={sm(styles.container, xs && styles.containerSm)}>
        <div style={sm(styles.leftContent, xs && styles.leftContentSm)}>
          <div style={sm(styles.outerSection, xs && styles.outerSectionSm, { backgroundImage: `url('${avatar && avatar.length ? getMediaUrl(avatar, 256) : defaultAvatar}')` })} />
          <div>
            <h2 style={sm(styles.infoItemH2, xs && styles.infoItemH2Sm)} ref={this.nameRef}>{name}</h2>
              <ul style={sm(infoItemList)}>
                <li style={listItemContainer}>
                  <div style={sm(infoItemLeft, styles.infoItemSubtitle)}>{type === 'model' ? 'Fashion Model' : ' '}</div>
                </li>
                <li style={listItemContainer}>
                  <div style={infoItemLeft}>Height:</div>
                  <div style={infoItemRight}>{isFemale ? femaleHeightMap[height] : maleHeightMap[height]}</div>
                </li>
                <li style={listItemContainer}>
                  <div style={infoItemLeft}>Bust:</div>
                  <div style={infoItemRight}>{isFemale ? femaleBustMap[bust] : maleWaistMap[waist]}</div>
                </li>
                <li style={listItemContainer}>
                  <div style={infoItemLeft}>Waist:</div>
                  <div style={infoItemRight}>{isFemale ? femaleWaistMap[waist] : maleSuitMap[suit]}</div>
                </li>
                <li style={listItemContainer}>
                  <div style={infoItemLeft}>{isFemale ? 'Hips:' : 'Inseam:'}</div>
                  <div style={infoItemRight}>{isFemale ? femaleHipsMap[hips] : maleInseamMap[inseam]}</div>
                </li>
              </ul>
          </div>
        </div>
        <div className="spacer"></div>
        <div style={sm(styles.rightContent, xs && styles.rightContentSm)}>
          <div>
            <h2 style={sm(styles.infoItemH2, xs && styles.infoItemH2Sm, { height: `${this.state.nameHeight}px` })}>{'\u00A0'}</h2>
              <ul style={sm(infoItemList)}>
                <li style={listItemContainer}>
                  <div style={sm(infoItemLeft, styles.infoItemSubtitle)}>{city}</div>
                </li>
                <li style={listItemContainer}>
                  <div style={infoItemLeft}>Shoes:</div>
                  <div style={infoItemRight}>{isFemale ? femaleShoeMap[shoeSize] : maleShoeMap[shoeSize]}</div>
                </li>
                <li style={listItemContainer}>
                  <div style={infoItemLeft}>Eyes:</div>
                  <div style={infoItemRight}>{eyeColorMap[eyeColor]}</div>
                </li>
                <li style={listItemContainer}>
                  <div style={infoItemLeft}>Hair:</div>
                  <div style={infoItemRight}>{hairColorMap[hairColor]}</div>
                </li>
              </ul>
          </div>
          {mutable ? (
              <div style={sm(styles.outerSection, xs && styles.outerSectionSm, xs && styles.profileDialogButton)} >
                <ProfileDialogButton profile={profile} mutable={mutable} />
              </div>
            ) : (
              <div style={sm(styles.outerSection, xs && styles.outerSectionSm)} >
                {'\u00A0'}
                {'\n'}
                {'\u00A0'}
              </div>
            )
          }
        </div>
      </div>
    );
  }
}

export default connect(
  state => ({
    sessionUnits: state.session.units,
  }),
  { })(MiniProfile)
