import React, { PropTypes } from 'react'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import FontIcon from 'material-ui/FontIcon'
import Avatar from 'material-ui/Avatar'
import IconButton from 'material-ui/IconButton'
import ImageAddAPhotoIcon from 'material-ui/svg-icons/image/add-a-photo'
import {List, ListItem} from 'material-ui/List';
import { white, grey200, grey400, grey600, blueGrey50 } from 'material-ui/styles/colors'

import theme from 'theme/mui-theme'
import * as sessionActions from 'redux/modules/session'
import * as profileActions from 'redux/modules/profile'
import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import Heading from 'components/Heading/Heading'
import Divider from 'components/Basic/Divider'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'

const { bool, string, array, object, oneOf } = PropTypes

const { primary2Color } = theme.palette

function makeValueLookUpMap(options = []) {
  return options.reduce((map, item) => ({ ...map, [item.value]: item.label }), {})
}

export const cityOptions = [
  { value: 'New York, USA', label: 'New York, USA' },
  { value: 'Paris, France', label: 'Paris, France' },
  { value: 'Tokyo, Japan', label: 'Tokyo, Japan' },
  { value: 'London, United Kingdom', label: 'London, United Kingdom' },
  { value: 'Seoul, South Korea', label: 'Seoul, South Korea' },
  { value: 'Milan, Italy', label: 'Milan, Italy' },
  { value: 'Copenhagen, Denmark', label: 'Copenhagen, Denmark' },
  { value: 'Los Angeles, USA', label: 'Los Angeles, USA' },
  { value: 'Chicago, USA', label: 'Chicago, USA' },
  { value: 'Stockholm, Sweden', label: 'Stockholm, Sweden' },
  { value: 'Antwerp, Belgum', label: 'Antwerp, Belgum' },
  { value: 'Berlin, Germany', label: 'Berlin, Germany' },
  { value: 'Hamburg, Germany', label: 'Hamburg, Germany' },
  { value: 'Munich, Germany', label: 'Munich, Germany' },
  { value: 'Vancouver, Canada', label: 'Vancouver, Canada' },
  { value: 'Toronto, Canada', label: 'Toronto, Canada' },
  { value: 'Moscow, Russia', label: 'Moscow, Russia' },
  { value: 'Amsterdam, Netherlands', label: 'Amsterdam, Netherlands' },
  { value: 'MelBourne, Australia', label: 'MelBourne, Australia' },
  { value: 'Sydney, Australia', label: 'Sydney, Australia' },
  { value: 'Rome, Italy', label: 'Rome, Italy' },
  { value: 'Madrid, Spain', label: 'Madrid, Spain' },
  { value: 'Florence, Italy', label: 'Florence, Italy' },
  { value: 'Hong Kong, China', label: 'Hong Kong, China' },
  { value: 'Singapore, Singapore', label: 'Singapore, Singapore' },
  { value: 'Dubai, United Arab Emirates', label: 'Dubai, United Arab Emirates' },
  { value: 'Miami, USA', label: 'Miami, USA' },
  { value: 'Dallas, USA', label: 'Dallas, USA' },
  { value: 'Boston, USA', label: 'Boston, USA' },
  { value: 'Las Vegas, USA', label: 'Las Vegas, USA' },
  { value: 'Prague, Czech Republic', label: 'Prague, Czech Republic' },
  { value: 'Vienna, Austria', label: 'Vienna, Austria' },
  { value: 'San Francisco, USA', label: 'San Francisco, USA' },
  { value: 'Cape Town, South Africa', label: 'Cape Town, South Africa' },
  { value: 'Montreal, Canada', label: 'Montreal, Canada' },
  { value: 'Mexico City, Mexico', label: 'Mexico City, Mexico' },
  { value: 'Barcelona, Spain', label: 'Barcelona, Spain' },
  { value: 'Buenos Aires, Argentina', label: 'Buenos Aires, Argentina' },
  { value: '', label: 'Other' },
]

export const cityMap = makeValueLookUpMap(cityOptions)

export const hairColorOptions = [
  { value: 'black', label: 'Black' },
  { value: 'brown', label: 'Brown' },
  { value: 'light brown', label: 'Light Brown' },
  { value: 'blond', label: 'Blond' },
  { value: 'red', label: 'Red' },
  { value: 'brunette', label: 'Brunette' },
  { value: 'grey', label: 'Grey' },
]

export const hairColorMap = makeValueLookUpMap(hairColorOptions)


export const eyeColorOptions = [
  { value: 'blue', label: 'Blue' },
  { value: 'brown', label: 'Brown' },
  { value: 'grey', label: 'Grey' },
  { value: 'green', label: 'Green' },
  { value: 'hazel', label: 'Hazel' },
]

export const eyeColorMap = makeValueLookUpMap(eyeColorOptions)

export const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
]

export const genderMap = makeValueLookUpMap(genderOptions)

export const ethnicityOptions = [
  { value: 'asian', label: 'Asian' },
  { value: 'black', label: 'Black' },
  { value: 'hispanic', label: 'Hispanic' },
  { value: 'white', label: 'White' },
]

export const ethnicityMap = makeValueLookUpMap(ethnicityOptions)

export const yearOptions = []
for (let year = new Date().getFullYear(); year >= 1900; year--) {
  yearOptions.push({ value: year, label: year.toString() })
}

export const yearMap = makeValueLookUpMap(yearOptions)

export const monthOptions = [
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
]

export const monthMap = makeValueLookUpMap(monthOptions)

export const bodyTypeOptions = [
  { value: 'slim', label: 'Slim' },
  { value: 'other', label: 'Someone should tell me what this list should be..' },
]

export const bodyTypeMap = makeValueLookUpMap(bodyTypeOptions)

export const bodyCupOptions = [
  { value: 'A', label: 'A' },
  { value: 'B', label: 'B' },
  { value: 'C', label: 'C' },
  { value: 'D', label: 'D' },
  { value: 'E', label: 'E' },
  { value: 'F', label: 'F' },
  { value: 'G', label: 'G' },
]

export const bodyCupMap = makeValueLookUpMap(bodyCupOptions)

export const femaleHeightOptions = [
  { value: 157, label: '5\'2” - 157cm' },
  { value: 159, label: '5\'2.5” - 159cm' },
  { value: 160, label: '5\'3” - 160cm' },
  { value: 161, label: '5\'3.5” - 161cm' },
  { value: 163, label: '5\'4” - 163cm' },
  { value: 164, label: '5\'4.5” - 164cm' },
  { value: 165, label: '5\'5” - 165cm' },
  { value: 166, label: '5\'5.5” - 166cm' },
  { value: 168, label: '5\'6" - 168cm' },
  { value: 169, label: '5\'6.5" - 169cm' },
  { value: 170, label: '5\'7" - 170cm' },
  { value: 171, label: '5\'7.5" - 171cm' },
  { value: 173, label: '5\'8" - 173cm' },
  { value: 174, label: '5\'8.5" - 174cm' },
  { value: 175, label: '5\'9" - 175cm' },
  { value: 177, label: '5\'9.5" - 177cm' },
  { value: 178, label: '5\'10" - 178cm' },
  { value: 179, label: '5\'10.5" - 179cm' },
  { value: 180, label: '5\'11" - 180cm' },
  { value: 182, label: '5\'11.5" - 182cm' },
  { value: 183, label: '6\'0" - 183cm' },
  { value: 184, label: '6\'0.5" - 184cm' },
  { value: 185, label: '6\'1" - 185cm' },
  { value: 187, label: '6\'1.5" - 187cm' },
  { value: 188, label: '6\'2" - 188cm' },
  { value: 189, label: '6\'2.5" - 189cm' },
  { value: 191, label: '6\'3" - 191cm' },
  { value: 192, label: '6\'3.5" - 192cm' },
]

export const femaleHeightMap = makeValueLookUpMap(femaleHeightOptions)

export const maleHeightOptions = [
  { value: 168, label: '5\'6" - 168cm' },
  { value: 169, label: '5\'6.5" - 169cm' },
  { value: 170, label: '5\'7" - 170cm' },
  { value: 171, label: '5\'7.5" - 171cm' },
  { value: 173, label: '5\'8" - 173cm' },
  { value: 174, label: '5\'8.5" - 174cm' },
  { value: 175, label: '5\'9" - 175cm' },
  { value: 177, label: '5\'9.5" - 177cm' },
  { value: 178, label: '5\'10" - 178cm' },
  { value: 179, label: '5\'10.5" - 179cm' },
  { value: 180, label: '5\'11" - 180cm' },
  { value: 182, label: '5\'11.5" - 182cm' },
  { value: 183, label: '6\'0" - 183cm' },
  { value: 184, label: '6\'0.5" - 184cm' },
  { value: 185, label: '6\'1" - 185cm' },
  { value: 187, label: '6\'1.5" - 187cm' },
  { value: 188, label: '6\'2" - 188cm' },
  { value: 189, label: '6\'2.5" - 189cm' },
  { value: 191, label: '6\'3" - 191cm' },
  { value: 192, label: '6\'3.5" - 192cm' },
  { value: 193, label: '6\'4" - 193cm' },
  { value: 194, label: '6\'4.5" - 194cm' },
  { value: 196, label: '6\'5" - 196cm' },
  { value: 197, label: '6\'5.5" - 197cm' },
  { value: 198, label: '6\'6" - 198cm' },
  { value: 199, label: '6\'6.5" - 199cm' },
  { value: 201, label: '6\'7" - 201cm' },
  { value: 202, label: '6\'7.5" - 202cm' },
]

export const maleHeightMap = makeValueLookUpMap(maleHeightOptions)

export const femaleDressOptions = [
  { value: 30.5, label: '0 - 30.5 EU' },
  { value: 31, label: '0.5 - 31 EU' },
  { value: 31.5, label: '1 - 31.5 EU' },
  { value: 32, label: '1.5 - 32 EU' },
  { value: 32.5, label: '2 - 32.5 EU' },
  { value: 33, label: '2.5 - 33 EU' },
  { value: 33.5, label: '3 - 33.5 EU' },
  { value: 34, label: '3.5 - 34 EU' },
  { value: 34.5, label: '4 - 34.5 EU' },
  { value: 35, label: '4.5 - 35 EU' },
  { value: 35.5, label: '5 - 35.5 EU' },
  { value: 36, label: '5.5 - 36 EU' },
  { value: 36.5, label: '6 - 36.5 EU' },
  { value: 37, label: '6.5 - 37 EU' },
  { value: 37.5, label: '7 - 37.5 EU' },
  { value: 38, label: '7.5 - 38 EU' },
  { value: 38.5, label: '8 - 38.5 EU' },
  { value: 39, label: '8.5 - 39 EU' },
  { value: 39.5, label: '9 - 39.5 EU' },
  { value: 40, label: '9.5 - 40 EU' },
  { value: 40.5, label: '10 - 40.5 EU' },
  { value: 41, label: '10.5 - 41 EU' },
  { value: 41.5, label: '11 - 41.5 EU' },
  { value: 42, label: '11.5 - 42 EU' },
  { value: 42.5, label: '12 - 42.5 EU' },
  { value: 43, label: '12.5 - 43 EU' },
  { value: 43.5, label: '13 - 43.5 EU' },
  { value: 44, label: '13.5 - 44 EU' },
  { value: 44.5, label: '14 - 44.5 EU' },
  { value: 45, label: '14.5 - 45 EU' },
  { value: 45.5, label: '15 - 45.5 EU' },
  { value: 46, label: '15.5 - 46 EU' },
  { value: 46.5, label: '16 - 46.5 EU' },
  { value: 47, label: '16.5 - 47 EU' },
  { value: 47.5, label: '17 - 47.5 EU' },
  { value: 48, label: '17.5 - 48 EU' },
  { value: 48.5, label: '18 - 48.5 EU' },
  { value: 49, label: '18.5 - 49 EU' },
  { value: 49.5, label: '19 - 49.5 EU' },
  { value: 50, label: '19.5 - 50 EU' },
]

export const femaleDressMap = makeValueLookUpMap(femaleDressOptions)

export const femaleWaistOptions = [
  { value: 48, label: '19” - 48cm' },
  { value: 50, label: '19.5” - 50cm' },
  { value: 51, label: '20” - 51cm' },
  { value: 52, label: '20.5” - 52cm' },
  { value: 53, label: '21” - 53cm' },
  { value: 55, label: '21.5” - 55cm' },
  { value: 56, label: '22” - 56cm' },
  { value: 57, label: '22.5” - 57cm' },
  { value: 58, label: '23” - 58cm' },
  { value: 60, label: '23.5” - 60cm' },
  { value: 61, label: '24” - 61cm' },
  { value: 62, label: '24.5” - 62cm' },
  { value: 64, label: '25” - 64cm' },
  { value: 65, label: '25.5” - 65cm' },
  { value: 66, label: '26” - 66cm' },
  { value: 67, label: '26.5” - 67cm' },
  { value: 69, label: '27” - 69cm' },
  { value: 70, label: '27.5” - 70cm' },
  { value: 71, label: '28” - 71cm' },
  { value: 72, label: '28.5” - 72cm' },
  { value: 74, label: '29” - 74cm' },
  { value: 75, label: '29.5” - 75cm' },
  { value: 76, label: '30” - 76cm' },
  { value: 77, label: '30.5” - 77cm' },
  { value: 79, label: '31” - 79cm' },
  { value: 80, label: '31.5” - 80cm' },
  { value: 81, label: '32” - 81cm' },
  { value: 83, label: '32.5” - 83cm' },
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
  { value: 98, label: '38.5” - 98cm' },
  { value: 99, label: '39” - 99cm' },
  { value: 100, label: '39.5” - 100cm' },
  { value: 102, label: '40” - 102cm' },
  { value: 103, label: '40.5” - 103cm' },
  { value: 104, label: '41” - 104cm' },
  { value: 105, label: '41.5” - 105cm' },
  { value: 107, label: '42” - 107cm' },
  { value: 108, label: '42.5” - 108cm' },
  { value: 109, label: '43” - 109cm' },
  { value: 110, label: '43.5” - 110cm' },
  { value: 112, label: '44” - 112cm' },
  { value: 113, label: '44.5” - 113cm' },
]

export const femaleWaistMap = makeValueLookUpMap(femaleWaistOptions)

export const maleWaistOptions = [
  { value: 56, label: '22” - 56cm' },
  { value: 57, label: '22.5” - 57cm' },
  { value: 58, label: '23” - 58cm' },
  { value: 60, label: '23.5” - 60cm' },
  { value: 61, label: '24” - 61cm' },
  { value: 62, label: '24.5” - 62cm' },
  { value: 64, label: '25” - 64cm' },
  { value: 65, label: '25.5” - 65cm' },
  { value: 66, label: '26” - 66cm' },
  { value: 67, label: '26.5” - 67cm' },
  { value: 69, label: '27” - 69cm' },
  { value: 70, label: '27.5” - 70cm' },
  { value: 71, label: '28” - 71cm' },
  { value: 72, label: '28.5” - 72cm' },
  { value: 74, label: '29” - 74cm' },
  { value: 75, label: '29.5” - 75cm' },
  { value: 76, label: '30” - 76cm' },
  { value: 77, label: '30.5” - 77cm' },
  { value: 79, label: '31” - 79cm' },
  { value: 80, label: '31.5” - 80cm' },
  { value: 81, label: '32” - 81cm' },
  { value: 83, label: '32.5” - 83cm' },
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
  { value: 98, label: '38.5” - 98cm' },
  { value: 99, label: '39” - 99cm' },
  { value: 100, label: '39.5” - 100cm' },
  { value: 102, label: '40” - 102cm' },
  { value: 103, label: '40.5” - 103cm' },
  { value: 104, label: '41” - 104cm' },
  { value: 105, label: '41.5” - 105cm' },
  { value: 107, label: '42” - 107cm' },
  { value: 108, label: '42.5” - 108cm' },
  { value: 109, label: '43” - 109cm' },
  { value: 110, label: '43.5” - 110cm' },
  { value: 112, label: '44” - 112cm' },
  { value: 113, label: '44.5” - 113cm' },
]

export const maleWaistMap = makeValueLookUpMap(maleWaistOptions)

export const femaleHipsOptions = [
  { value: 79, label: '31” - 79cm' },
  { value: 80, label: '31.5” - 80cm' },
  { value: 81, label: '32” - 81cm' },
  { value: 83, label: '32.5” - 83cm' },
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
  { value: 98, label: '38.5” - 98cm' },
  { value: 99, label: '39” - 99cm' },
  { value: 100, label: '39.5” - 100cm' },
  { value: 102, label: '40” - 102cm' },
  { value: 103, label: '40.5” - 103cm' },
  { value: 104, label: '41” - 104cm' },
  { value: 105, label: '41.5” - 105cm' },
  { value: 107, label: '42” - 107cm' },
  { value: 108, label: '42.5” - 108cm' },
  { value: 109, label: '43” - 109cm' },
  { value: 110, label: '43.5” - 110cm' },
  { value: 112, label: '44” - 112cm' },
  { value: 113, label: '44.5” - 113cm' },
  { value: 114, label: '45” - 114cm' },
  { value: 116, label: '45.5” - 116cm' },
  { value: 117, label: '46” - 117cm' },
  { value: 118, label: '46.5” - 118cm' },
  { value: 119, label: '47” - 119cm' },
  { value: 121, label: '47.5” - 121cm' },
  { value: 122, label: '48” - 122cm' },
  { value: 123, label: '48.5” - 123cm' },
  { value: 124, label: '49” - 124cm' },
  { value: 126, label: '49.5” - 126cm' },
]

export const femaleHipsMap = makeValueLookUpMap(femaleHipsOptions)

export const maleHipsOptions = [
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
  { value: 98, label: '38.5” - 98cm' },
  { value: 99, label: '39” - 99cm' },
  { value: 100, label: '39.5” - 100cm' },
  { value: 102, label: '40” - 102cm' },
  { value: 103, label: '40.5” - 103cm' },
  { value: 104, label: '41” - 104cm' },
  { value: 105, label: '41.5” - 105cm' },
  { value: 107, label: '42” - 107cm' },
  { value: 108, label: '42.5” - 108cm' },
  { value: 109, label: '43” - 109cm' },
  { value: 110, label: '43.5” - 110cm' },
  { value: 112, label: '44” - 112cm' },
  { value: 113, label: '44.5” - 113cm' },
  { value: 114, label: '45” - 114cm' },
  { value: 116, label: '45.5” - 116cm' },
  { value: 117, label: '46” - 117cm' },
  { value: 118, label: '46.5” - 118cm' },
]

export const maleHipsMap = makeValueLookUpMap(maleHipsOptions)

export const femaleBustOptions = [
  { value: 69, label: '27” - 69cm' },
  { value: 70, label: '27.5” - 70cm' },
  { value: 71, label: '28” - 71cm' },
  { value: 72, label: '28.5” - 72cm' },
  { value: 74, label: '29” - 74cm' },
  { value: 75, label: '29.5” - 75cm' },
  { value: 76, label: '30” - 76cm' },
  { value: 77, label: '30.5” - 77cm' },
  { value: 79, label: '31” - 79cm' },
  { value: 80, label: '31.5” - 80cm' },
  { value: 81, label: '32” - 81cm' },
  { value: 83, label: '32.5” - 83cm' },
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
  { value: 98, label: '38.5” - 98cm' },
  { value: 99, label: '39” - 99cm' },
  { value: 100, label: '39.5” - 100cm' },
  { value: 102, label: '40” - 102cm' },
  { value: 103, label: '40.5” - 103cm' },
  { value: 104, label: '41” - 104cm' },
  { value: 105, label: '41.5” - 105cm' },
  { value: 107, label: '42” - 107cm' },
  { value: 108, label: '42.5” - 108cm' },
  { value: 109, label: '43” - 109cm' },
  { value: 110, label: '43.5” - 110cm' },
  { value: 112, label: '44” - 112cm' },
  { value: 113, label: '44.5” - 113cm' },
  { value: 114, label: '45” - 114cm' },
  { value: 116, label: '45.5” - 116cm' },
  { value: 117, label: '46” - 117cm' },
  { value: 118, label: '46.5” - 118cm' },
]

export const femaleBustMap = makeValueLookUpMap(femaleBustOptions)

export const maleChestOptions = [
  { value: 79, label: '31” - 79cm' },
  { value: 80, label: '31.5” - 80cm' },
  { value: 81, label: '32” - 81cm' },
  { value: 83, label: '32.5” - 83cm' },
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
  { value: 98, label: '38.5” - 98cm' },
  { value: 99, label: '39” - 99cm' },
  { value: 100, label: '39.5” - 100cm' },
  { value: 102, label: '40” - 102cm' },
  { value: 103, label: '40.5” - 103cm' },
  { value: 104, label: '41” - 104cm' },
  { value: 105, label: '41.5” - 105cm' },
  { value: 107, label: '42” - 107cm' },
  { value: 108, label: '42.5” - 108cm' },
  { value: 109, label: '43” - 109cm' },
  { value: 110, label: '43.5” - 110cm' },
  { value: 112, label: '44” - 112cm' },
  { value: 113, label: '44.5” - 113cm' },
  { value: 114, label: '45” - 114cm' },
  { value: 116, label: '45.5” - 116cm' },
  { value: 117, label: '46” - 117cm' },
  { value: 118, label: '46.5” - 118cm' },
  { value: 119, label: '47” - 119cm' },
  { value: 121, label: '47.5” - 121cm' },
  { value: 122, label: '48” - 122cm' },
  { value: 123, label: '48.5” - 123cm' },
  { value: 124, label: '49” - 124cm' },
  { value: 126, label: '49.5” - 126cm' },
  { value: 127, label: '50” - 127cm' },
  { value: 128, label: '50.5” - 128cm' },
  { value: 130, label: '51” - 130cm' },
  { value: 131, label: '51.5” - 131cm' },
  { value: 132, label: '52” - 132cm' },
  { value: 133, label: '52.5” - 133cm' },
  { value: 135, label: '53” - 135cm' },
  { value: 136, label: '53.5” - 136cm' },
  { value: 137, label: '54” - 137cm' },
  { value: 138, label: '54.5” - 138cm' },
]

export const maleChestMap = makeValueLookUpMap(maleChestOptions)

export const femaleShoeOptions = [
  { value: 4.5, label: '4.5US - 35EU' },
  { value: 5, label: '5US - 36EU' },
  { value: 5.5, label: '5.5US - 36EU' },
  { value: 6, label: '6US - 37EU' },
  { value: 6.5, label: '6.5US - 37EU' },
  { value: 7, label: '7US - 38EU' },
  { value: 7.5, label: '7.5US - 39EU' },
  { value: 8, label: '8US - 39EU' },
  { value: 8.5, label: '8.5US - 40EU' },
  { value: 9, label: '9US - 40EU' },
  { value: 9.5, label: '9.5US - 41EU' },
  { value: 10, label: '10US - 41/42EU' },
  { value: 10.5, label: '10.5US - 42EU' },
  { value: 11, label: '11US - 43EU' },
  { value: 11.5, label: '11.5US - 43EU' },
  { value: 12, label: '12US - 44EU' },
]

export const femaleShoeMap = makeValueLookUpMap(femaleShoeOptions)

export const maleShoeOptions = [
  { value: 7, label: '7US - 40EU' },
  { value: 7.5, label: '7.5US - 40EU' },
  { value: 8, label: '8US - 41EU' },
  { value: 8.5, label: '8.5US - 41/42EU' },
  { value: 9, label: '9US - 42EU' },
  { value: 9.5, label: '9.5US - 43EU' },
  { value: 10, label: '10US - 43/44EU' },
  { value: 10.5, label: '10.5US - 44EU' },
  { value: 11, label: '11US - 44EU' },
  { value: 11.5, label: '11.5US - 45EU' },
  { value: 12, label: '12US - 46EU' },
  { value: 13, label: '13US - 47EU' },
  { value: 14, label: '14US - 48EU' },
  { value: 15, label: '15US - 49EU' },
]

export const maleShoeMap = makeValueLookUpMap(maleShoeOptions)

export const maleSuitOptions = [
  { value: 42, label: '32US - 42 EU' },
  { value: 44, label: '34US - 44 EU' },
  { value: 46, label: '36US - 46 EU' },
  { value: 48, label: '38US - 48 EU' },
  { value: 50, label: '40US - 50 EU' },
  { value: 52, label: '42US - 52 EU' },
  { value: 54, label: '44US - 54 EU' },
  { value: 56, label: '46US - 56 EU' },
  { value: 58, label: '48US - 58 EU' },
  { value: 60, label: '50US - 60 EU' },
]

export const maleSuitMap = makeValueLookUpMap(maleSuitOptions)

export const femaleInseamOptions = [
  { value: 64, label: '25” - 64cm' },
  { value: 65, label: '25.5” - 65cm' },
  { value: 66, label: '26” - 66cm' },
  { value: 67, label: '26.5” - 67cm' },
  { value: 69, label: '27” - 69cm' },
  { value: 70, label: '27.5” - 70cm' },
  { value: 71, label: '28” - 71cm' },
  { value: 72, label: '28.5” - 72cm' },
  { value: 74, label: '29” - 74cm' },
  { value: 75, label: '29.5” - 75cm' },
  { value: 76, label: '30” - 76cm' },
  { value: 77, label: '30.5” - 77cm' },
  { value: 79, label: '31” - 79cm' },
  { value: 80, label: '31.5” - 80cm' },
  { value: 81, label: '32” - 81cm' },
  { value: 83, label: '32.5” - 83cm' },
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
]

export const femaleInseamMap = makeValueLookUpMap(femaleInseamOptions)

export const maleInseamOptions = [
  { value: 64, label: '25” - 64cm' },
  { value: 65, label: '25.5” - 65cm' },
  { value: 66, label: '26” - 66cm' },
  { value: 67, label: '26.5” - 67cm' },
  { value: 69, label: '27” - 69cm' },
  { value: 70, label: '27.5” - 70cm' },
  { value: 71, label: '28” - 71cm' },
  { value: 72, label: '28.5” - 72cm' },
  { value: 74, label: '29” - 74cm' },
  { value: 75, label: '29.5” - 75cm' },
  { value: 76, label: '30” - 76cm' },
  { value: 77, label: '30.5” - 77cm' },
  { value: 79, label: '31” - 79cm' },
  { value: 80, label: '31.5” - 80cm' },
  { value: 81, label: '32” - 81cm' },
  { value: 83, label: '32.5” - 83cm' },
  { value: 84, label: '33” - 84cm' },
  { value: 85, label: '33.5” - 85cm' },
  { value: 86, label: '34” - 86cm' },
  { value: 88, label: '34.5” - 88cm' },
  { value: 89, label: '35” - 89cm' },
  { value: 90, label: '35.5” - 90cm' },
  { value: 91, label: '36” - 91cm' },
  { value: 93, label: '36.5” - 93cm' },
  { value: 94, label: '37” - 94cm' },
  { value: 95, label: '37.5” - 95cm' },
  { value: 97, label: '38” - 97cm' },
]

export const maleInseamMap = makeValueLookUpMap(maleInseamOptions)

const styles = {
  wrapper: {
    display: 'flex',
    flexWrap: 'wrap',
    alignContent: 'stretch',
    alignItems: 'stretch',
  },
  monochrome: {
    filter: 'contrast(0.9) grayscale(1)',
    WebkitFilter: 'contrast(0.9) grayscale(1)',
  },
  coverCard: {
    backgroundColor: white,
    width: 480,
  },
  cover: {
    backgroundColor: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    height: 100,
  },
  coverWrapper: {
    display: 'flex',
  },
  coverInfo: {
    flex: 1,
    textAlign: 'center',
    paddingTop: '0.5em',
  },
  avatar: {
    width: 200,
    height: 200,
    backgroundColor: primary2Color,
    backgroundSize: 'cover',
    backgroundPosition: 'top center',
    border: `1px solid ${white}`,
    borderRadius: '100%',
    marginTop: -100,
    zIndex: 1,
    position: 'relative',
  },
  coverName: {
    padding: '1em',
    textAlign: 'center',
  },
  contactButton: {
    marginTop: '2em',
  },
  social: {
    display: 'flex',
    justifyContent: 'center',
  },
  socialAvatar: {
    margin: '0.5em',
  },
  main: {
    flex: 1,
  },
  mainSm: {
    marginLeft: 24,
  },
  photos: {
    backgroundColor: blueGrey50,
  },
  photosGrid: {
    backgroundColor: 'none',
    padding: 0,
  },
  fileStyle: {
    position: 'relative',
    top: -20,
    left: -20,
    opacity: 0,
  },
  photoButton: {
    background: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 3,
    margin: 6,
  },
}

const defaultAvatar = require('../../../static/images/ic_face_white_24px.svg')
const defaultCover = require('../../../static/images/ic_landscape_white_48px.svg')

const ModelForm = ({ mutable, profile }, { mediaQueries: { xs } }) =>
  <div>
    <Form legend="Background Info" action={profileActions.save} data={profile}>
      <Field mutable={mutable} label="Name" name="name" fullWidth={xs} />
      <Field mutable={mutable} label="Nationality" name="nationality" fullWidth={xs} />

      <Field mutable={mutable} type="select" label="Gender" name="gender" options={genderOptions} fullWidth={xs} />
      <Field mutable={mutable} type="select" label="Year born" name="birthYear" options={yearOptions} fullWidth={xs} />

      <Field mutable={mutable} type="select" label="Current location" name="city" options={cityOptions} fullWidth={xs} />
      <Field mutable={mutable} type="toggle" label="Willing to travel" name="travelOk" style={{ paddingTop: '2em ' }} fullWidth={xs} />
    </Form>

    <Form legend="Measurements - Details" action={profileActions.save} data={profile}>
      <Field mutable={mutable} type="select" label="Height" name="height" options={profile.gender === 'female' ? femaleHeightOptions : maleHeightOptions} fullWidth={xs} />
      <Field mutable={mutable} type="select" label="Waist" name="waist" options={profile.gender === 'female' ? femaleWaistOptions : maleWaistOptions} fullWidth={xs} />

      <Field mutable={mutable} type="select" label="Hips" name="hips" options={profile.gender === 'female' ? femaleHipsOptions : maleHipsOptions} fullWidth={xs} />

      {
        profile.gender === 'male' ?
        <Field mutable={mutable} type="select" label="Chest" name="chest" options={maleChestOptions} fullWidth={xs} />
        : null
      }

      {
        profile.gender === 'female' ?
        <Field mutable={mutable} type="select" label="Bust" name="bust" options={profile.gender === 'female' ? femaleBustOptions : femaleBustOptions} fullWidth={xs} />
        : null
      }

      { profile.gender === 'female' ? <Field mutable={mutable} type="select" label="Dress size" name="dressSize" options={femaleDressOptions} fullWidth={xs} /> : null }
      { profile.gender === 'female' ? <Field mutable={mutable} type="select" label="Cup" name="cup" options={bodyCupOptions} fullWidth={xs} /> : null }

      { profile.gender === 'male' ? <Field mutable={mutable} type="select" label="Suit" name="suit" options={maleSuitOptions} fullWidth={xs} /> : null }
      <Field mutable={mutable} type="select" label="Inseam" name="inseam" options={profile.gender === 'female' ? femaleInseamOptions : maleInseamOptions} fullWidth={xs} />

      <Field mutable={mutable} type="select" label="Eye color" name="eyeColor" options={eyeColorOptions} fullWidth={xs} />
      <Field mutable={mutable} type="select" label="Shoe size" name="shoeSize" options={profile.gender === 'female' ? femaleShoeOptions : maleShoeOptions} fullWidth={xs} />

      <Field mutable={mutable} type="select" label="Hair color" name="hairColor" options={hairColorOptions} fullWidth={xs} />
      <Field mutable={mutable} label="Hair length" name="hairLength" fullWidth={xs} />

      <Field mutable={mutable} type="select" label="Ethnicity" name="ethnicity" options={ethnicityOptions} fullWidth={xs} />
      <Field mutable={mutable} label="Background" name="background" fullWidth={xs} />

      <Field mutable={mutable} type="checkbox" label="Piercings" name="piercings" fullWidth={xs} />
      <Field mutable={mutable} type="checkbox" label="Tattoos" name="tattoos" fullWidth={xs} />
    </Form>
  </div>

ModelForm.contextTypes = {
  mediaQueries: object,
}

const RepresentativeForm = ({ mutable, profile }, { mediaQueries: { xs } }) =>
  <div>
    <Form legend="Company Info" action={profileActions.save} data={profile}>
      <Field mutable={mutable} label="Company Name" name="companyName" fullWidth />
      <Field mutable={mutable} label="Company Address" name="fullAddress" fullWidth />
    </Form>

    <Form legend="Representative Info" action={profileActions.save} data={profile}>
      <Field mutable={mutable} label="Name" name="name" fullWidth={xs} />
      <Field mutable={mutable} type="select" label="Current location" name="city" options={cityOptions} fullWidth={xs} />

      <Field mutable={mutable} label="Current position" name="jobPosition" fullWidth={xs} />
      <Field mutable={mutable} type="date" label="Current position since" name="jobPositionSinceDate" fullWidth={xs} />

      <Field mutable={mutable} type="textarea" label="Brief Bio - Experience" name="bio" fullWidth />
    </Form>
  </div>

RepresentativeForm.contextTypes = {
  mediaQueries: object,
}

const ProfileForm = ({ sessionUnits, sessionSet, mutable, type, name, profile }) => {
  switch (type) {
    case 'representative':
      return <RepresentativeForm {...{ sessionUnits, sessionSet, mutable, name, profile }} />
    case 'model':
    default:
      return <ModelForm {...{ sessionUnits, sessionSet, mutable, name, profile }} />
  }
}

export const CoverName = ({ type, profile = { name: '', companyName: '' }, style = styles.coverName }) => {
  if (!profile) {
    return <span />
  }

  switch (type) {
    case 'representative':
      return (
        <div style={style}>
          <Heading text={profile.companyName} weight={400} size={1.52} margin />
          <Heading text={profile.name} />
          { /* <RaisedButton label="Contact" primary style={styles.contactButton} /> */}
        </div>
      )
    case 'model':
    default:
      return (
        <div style={style}>
          <Heading text={profile.name} weight={400} size={1.52} margin />
          <Heading text={type} style={{ textTransform: 'capitalize' }} />
          { /* <RaisedButton label="Contact" primary style={styles.contactButton} /> */}
        </div>
      )
  }
}

const Profile = ({ mutable, sessionUnits, sessionSet, type, avatar, cover, ...profile }, { mediaQueries: { xs } }) => (
  <div style={styles.wrapper}>
    <Paper style={styles.coverCard} zDepth={xs ? 0 : 1} rounded={xs ? false : true}>
      <div style={sm(styles.cover, styles.monochrome, { backgroundImage: `url('${cover && cover.length ? getMediaUrl(cover, 512) : defaultCover}')` })}>
        { mutable ?
          <IconButton tooltip="Select cover photo" touch tooltipPosition="bottom-right" style={styles.photoButton}>
            <ImageAddAPhotoIcon color={grey600} />
            <Form layout={false} action={profileActions.attachMedia} data={profile}>
              <Field mutable type="file" name="cover" layout={false} style={styles.fileStyle} accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff" />
            </Form>
          </IconButton>
          : ''
        }
      </div>
      <div style={styles.coverWrapper}>
        <div style={styles.coverInfo}>
          { /* <Heading text={<Rating max={5} value={4} />} weight={400} size={1} />
          <Heading text="Rating" weight={300} size={1} opacity={0.8} /> */ }
        </div>
        <div style={sm(styles.avatar, { backgroundImage: `url('${avatar && avatar.length ? getMediaUrl(avatar, 256) : defaultAvatar}')` })}>
          { mutable ?
            <IconButton tooltip="Select profile photo" touch tooltipPosition="bottom-right" style={sm(styles.photoButton, { margin: 32 })}>
              <ImageAddAPhotoIcon color={grey600} />
              <Form layout={false} action={profileActions.attachMedia} data={profile}>
                <Field mutable type="file" name="avatar" layout={false} style={styles.fileStyle} accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff" />
              </Form>
            </IconButton>
            : ''
          }
        </div>
        <div style={styles.coverInfo}>
          { /* <Heading text="12" weight={400} size={1} opacity={1} />
          <Heading text="Bookings" weight={300} size={1} opacity={0.8} /> */ }
        </div>
      </div>

      <CoverName type={type} profile={profile} />

      <div style={styles.social}>
        <Avatar
          icon={<FontIcon className="fa fa-facebook" />}
          color={grey400}
          backgroundColor={grey200}
          style={styles.socialAvatar}
        />
        <Avatar
          icon={<FontIcon className="fa fa-twitter" />}
          color={grey400}
          backgroundColor={grey200}
          style={styles.socialAvatar}
        />
        <Avatar
          icon={<FontIcon className="fa fa-google-plus" />}
          color={grey400}
          backgroundColor={grey200}
          style={styles.socialAvatar}
        />
      </div>
      { /*
      <List>
        <Subheader>Today</Subheader>
        <ListItem
          leftAvatar={<Avatar src={getMediaUrl(avatar)} />}
          primaryText="Brunch this weekend?"
          secondaryText={
            <p>
              <span style={{ color: darkBlack }}>{name}</span> --
              I&aposll be in your neighborhood doing errands this weekend. Do you want to grab brunch?
            </p>
          }
          secondaryTextLines={2}
        />
        <Divider />
      </List>
      */ }

    </Paper>

    <Paper style={sm(styles.main, !xs && styles.mainSm)} zDepth={xs ? 0 : 1} rounded={xs ? false : true}>
      <ProfileForm {...{ sessionUnits, sessionSet, mutable, type, profile }} />
    </Paper>
  </div>
)

Profile.propTypes = {
  sessionUnits: oneOf(['metric', 'imperial']),
  mutable: bool,
  name: string,
  avatar: string,
  cover: string,
  media: array,
}

Profile.defaultProps = {
  sessionUnits: 'metric',
  mutable: false,
  media: [],
}

Profile.contextTypes = {
  mediaQueries: object,
}

export default connect(
  state => ({
    sessionUnits: state.session.units,
  }),
  {
    sessionSet: sessionActions.set,
  })(Profile)
