import React, { Component, PropTypes } from 'react'
import ReactDOM from 'react-dom'
import IconButton from 'material-ui/IconButton'
import AvPlaylistAddIcon from 'material-ui/svg-icons/av/playlist-add'
import { white, grey200 } from 'material-ui/styles/colors'

import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'
import { DEFAULT_AVATAR } from '../../constants'

const { number, string, object, func } = PropTypes

const styles = {
  resultItem: {
    width: 'calc(50% - 12px)',
    margin: 5,
    position: 'relative',
    overflow: 'hidden',
    border: '1px solid #888',
    cursor: 'pointer',
  },
  resultItemSm: {
    width: 'calc(25% - 12px)',
  },
  resultItemMenu: {
    position: 'absolute',
    right: 0,
    zIndex: 1,
    display: 'none',
  },
  resultItemCover: {
    height: '100%',
    width: '100%',
    background: grey200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundImage: `url('${DEFAULT_AVATAR}')`,
  },
  resultItemInfo: {
    width: '100%',
    height: 125,
    background: 'rgba(255, 3, 3, 0.65)', // primary1Color,
    opacity: 0.8,
    transition: 'all 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms',
    position: 'absolute',
    bottom: -135,
    padding: 10,
    boxSizing: 'border-box',
    color: white,
    textShadow: '0px 0px 1px #000',
  },
  resultItemInfoHovered: {
    bottom: 0,
  },
}

export default class ResultItem extends Component {
  static propTypes = {
    id: string,
    slug: string,
    name: string,
    height: number,
    hairColor: string,
    eyeColor: string,
    avatar: string,
    tooltip: string,
    onItemTouchTap: func,
    onAddToListPromptOpen: func,
  };

  static contextTypes = {
    account: object,
    mediaQueries: object,
    mediaMatcher: object,
  };

  state = {
    hovered: false,
    width: 0,
  };

  componentDidMount() {
    ReactDOM.findDOMNode(this).addEventListener('mouseover', this::this.onMouseOver)
    ReactDOM.findDOMNode(this).addEventListener('mouseout', this::this.onMouseOut)
    window.addEventListener('resize', this::this.onResize)
  }

  shouldComponentUpdate(nextProps, nextState) {
    const { hovered, width } = this.state

    return nextState.hovered !== hovered || nextState.width !== width
  }

  componentWillUnmount() {
    ReactDOM.findDOMNode(this).removeEventListener('mouseover', this.onMouseOver)
    ReactDOM.findDOMNode(this).removeEventListener('mouseout', this.onMouseOut)
    window.removeEventListener('resize', this::this.onResize)
  }

  onResize() {
    this.forceUpdate()
  }

  onMouseOver() {
    this.setState({ hovered: true })
  }

  onMouseOut() {
    this.setState({ hovered: false })
  }

  makeRef = (div) => {
    if (!this.div && div) {
      this.div = div

      this.setState({ width: div.clientWidth })
    }
  };

  handleAddToListPromptOpen = (event) => {
    this.props.onAddToListPromptOpen()
    event.stopPropagation()
  };

  render() {
    const {
      account: { permissions: { representative: isRepresentative } },
      mediaMatcher: { matches: { xs } },
    } = this.context
    const { id, name, height, hairColor, eyeColor, avatar, tooltip, onItemTouchTap } = this.props
    const { hovered, width } = this.state
    const divHeight = width * 1.5016
    const avatarStyle = avatar && { backgroundImage: `url('${getMediaUrl(avatar)}')` } || {}

    return (
      <div
        key={id}
        style={sm(styles.resultItem, !xs && styles.resultItemSm, { height: divHeight })}
        ref={this.makeRef}
        onTouchTap={onItemTouchTap}
      >
        {isRepresentative &&
          <div
            style={sm(
              styles.resultItemMenu,
              hovered && { display: 'block' },
              xs && { display: 'block' },
            )}
          >
            <IconButton
              touch
              color={white}
              iconStyle={{ color: white }}
              tooltip={tooltip}
              tooltipPosition="bottom-left"
              onTouchTap={this.handleAddToListPromptOpen}
            >
              <AvPlaylistAddIcon color={white} />
            </IconButton>
          </div>}

        <div style={sm(styles.resultItemCover, styles.monochrome, avatarStyle)} />
        <div style={sm(styles.resultItemInfo, hovered && styles.resultItemInfoHovered)}>
          <strong>{name}</strong><br />
          {height}cm<br />
          Hair: {hairColor} <br />
          Eyes: {eyeColor}<br />
        </div>

      </div>
    )
  }
}
