import React, { PropTypes } from 'react'

import ToggleStarBorderIcon from 'material-ui/svg-icons/toggle/star-border'
import ToggleStarIcon from 'material-ui/svg-icons/toggle/star'

const { number } = PropTypes

const styles = {
  wrapper: {

  },
  icon: {
    width: '1em',
    height: '1em',
  },
}


const Icons = ({ max, value }) => {
  const icons = []

  for (let index = 1; index <= max; index++) {
    let icon

    if (index <= value) {
      icon = <ToggleStarIcon key={index} style={styles.icon} />
    } else {
      icon = <ToggleStarBorderIcon key={index} style={styles.icon} />
    }

    icons.push(icon)
  }

  return <div>{icons}</div>
}


const Rating = (props) => (
  <div style={styles.wrapper}>
    <Icons { ...props } />
  </div>
)

Rating.propTypes = {
  max: number,
  value: number,
}

Rating.defaultProps = {
  max: 5,
  value: 0,
}

export default Rating
