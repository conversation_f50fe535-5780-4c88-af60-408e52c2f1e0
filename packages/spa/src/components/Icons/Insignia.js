import React, { PropTypes } from 'react'
import SvgIcon from 'material-ui/SvgIcon'
import sm from 'helpers/stylesMerge'

const { string, oneOf } = PropTypes

const styles = {
  rect: {
    fill: '#fff',
  },
  light: {
    opacity: 0.75,
  },
  lighter: {
    opacity: 0.5,
  },
}

export default function InsigniaIcon(props) {
  const { color, corner, ...restProps } = props
  let viewBox = '0 0 160 160'

  if (corner === 'top-right') viewBox = '80 0 80 80'
  else if (corner === 'bottom-left') viewBox = '0 80 80 80'

  const svgProps = {
    ...restProps,
    color,
    viewBox,
  }

  styles.rect.fill = color

  return (
    <SvgIcon {...svgProps}>
      <g>
        { corner === 'none' || corner === 'bottom-left' ?
          <g>
            <rect style={styles.rect} x="48" y="88" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.light)} x="48" y="112" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.lighter)} x="48" y="136" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.light)} x="24" y="88" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.lighter)} y="88" width="24" height="24"/>
          </g>
          : null
        }
        { corner === 'none' || corner === 'top-right' ?
          <g>
            <rect style={styles.rect} x="88" y="48" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.light)} x="88" y="24" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.lighter)} x="88" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.light)} x="112" y="48" width="24" height="24"/>
            <rect style={sm(styles.rect, styles.lighter)} x="136" y="48" width="24" height="24"/>
          </g>
          : null
        }
      </g>
    </SvgIcon>
  )
}

InsigniaIcon.propTypes = {
  color: string,
  corner: oneOf(['none', 'top-right', 'bottom-left']),
}

InsigniaIcon.defaultProps = {
  color: '#fff',
  corner: 'none',
}
