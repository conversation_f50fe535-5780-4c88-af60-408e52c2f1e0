import React, { PropTypes } from 'react'
import { connect } from 'react-redux'

import sm from 'helpers/stylesMerge'
import {
  femaleHeightMap, femaleBustMap, femaleWaistMap, femaleHipsMap, femaleShoeMap,
  maleHeightMap, maleWaistMap, maleSuitMap, maleInseamMap, maleShoeMap,
  eyeColorMap, hairColorMap,
} from 'components/Profile/Profile'

const { object } = PropTypes

const styles = {
  gridListItem: {
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    boxSizing: 'border-box',
    margin: 3,
    position: 'relative',
    border: '1px solid #888',
  },
  portrait: {
    width: 'calc(25% - 6px)',
  },
  landscape: {
    width: '100%',
  },
  infoItem: {
    border: 0,
    fontSize: '120%',
  },
  infoItemList: {
    listStyle: 'none',
    margin: '0 auto',
    padding: 0,
  },
  infoItemH2: {
    borderBottom: '2px solid #333',
    paddingBottom: 5,
    margin: '0.5em auto',
    textAlign: 'center',
  },
  infoItemH2Sm: {
    width: '30%',
  },
  infoItemDivLeft: {
    display: 'inline-block',
    textAlign: 'right',
    paddingRight: '0.25em',
    fontStyle: 'italic',
    width: 'calc(45% - 0.25em)',
  },
  infoItemDivRight: {
    display: 'inline-block',
    textAlign: 'left',
    paddingLeft: '0.25em',
    width: 'calc(45% - 0.25em)',
  },
}

const _InfoItem = ({ name, gender, height, bust, waist, hips, suit, inseam, shoeSize, eyeColor, hairColor }, { mediaQueries: { xs } }) =>
  <div style={sm(styles.gridListItem, !xs && styles.portrait || styles.landscape, styles.infoItem)}>
    <h2 style={sm(styles.infoItemH2, !xs && styles.infoItemH2Sm)}>{name}</h2>
    { gender === 'female' ?
      <ul style={styles.infoItemList}>
        <li>
          <div style={styles.infoItemDivLeft}>Height</div>
          <div style={styles.infoItemDivRight}>{femaleHeightMap[height]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Bust</div>
          <div style={styles.infoItemDivRight}>{femaleBustMap[bust]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Waist</div>
          <div style={styles.infoItemDivRight}>{femaleWaistMap[waist]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Hips</div>
          <div style={styles.infoItemDivRight}>{femaleHipsMap[hips]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Shoes</div>
          <div style={styles.infoItemDivRight}>{femaleShoeMap[shoeSize]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Eyes</div>
          <div style={styles.infoItemDivRight}>{eyeColorMap[eyeColor]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Hair</div>
          <div style={styles.infoItemDivRight}>{hairColorMap[hairColor]}</div>
        </li>
      </ul>
      :
      <ul style={styles.infoItemList}>
        <li>
          <div style={styles.infoItemDivLeft}>Height</div>
          <div style={styles.infoItemDivRight}>{maleHeightMap[height]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Waist</div>
          <div style={styles.infoItemDivRight}>{maleWaistMap[waist]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Suit</div>
          <div style={styles.infoItemDivRight}>{maleSuitMap[suit]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Inseam</div>
          <div style={styles.infoItemDivRight}>{maleInseamMap[inseam]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Shoes</div>
          <div style={styles.infoItemDivRight}>{maleShoeMap[shoeSize]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Eyes</div>
          <div style={styles.infoItemDivRight}>{eyeColorMap[eyeColor]}</div>
        </li>
        <li>
          <div style={styles.infoItemDivLeft}>Hair</div>
          <div style={styles.infoItemDivRight}>{hairColorMap[hairColor]}</div>
        </li>
      </ul>
    }
  </div>

_InfoItem.contextTypes = {
  mediaQueries: object,
}

export default connect(
  state => ({
    sessionUnits: state.session.units,
  }),
  { })(_InfoItem)
