import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'

import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import Progress from 'components/Progress/Progress'

const { bool, number, string, object, any, func } = PropTypes

const styles = {
  gridListItem: {
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    boxSizing: 'border-box',
    margin: 3,
    position: 'relative',
  },
  portrait: {
    width: 'calc(25% - 6px)',
  },
  portraitSm: {
    width: 'calc(50% - 6px)',
  },
  landscape: {
    width: 'calc(25% - 6px)',
  },
  landscapeSm: {
    width: 'calc(50% - 6px)',
  },
}

export default class GridItem extends PureComponent {
  static propTypes = {
    id: any.isRequired,
    item: string.isRequired,
    index: number,
    mutable: bool,
    onGridItemClick: func,
  }

  static contextTypes = {
    mediaMatcher: object,
  }

  static defaultProps = {
    mutable: false,
  }

  state = {
    orientation: null,
    width: 0,
  }

  componentWillMount() {
    const { item } = this.props
    const src = getMediaUrl(item, 768)
    const image = new Image()

    image.onload = () => {
      const { width, height } = image
      const orientation = 'portrait'

      this.setState({ orientation })
    }

    image.src = src
  }

  componentDidMount() {
    window.addEventListener('resize', this.onResize.bind(this))
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.onResize)
  }

  onResize() {
    if (this.div) {
      this.setState({ width: this.div.clientWidth })
    }
  }

  makeRef = (div) => {
    if (!this.div && div) {
      this.div = div

      this.setState({ width: div.clientWidth })
    }
  }

  render() {
    const { mediaMatcher: { matches: { xs } } } = this.context

    const { item, index } = this.props
    const { width } = this.state
    let { orientation } = this.state

    if (orientation) {
      orientation = (index === 0 && 'portrait') || orientation
      const height = orientation === 'portrait' ? width * 1.2525 : width * 0.6368

      return (
        <div
          ref={this.makeRef}
          key={item}
          onClick={this.props.onGridItemClick}
          style={sm(
            styles.gridListItem,
            styles[orientation],
            xs && styles[`${orientation}Sm`],
            { height },
            { backgroundImage: `url('${getMediaUrl(item, 768)}')` },
            { cursor: 'pointer' },
          )}
        />
      )
    }

    return (
      <div
        style={sm(styles.gridListItem, styles.portrait, xs && styles.portraitSm, {
          border: 'none',
          cursor: 'default',
          height: styles.portrait.width,
          opacity: 1,
        })}
      >
        <Progress show style={{ height: 'auto' }} />
      </div>
    )
  }
}
