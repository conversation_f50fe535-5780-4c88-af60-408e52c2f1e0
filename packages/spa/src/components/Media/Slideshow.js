import React, { Component, PropTypes } from 'react'
import Lightbox from 'react-image-lightbox'
import getMediaUrl from 'helpers/getMediaUrl'
import noop from 'helpers/noop'

const { bool, number, arrayOf, string, func } = PropTypes

class Slideshow extends Component {
  static propTypes = {
    isOpen: bool,
    index: number,
    media: arrayOf(
      string.isRequired,
    ),
    onCloseLightbox: func,
  }

  static defaultProps = {
    isOpen: false,
    index: 0,
    media: [],
    onCloseLightbox: noop,
  }

  constructor(props) {
    super(props)

    this.state = {
      lightboxIsOpen: props.isOpen || false,
      index: props.index || 0,
      images: this.getImages(props),
    }

    this.closeLightbox = this.closeLightbox.bind(this)
    this.gotoNext = this.gotoNext.bind(this)
    this.gotoPrevious = this.gotoPrevious.bind(this)
    this.gotoImage = this.gotoImage.bind(this)
    this.handleClickImage = this.handleClickImage.bind(this)
    this.openLightbox = this.openLightbox.bind(this)
  }

  componentWillReceiveProps(nextProps) {
    const { isOpen, index } = nextProps

    let newState = {}

    if (isOpen) { newState.lightboxIsOpen = true }
    if (index) { newState.currentImage = index }

    this.setState(newState)
  }

  getImages() {
    const { media } = this.props

    if (!media) { return [] }

    return (media.map && media.map((image) => getMediaUrl(image, 2048)))
  }

  openLightbox(index, event) {
    event.preventDefault()
    this.setState({
      currentImage: index,
      lightboxIsOpen: true,
    })
  }
  closeLightbox() {
    this.props.onCloseLightbox()
    this.setState({
      currentImage: 0,
      lightboxIsOpen: false,
    })
  }
  gotoPrevious() {
    this.setState({
      currentImage: this.state.currentImage - 1,
      index: (this.state.index + this.state.images.length - 1) % this.state.images.length,
    })
  }
  gotoNext() {
    this.setState({
      index: (this.state.index + 1) % this.state.images.length,
    })
  }
  gotoImage(index) {
    this.setState({
      currentImage: index,
    })
  }
  gotoStart() {
    this.setState({
      currentImage: 0,
    })
  }
  handleClickImage() {
    if (this.state.currentImage === this.state.images.length - 1) {
      this.gotoStart()
    } else {
      this.gotoNext()
    }
  }

  render() {
    const isOpen = this.state.lightboxIsOpen
    const images = this.state.images || []
    const index = this.state.index

    return (
      isOpen ? (
        <div className="section">
          <Lightbox
            mainSrc={images[index]}
            nextSrc={images[(index + 1) % images.length]}
            prevSrc={images[(index + images.length - 1) % images.length]}
            onCloseRequest={this.closeLightbox}
            onClose={this.closeLightbox}
            onMovePrevRequest={this.gotoPrevious}
            onMoveNextRequest={this.gotoNext}
            reactModalStyle={{
              overlay: { zIndex: 2001 },
            }}
          />
        </div>
      ) : null
    )
  }
}

Slideshow.displayName = 'Slideshow'

export default Slideshow
