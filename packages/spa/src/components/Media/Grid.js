import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import update from 'immutability-helper'
import { DragDropContext as dragDropContext, DropTarget as dropTarget } from 'react-dnd'
import HTML5Backend from 'react-dnd-html5-backend'
import { connect as reduxConnect } from 'react-redux'
import { white, blueGrey200 } from 'material-ui/styles/colors'

import sm from 'helpers/stylesMerge'
import * as profileActions from 'redux/modules/profile'
import GridItem from 'components/Media/GridItem'
import DndGridItem from 'components/Media/DndGridItem'
import UploadProgress from 'components/Progress/Upload'

const { bool, array, object, func } = PropTypes

const styles = {
  wrapper: {},
  item: {
    flex: '0 0 auto',
    background: '#fafafa',
    borderRadius: 3,
    border: `1px solid rgb(${blueGrey200})`,
    margin: 6,
  },
  gridList: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignContent: 'flex-end',
    alignItems: 'center',
    position: 'relative',
  },
  upload: {
    textAlign: 'center',
    position: 'absolute',
    top: 0,
    right: 0,
  },
  uploadForm: {
    display: 'none',
  },
  iconButton: {
    width: 'initial',
    height: 'initial',
  },
  icon: {
    width: 'auto',
    height: '100%',
    display: 'block',
    margin: '0 auto',
  },
}

const itemTarget = {
  drop() {},
}

@dragDropContext(HTML5Backend)
@dropTarget('mediaItem', itemTarget, connect => ({
  connectDropTarget: connect.dropTarget(),
}))
@reduxConnect(() => ({}), {
  profileSave: profileActions.save,
})
class MutableGrid extends PureComponent {
  static propTypes = {
    items: array,
    profile: object,
    profileSave: func,
    mutable: bool,
  }

  static contextTypes = {
    mediaQueries: object,
  }

  state = {
    items: [...this.props.items],
  }

  componentWillReceiveProps(nextProps) {
    const { items } = this.props
    const { items: nextItems } = nextProps

    if (items !== nextItems) {
      this.setState({ items: nextItems })
    }
  }

  findItem = (id) => {
    const { items } = this.state
    const foundItem = items.filter(item => item === id)[0]

    return {
      item: foundItem,
      index: items.indexOf(foundItem),
    }
  }

  moveItem = (id, atIndex) => {
    const { profileSave, profile: { id: profileId } } = this.props
    const { item, index } = this.findItem(id)

    const newState = update(this.state, {
      items: {
        $splice: [[index, 1], [atIndex, 0, item]],
      },
    })

    const media = newState.items.reduce(
      (items, current) => (current.id ? items : [...items, current]),
      [],
    )

    this.setState(newState)
    profileSave({ media, id: profileId })
  }

  // handleOpenFileSelector = () => document.getElementById('file-upload-media').click()

  render() {
    const { mediaQueries: { xs } } = this.context
    const { profile } = this.props
    const { items } = this.state

    return (
      <div style={styles.gridList}>
        {/* <div style={styles.upload}>
          <IconButton style={styles.iconButton} onTouchTap={this.handleOpenFileSelector}>
            <FileFileUploadIcon style={styles.icon} color={accent3Color} />
            <Form style={styles.uploadForm} layout={false} action={profileActions.attachMedia} data={profile}>
              <Field mutable type="file" name="media" id="file-upload-media" layout={false} accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff" />
            </Form>
            <Heading size={1} text="Upload photo" style={{ fontSize: 14 }}/>
          </IconButton>
        </div> */}

        {items.map(
          (item, index) =>
            item.progress
              ? <UploadProgress
                key={item.uploadId}
                style={{
                  width: xs ? 'calc(50% - 6px)' : 'calc(25% - 6px)',
                }}
                {...item}
              />
              : <DndGridItem
                key={item}
                index={index}
                id={item}
                item={item}
                profile={profile}
                findItem={this.findItem}
                moveItem={this.moveItem}
              />,
        )}
      </div>
    )
  }
}

const ImmutableGrid = ({ items, onClick }) => (
  <div style={styles.gridList}>
    {items.map(
      (item, index) =>
        item.id
          ? null
          : <GridItem key={item} onGridItemClick={() => { onClick(index) }} index={index} id={item} item={item} mutable={false} />,
    )}
  </div>
)

const MediaGrid = ({ rootStyle, mutable, media, profile, onGridItemClick }) => (
  <div style={sm(styles.wrapper, rootStyle)}>
    {mutable
      ? <MutableGrid mutable items={media} profile={profile} />
      : <ImmutableGrid items={media} profile={profile} onClick={onGridItemClick} />}
  </div>
)

MediaGrid.propTypes = {
  mutable: bool,
  rootStyle: object,
  media: array,
  onGridItemClick: func,
}

MediaGrid.defaultProps = {
  mutable: false,
  rootStyle: {},
  media: [],
}

export default MediaGrid
