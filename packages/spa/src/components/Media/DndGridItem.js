import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import { DragSource as dragSource, DropTarget as dropTarget } from 'react-dnd'
import IconButton from 'material-ui/IconButton'
import ActionDeleteIcon from 'material-ui/svg-icons/action/delete'

import theme from 'theme/mui-theme'
import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import * as profileActions from 'redux/modules/profile'
import Progress from 'components/Progress/Progress'

const { bool, number, string, object, func, any } = PropTypes

const { accent3Color } = theme.palette

const styles = {
  gridListItem: {
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    boxSizing: 'border-box',
    margin: 3,
    position: 'relative',
  },
  itemToolbar: {
    backgroundColor: accent3Color,
    position: 'absolute',
    top: 10,
    right: 10,
    opacity: 0.9,
    borderRadius: 3,
  },
  iconButton: {
    padding: '32px 40px',
    width: 'initial',
    height: 'initial',
  },
  icon: {
    width: 'auto',
    height: '100%',
    display: 'block',
    margin: '0 auto',
  },
  image: {
    width: '100%',
  },
  portrait: {
    width: 'calc(25% - 6px)',
  },
  portraitSm: {
    width: 'calc(50% - 6px)',
  },
  landscape: {
    width: 'calc(25% - 6px)',
  },
  landscapeSm: {
    width: 'calc(50% - 6px)',
  },
}

const MediaItemToolbar = ({ profileId, mediaId }, { store: { dispatch } }) => {
  const handleButtonClick = () => {
    dispatch(profileActions.detachMedia(profileId, 'media', mediaId))
  }

  return (
    <div style={styles.itemToolbar}>
      <IconButton onTouchTap={handleButtonClick}><ActionDeleteIcon color="white" /></IconButton>
    </div>
  )
}

MediaItemToolbar.contextTypes = {
  store: object,
}

const itemSource = {
  beginDrag(props) {
    return {
      id: props.id,
      originalIndex: props.findItem(props.id).index,
    }
  },

  endDrag(props, monitor) {
    // const { id: droppedId, originalIndex } = monitor.getItem()
    const didDrop = monitor.didDrop()

    if (!didDrop) {
      // props.moveItem(droppedId, originalIndex)
    }
  },
}

const itemTarget = {
  canDrop() {
    return false
  },

  hover(props, monitor) {
    const { id: draggedId } = monitor.getItem()
    const { id: overId } = props

    if (draggedId !== overId) {
      const { index: overIndex } = props.findItem(overId)
      props.moveItem(draggedId, overIndex)
    }
  },
}

@dropTarget('mediaItem', itemTarget, _connect => ({
  connectDropTarget: _connect.dropTarget(),
}))
@dragSource('mediaItem', itemSource, (_connect, monitor) => ({
  connectDragSource: _connect.dragSource(),
  isDragging: monitor.isDragging(),
}))
export default class DndGridItem extends PureComponent {
  static propTypes = {
    connectDragSource: func.isRequired,
    connectDropTarget: func.isRequired,
    isDragging: bool.isRequired,
    id: any.isRequired,
    item: string.isRequired,
    profile: object.isRequired,
    moveItem: func.isRequired,
    findItem: func.isRequired,
    mutable: bool,
    index: number,
  }

  static contextTypes = {
    mediaMatcher: object,
  }

  static defaultProps = {
    mutable: false,
  }

  state = {
    orientation: null,
    width: 0,
  }

  componentWillMount() {
    const { item } = this.props
    const src = getMediaUrl(item, 768)
    const image = new Image()

    image.onload = () => {
      const { width, height } = image
      const orientation = 'portrait'

      this.setState({ orientation })
    }

    image.src = src
  }

  componentDidMount() {
    window.addEventListener('resize', this.onResize.bind(this))
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.onResize)
  }

  onResize() {
    if (this.div) {
      this.setState({ width: this.div.clientWidth })
    }
  }

  makeRef = (div) => {
    if (!this.div && div) {
      this.div = div

      this.setState({ width: div.clientWidth })
    }
  }

  render() {
    const { mediaMatcher: { matches: { xs } } } = this.context

    const { item, profile, isDragging, connectDragSource, connectDropTarget, index } = this.props
    const { width } = this.state
    const opacity = isDragging ? 0 : 1
    let { orientation } = this.state

    if (orientation) {
      orientation = (index === 0 && 'portrait') || orientation
      const height = orientation === 'portrait' ? width * 1.2525 : width * 0.6368

      return connectDragSource(
        connectDropTarget(
          <div
            ref={this.makeRef}
            key={item}
            style={sm(
              styles.gridListItem,
              styles[orientation],
              xs && styles[`${orientation}Sm`],
              { height, opacity },
              { cursor: 'move', backgroundImage: `url('${getMediaUrl(item, 768)}')` },
            )}
          >
            {/* <img src={getMediaUrl(item)} style={styles.image} /> */}
            <MediaItemToolbar profileId={profile.id} mediaId={item} />
          </div>,
        ),
      )
    }

    return (
      <div
        style={sm(styles.gridListItem, styles.portrait, xs && styles.portraitSm, {
          border: 'none',
          cursor: 'default',
          height: styles.portrait.width,
          opacity: 1,
        })}
      >
        <Progress show style={{ height: 'auto' }} />
      </div>
    )
  }
}
