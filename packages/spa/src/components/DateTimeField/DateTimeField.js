import React, { Component, PropTypes } from 'react'
import DatePicker from 'material-ui/DatePicker'
import TimePicker from 'material-ui/TimePicker'
import IconButton from 'material-ui/IconButton'
import EditorFormatClearIcon from 'material-ui/svg-icons/editor/format-clear'
import { grey200, grey300, grey500 } from 'material-ui/styles/colors'

import noop from 'helpers/noop'
import sm from 'helpers/stylesMerge'

const { string, object, func } = PropTypes

const styles = {
  dateTimeFields: {
    display: 'flex',
  },
  dateTimeField: {
    width: '50%',
  },
  dateInputField: {
    marginRight: '0.5em',
  },
  timeInputField: {
    marginLeft: '0.5em',
  },
  label: {
    fontSize: '80%',
    color: grey300,
  },
}

export default class DateTimeField extends Component {
  static propTypes = {
    label: string,
    value: object,
    onChange: func,
  }

  static defaultProps = {
    value: null,
    label: 'Date & time',
    onChange: noop,
  }

  state = {
    value: this.props.value,
  }

  componentWillReceiveProps(nextProps) {
    const { value } = this.props
    const { value: nextValue } = nextProps

    if (value !== nextValue) {
      this.setState({ value: nextValue })
    }
  }

  handleDateChange = (event, value) => {
    this.setState({ value })
    this.props.onChange(value)
  }

  handleClearFields = () => {
    this.setState({ value: null })
    this.props.onChange(undefined)
  }

  render() {
    const { label } = this.props
    const { value } = this.state

    return (
      <div>
        <label style={styles.label}>{label}</label>

        <div style={styles.dateTimeFields}>
          <DatePicker
            DateTimeFormat={Intl.DateTimeFormat}
            hintText="Date"
            style={sm(styles.dateTimeField, styles.dateInputField)}
            value={value}
            onChange={this.handleDateChange}
            placeholder="Date"
            fullWidth
          />
          <TimePicker
            format="24hr"
            hintText="Time"
            pedantic
            value={value}
            style={sm(styles.dateTimeField, styles.timeInputField)}
            onChange={this.handleDateChange}
            placeholder="Time"
            fullWidth
          />
          <IconButton touch onTouchTap={this.handleClearFields}>
            <EditorFormatClearIcon color={value ? grey500 : grey200} />
          </IconButton>
        </div>
      </div>
    )
  }
}
