import React, { PureComponent, PropTypes } from 'react'
import { connect } from 'react-redux'
import Snackbar from 'material-ui/Snackbar'

import * as snackbarActions from 'redux/modules/snackbar'

const { bool, number, string, func } = PropTypes

@connect(
  state => ({
    open: state.snackbar.open,
    message: state.snackbar.message,
  }), {
    ...snackbarActions,
  })
export default class UiSnackbar extends PureComponent {
  static propTypes = {
    open: bool,
    message: string,
    autoHideDuration: number,
    hide: func,
  }

  static defaultProps = {
    open: false,
    message: '',
    autoHideDuration: 6000,
  }

  render() {
    const { open, message, autoHideDuration, hide } = this.props

    return (
      <Snackbar
        open={open}
        message={message}
        autoHideDuration={autoHideDuration}
        onRequestClose={hide}
        onActionTouchTap={hide}
        action="Dismiss"
      />
    )
  }
}
