import React from 'react'
import { IndexRoute, Route, Redirect } from 'react-router'

import {
  isLoaded as isSessionLoaded,
  load as loadSession,
  destroy as destroySession,
} from 'redux/modules/session'
import {
  isLoaded as isAccountLoaded,
  load as loadAccount,
  reset as resetAccount,
} from 'redux/modules/account'
import { reset as resetProfile } from 'redux/modules/profile'
import App from 'containers/App/App'
import NotFound from 'containers/NotFound/NotFound'
import Dashboard from 'containers/Dashboard/Dashboard'
import ProfileSearch from 'containers/Profile/Search'
import Profile from 'containers/Profile/Profile'
import ProjectsList from 'containers/Projects/List'
import Project from 'containers/Projects/Project'
import ProjectDashboard from 'containers/Projects/Dashboard'
import ProjectEdit from 'containers/Projects/Edit'
import TalentList from 'containers/Projects/TalentList'
import ProjectCastingEdit from 'containers/Projects/CastingEdit'
import ProjectBookingEdit from 'containers/Projects/BookingEdit'
import AdminProfiles from 'containers/Admin/Profiles'
import AdminJobs from 'containers/Admin/Jobs'
import AdminJob from 'containers/Admin/Job'
import JobsList from 'containers/Jobs/List'
import Job from 'containers/Jobs/Job'
import JobDetails from 'containers/Jobs/Details'

export default (store) => {
  const requireSession = (nextState, replaceState, cb) => {
    function checkToken() {
      const { session } = store.getState()

      if (session.token && !session.tokenExpired && !isAccountLoaded(store.getState())) {
        store.dispatch(loadAccount(session.account)).then(() => cb()).catch(() => cb())
      } else {
        cb()
      }
    }

    if (!isSessionLoaded(store.getState())) {
      store.dispatch(loadSession()).then(checkToken)
    } else {
      checkToken()
    }
  }

  const requireAdmin = (nextState, replaceState, cb) => {
    const { account: { data: account } } = store.getState()

    if (!account || !account.permissions.admin) {
      replaceState('/')
    }

    cb()
  }

  const requireProfile = (nextState, replaceState, cb) => {
    const { session } = store.getState()

    function checkProfile() {
      const { account: { data: account } } = store.getState()

      if (!account.profiles || account.profiles.length === 0 || account.permissions.admin) {
        replaceState('/')
      }

      cb()
    }

    if (!isAccountLoaded(store.getState())) {
      store.dispatch(loadAccount(session.account)).then(checkProfile)
    } else {
      checkProfile()
    }
  }

  const logout = (nextState, replaceState, cb) => {
    Promise.all([
      store.dispatch(destroySession()),
      store.dispatch(resetAccount()),
      store.dispatch(resetProfile()),
    ]).then(() => {
      replaceState('/')
      cb()
    })
  }

  return (
    <Route path="/" component={App} onEnter={requireSession}>
      <IndexRoute component={Dashboard} />

      <Route path="logout" component={Dashboard} onEnter={logout} />

      <Route path="profile" component={Profile} onEnter={requireProfile} />
      <Route path="profile/:tabSlug" component={Profile} onEnter={requireProfile} />

      <Route path="search" component={ProfileSearch} />
      <Route path="search/:profileType" component={ProfileSearch} />

      <Route path="projects" component={ProjectsList} />
      {/* <Route path="projects/dump" component={() => <div>Dump Component</div>} /> */}
      <Route path="projects/:projectId/:listType/:listId/search" component={ProfileSearch} />

      <Route path="projects/:projectId" component={Project}>
        <IndexRoute component={ProjectDashboard} />
        <Route path="edit" component={ProjectEdit} />

        <Redirect from="casting" to="castings" />
        <Redirect from="booking" to="bookings" />
        <Route path=":castingsOrBookings" component={ProjectDashboard} />

        <Route path="casting/create" component={ProjectCastingEdit} />
        <Route path="booking/create" component={ProjectBookingEdit} />
        <Route path="casting/:castingId/edit" component={ProjectCastingEdit} />
        <Route path="booking/:bookingId/edit" component={ProjectBookingEdit} />
        <Route path=":listType/:listId" component={TalentList} />
        <Route path=":listType/:listId/:filter" component={TalentList} />
      </Route>

      <Route path="jobs" component={JobsList} />
      <Route path="jobs/:filter" component={JobsList} />
      <Route path="my-jobs" component={JobsList} />
      <Route path="my-jobs/:filter" component={JobsList} />

      <Route path="job/:jobId" component={Job}>
        <IndexRoute component={JobDetails} />

        <Route path="casting/:castingId" component={JobDetails} />
      </Route>

      <Route path="admin/approve/profiles/:type" component={AdminProfiles} onEnter={requireAdmin} />
      <Route path="admin/approve/jobs" component={AdminJobs} onEnter={requireAdmin} />

      <Route path="admin/approve/job/:jobId" component={AdminJob}>
        <IndexRoute component={JobDetails} />
      </Route>

      <Route path=":profileSlug" component={Profile} />
      <Route path=":profileSlug/:tabSlug" component={Profile} />
      <Route path="*" component={NotFound} status={404} />

    </Route>
  )
}
