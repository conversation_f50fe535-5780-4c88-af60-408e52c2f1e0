import React from 'react'
import PropTypes from 'prop-types'
import MuiThemeProvider from 'material-ui/styles/MuiThemeProvider'
import getMuiTheme from 'material-ui/styles/getMuiTheme'
import { white } from 'material-ui/styles/colors'

import theme from 'theme/mui-theme'
import sm from 'helpers/stylesMerge'
import CompCard from 'containers/Profile/CompCard'

const { object, arrayOf } = PropTypes

const muiTheme = getMuiTheme(theme)

const styles = {
  wrapper: {
    backgroundColor: white,
  },
}

export default function CompCardApp({ data }) {
  console.log('some sweet data.', data)
  return (
    <MuiThemeProvider muiTheme={muiTheme}>
      <div style={styles.wrapper}>
        <CompCard printable mutable={false} profile={data} />
      </div>
    </MuiThemeProvider>
  )
}

CompCardApp.propTypes = {
  errors: arrayOf(object),
  data: object.isRequired,
}

CompCardApp.defaultProps = {
  errors: [],
}
