import React, { Component, PropTypes, Children } from 'react'
import { connect } from 'react-redux'
import DocumentMeta from 'react-document-meta'
import MuiThemeProvider from 'material-ui/styles/MuiThemeProvider'
import getMuiTheme from 'material-ui/styles/getMuiTheme'

import config from 'config'
import theme from 'theme/mui-theme'
import Login from 'containers/Login/Login'
import NavDrawer from 'components/Navigation/NavDrawer'
import MediaQueryContext from 'components/MediaQueryContext/MediaQueryContext'
import Snackbar from 'components/Snackbar/Snackbar'
import ConfirmPrompt from 'components/Dialogs/Confirm'

const { object } = PropTypes

const muiTheme = getMuiTheme(theme)

@connect(
  state => ({
    session: state.session,
    account: state.account.data,
    confirmPrompt: state.confirmPrompt,
  }),
  {},
)
export default class App extends Component {
  static propTypes = {
    children: object.isRequired,
    session: object,
    account: object,
    confirmPrompt: object,
  };

  static contextTypes = {
    store: PropTypes.object.isRequired,
    router: PropTypes.object,
  };

  static childContextTypes = {
    session: object,
    account: object,
  };

  state = {
    navDrawerOpen: false,
  };

  getChildContext() {
    return {
      session: this.props.session || {},
      account: this.props.account || {},
    }
  }

  handleDrawerNavToggle = () => this.setState({ navDrawerOpen: !this.state.navDrawerOpen });

  renderChildren = () =>
    Children.map(this.props.children, child =>
      React.cloneElement(child, { handleDrawerNavToggle: this.handleDrawerNavToggle }));

  render() {
    const { session, account, confirmPrompt } = this.props

    return (
      <MuiThemeProvider muiTheme={muiTheme}>
        <MediaQueryContext>
          <DocumentMeta {...config.app} />

          <NavDrawer open={this.state.navDrawerOpen} onRequestChange={this.handleDrawerNavToggle} />

          {session.token && !session.tokenExpired && account ? this.renderChildren() : <Login />}

          <ConfirmPrompt {...confirmPrompt} />

          <Snackbar />
        </MediaQueryContext>
      </MuiThemeProvider>
    )
  }
}
