import React, { Component, PropTypes } from 'react'
import AppBar from 'material-ui/AppBar'
import TextField from 'material-ui/TextField'
import IconButton from 'material-ui/IconButton'
import FlatButton from 'material-ui/FlatButton'
import ImagePortraitIcon from 'material-ui/svg-icons/image/portrait'
import ActionWorkIcon from 'material-ui/svg-icons/action/work'
import ActionDashboardIcon from 'material-ui/svg-icons/action/dashboard'
import SocialPeopleIcon from 'material-ui/svg-icons/social/people'
import AvRecentActorsIcon from 'material-ui/svg-icons/av/recent-actors'

import theme from 'theme/mui-theme'
import stylesMerge from 'helpers/stylesMerge'
import BrandLogoIcon from 'components/Icons/Logo'

const { primary1Color } = theme.palette

const { object } = PropTypes

const appBarHeight = 67

export const styles = {
  logo: {
    padding: 0,
    fontSize: 11,
    width: '4.5em',
    height: '4.5em',
    display: 'block',
  },
  appBar: {
    position: 'fixed',
    top: 0,
    flexWrap: 'wrap',
    paddingLeft: '8%',
    paddingRight: '8%',
    borderBottom: `2px solid ${primary1Color}`,
    boxShadow: 'none',
    height: appBarHeight,
  },
  appBarSpacer: {
    paddingTop: appBarHeight,
  },
  appBarSearchContainer: {
    position: 'fixed',
    zIndex: 2000,
    width: '20%',
    margin: 'auto',
    left: 0,
    right: 0,
  },
  appBarSearchContainerSm: {
    display: 'none',
  },
  appBarSearch: {},
  iconStyle: {
    color: primary1Color,
  },
}

export class AppBarSearch extends Component {
  render() {
    return <TextField style={styles.appBarSearch} hintText={'Search'} />
  }
}

const LogoIcon = (
  <IconButton style={{ padding: 0, margin: 0 }} iconStyle={styles.logo}>
    <BrandLogoIcon />
  </IconButton>
)

const DashBoardIcon = (
  <IconButton href="#/" iconStyle={styles.iconStyle}>
    <ActionDashboardIcon />
  </IconButton>
)

const ProfileIcon = (
  <IconButton href="#/profile" iconStyle={styles.iconStyle}>
    <ImagePortraitIcon />
  </IconButton>
)

const JobsIcon = (
  <IconButton href="#/my-jobs" iconStyle={styles.iconStyle}>
    <ActionWorkIcon />
  </IconButton>
)

const ProjectsIcon = (
  <IconButton href="#/projects" iconStyle={styles.iconStyle}>
    <AvRecentActorsIcon />
  </IconButton>
)

const SearchIcon = (
  <IconButton href="#/search/talent" iconStyle={styles.iconStyle}>
    <SocialPeopleIcon />
  </IconButton>
)

const DefaultTalentIcons = (
  <div>
    {DashBoardIcon}
    {ProfileIcon}
    {JobsIcon}
  </div>
)

const DefaultRepresentativeIcons = (
  <div>
    {DashBoardIcon}
    {ProfileIcon}
    {ProjectsIcon}
    {SearchIcon}
  </div>
)

const defaultAppBarProps = {
  title: 'Default Title',
  titleStyle: styles.appBarTitle,
  style: styles.appBar,
  iconElementLeft: LogoIcon,
}

// Utility that ensures injected icons on the right
// from props show up with correct vertical Alignment
// Can be used to sync styles between all right icons in the appbar
const getInjectedElements = (iconElementRight) => {
  if (!iconElementRight) {
    return null
  }
  let elementsOnTheRight = null

  if (iconElementRight.type.muiName === 'FlatButton') {
    // This doesn't work ):
    elementsOnTheRight =
      iconElementRight &&
      iconElementRight.props &&
      <FlatButton
        {...iconElementRight.props}
        style={stylesMerge(iconElementRight.props.style, { verticalAlign: 'middle' })}
      >
        {iconElementRight.props.children}
      </FlatButton>
  } else {
    elementsOnTheRight =
      iconElementRight &&
      iconElementRight.props &&
      <IconButton
        {...iconElementRight.props}
        style={stylesMerge(iconElementRight.props.style, { verticalAlign: 'middle' })}
        iconStyle={{ ...iconElementRight.props.iconStyle, ...styles.iconStyle }}
      >
        {iconElementRight.props.children}
      </IconButton>
  }

  return elementsOnTheRight
}

// Quick icons menu, can append to defaults by passing iconElementRight as per material ui props.
const generateIconsRight = (
  { replaceIconsElementRight = false, iconElementRight = null },
  account,
) => {
  const isAdmin = account && account.permissions && account.permissions.admin
  const isRepresentative = account && account.permissions && account.permissions.representative
  const isTalent = !isAdmin && !isRepresentative
  let defaultIcons = null

  if (isTalent) {
    defaultIcons = DefaultTalentIcons
  } else if (isRepresentative) {
    defaultIcons = DefaultRepresentativeIcons
  }

  if (replaceIconsElementRight) {
    return iconElementRight
  }
  const elementsOnTheRight = getInjectedElements(iconElementRight)

  return (
    <div style={{display: 'inline-flex'}}>
      {defaultIcons}
      {elementsOnTheRight}
    </div>
  )
}

export default class LookBookAppBar extends Component {
  static contextTypes = {
    account: object,
    mediaMatcher: object,
  }

  componentDidMount() {
    window.addEventListener('resize', this.onResize.bind(this))
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.onResize)
  }

  onResize() {
    this.forceUpdate()
  }

  render() {
    const { account } = this.context
    const { mediaMatcher: { matches: { xs, sm, md, lg, xl } } } = this.context
    const injectedIcons = generateIconsRight(this.props, account)
    const { noSearch, ...propsToPass } = this.props

    return (
      <div>
        <AppBar {...defaultAppBarProps} {...propsToPass} iconElementRight={injectedIcons} />
        <div
          style={stylesMerge(
            styles.appBarSearchContainer,
            (xs || sm || md) && !(lg || xl) && styles.appBarSearchContainerSm,
          )}
        >
        { this.props.noSearch
          ? null
          : <AppBarSearch />
        }
        </div>
        <div style={styles.appBarSpacer} />
      </div>
    )
  }
}
