import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import SwipeableViews from 'react-swipeable-views'
import Paper from 'material-ui/Paper'
import { Tabs, Tab } from 'material-ui/Tabs'
import IconButton from 'material-ui/IconButton'
import FlatButton from 'material-ui/FlatButton'

import NavigationCloseIcon from 'material-ui/svg-icons/navigation/close'
import { grey50 } from 'material-ui/styles/colors'

import theme from 'theme/mui-theme'
import * as profileActions from 'redux/modules/profile'
import * as projectActions from 'redux/modules/project'
import AppBar from 'containers/AppBar/AppBar'
import Loading from 'components/Loading/Loading'
import Details from 'containers/Profile/Details'
import Portfolio from 'containers/Profile/Portfolio'
import CompCard from 'containers/Profile/CompCard'
import AddToListPrompt from 'components/Dialogs/AddToListPrompt'
import MiniProfile from 'components/Profile/MiniProfile'

const { primary2Color } = theme.palette

const { bool, string, object, func, arrayOf } = PropTypes

const styles = {
  logo: {
    fontSize: 11,
    width: '4.5em',
    height: '4.5em',
    display: 'block',
  },
  paperModel: {
    backgroundColor: grey50,
  },
  paper: {
    margin: '64px 0 0 0',
    backgroundColor: grey50,
  },
  tabs: {
    width: '100%',
    padding: '0 24px',
    margin: '0 -24px',
    backgroundColor: grey50,
  },
  tab: {
    textTransform: 'capitalize',
    paddingRight: '60px',
    paddingLeft: '60px',
    fontSize: '22px',
    fontWeight: '400',
  },
  leftTabButton: {
    alignItems: 'flex-end',
  },
  rightTabButton: {
    alignItems: 'flex-start',
  },
}

const tabSlugToId = (slug) => {
  switch (slug) {
    case 'portfolio':
      return 1
    case 'comp-card':
      return 2
    default:
      return 1
  }
}

const tabIndexToSlug = (index) => {
  switch (index) {
    case 1:
      return 'portfolio'
    case 2:
      return 'comp-card'
    default:
      return ''
  }
}

@connect(
  state => ({
    session: state.session.data,
    account: state.account.data,
    profile: state.profile.data,
    mediaUploads: state.profile.uploads,
    loading: state.profile.loading,
    loaded: state.profile.loaded,
    action: state.profile.action,
    error: state.profile.error,
  }),
  {
    ...profileActions,
    projectAddToList: projectActions.addToList,
  },
)
export default class ProfileScreen extends PureComponent {
  static propTypes = {
    handleDrawerNavToggle: func,
    params: object,
    location: object,
    session: object,
    account: object,
    profile: object,
    mediaUploads: arrayOf(object),
    loading: bool,
    loaded: bool,
    action: string,
    error: object,
    load: func,
    save: func,
    projectAddToList: func,
  }

  static contextTypes = {
    router: object,
  }

  state = {
    tabIndex: 1,
    openAddToListPrompt: false,
    viewImmutable: false,
  }

  componentWillMount() {
    const { params, account, load } = this.props

    if (params.profileSlug) {
      load(null, params.profileSlug)
    } else if (account && account.profiles && account.profiles.length) {
      load(account.profiles[0].id)
    }

    this.setState({ tabIndex: tabSlugToId(params.tabSlug) })
  }

  componentWillReceiveProps(nextProps) {
    const { params: { profileSlug, tabSlug }, load } = this.props

    if (nextProps.params.tabSlug !== tabSlug) {
      this.setState({ tabIndex: tabSlugToId(nextProps.params.tabSlug) })
    }

    if (!profileSlug && nextProps.account !== this.props.account) {
      const { account } = nextProps

      if (account && account.profiles && account.profiles.length) {
        load(account.profiles[0].id)
      }
    }
  }

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle()

  handleAppBarLeftIconButtonTouchTap = returnTo => () => this.context.router.push(returnTo)

  handleTabsChange = (tabIndex) => {
    const { params: { profileSlug }, location: { query } } = this.props

    this.context.router.replace(
      `/${profileSlug || 'profile'}/${tabIndexToSlug(tabIndex)}${query.returnTo ? `?returnTo=${query.returnTo}` : ''}`,
    )
    this.setState({ tabIndex })
  }

  handleAddToListPromptOpen = () => this.setState({ openAddToListPrompt: true })
  handleAddToListPromptClose = () => this.setState({ openAddToListPrompt: false })
  handleAddToListPromptConfirm = (projectId) => {
    const { profile: { id }, projectAddToList } = this.props

    projectAddToList(projectId, id, 'castingList', this.handleAddToListPromptClose)
  }

  handleViewImmutableToggle = () => this.setState({ viewImmutable: !this.state.viewImmutable })

  render() {
    const {
      params: { profileSlug },
      account,
      profile,
      mediaUploads,
      loading,
      action,
      location: { query },
    } = this.props
    const { tabIndex, openAddToListPrompt } = this.state
    const isRepresentative = account && account.permissions && account.permissions.representative
    let mutable = account && account.permissions && account.permissions.admin

    if (
      account &&
      account.profiles.length &&
      profile &&
      account.profiles.find(item => item.id === profile.id)
    ) {
      mutable = true && !this.state.viewImmutable
    }

    if (query.print) {
      return <CompCard mutable={false} printable profile={profile} />
    }

    const appBarProps = {
      title: profileSlug ? 'Talent Profile' : 'Your Profile',
      onLeftIconButtonTouchTap: this.handleDrawerNavToggle,
    }

    if (query.returnTo) {
      appBarProps.iconElementLeft = (
        <IconButton onTouchTap={this.handleAppBarLeftIconButtonTouchTap(query.returnTo)}>
          <NavigationCloseIcon />
        </IconButton>
      )
    }

    if (profile && profile.type === 'model') {
      if (isRepresentative) {
        appBarProps.iconElementRight = (
          <FlatButton onTouchTap={this.handleAddToListPromptOpen} label="Add to project" />
        )
      } else {
        appBarProps.iconElementRight = (
          <FlatButton onTouchTap={this.handleViewImmutableToggle} label={this.state.viewImmutable ? 'My View' : 'Client View'} />
        )
      }

      return (
        <div>
          <AppBar {...appBarProps} />
          <MiniProfile {...profile} profile={profile} mutable={mutable} />
          <Tabs
            style={styles.tabs}
            inkBarStyle={{ display: 'none' }}
            tabItemContainerStyle={styles.tabLabel}
            onChange={this.handleTabsChange}
            value={tabIndex}
          >
            {/* <Tab label="Profile" value={0} /> */}
            <Tab
              label="Portfolio"
              style={styles.tab}
              buttonStyle={styles.leftTabButton}
              value={1}
            />
            <Tab
              label="Comp Card"
              style={styles.tab}
              buttonStyle={styles.rightTabButton}
              value={2}
            />
          </Tabs>
          <Paper style={styles.paperModel} rounded={false} zDepth={0}>
            <Loading loading={loading && action === 'load'}>
              <SwipeableViews
                animateHeight={false}
                animateTransitions={false}
                index={tabIndex}
                onChangeIndex={this.handleTabsChange}
              >
                <div>
                  <Details mutable={mutable} profile={profile} />
                </div>
                <div>
                  <Portfolio mutable={mutable} profile={profile} mediaUploads={mediaUploads} />
                </div>
                <div>
                  <CompCard mutable={mutable} profile={profile} mediaUploads={mediaUploads} />
                </div>
              </SwipeableViews>
            </Loading>
          </Paper>

          {isRepresentative &&
            <AddToListPrompt
              open={openAddToListPrompt}
              onCloseConfirm={this.handleAddToListPromptConfirm}
              onCloseCancel={this.handleAddToListPromptClose}
            />}
        </div>
      )
    }

    return (
      <div>
        <AppBar {...appBarProps} />
        <Paper style={styles.paper} rounded={false} zDepth={0}>
          <Loading loading={loading && action === 'load'}>
            <Details mutable={mutable} profile={profile} />
          </Loading>
        </Paper>
      </div>
    )
  }
}
