import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import FlatButton from 'material-ui/FlatButton'
import CircularProgress from 'material-ui/CircularProgress'
import ActionPrintIcon from 'material-ui/svg-icons/action/print'
import FileFileDownloadIcon from 'material-ui/svg-icons/file/file-download'
import { white, grey50 } from 'material-ui/styles/colors'

import * as profileActions from 'redux/modules/profile'
import sm from 'helpers/stylesMerge'
import CompCardSideA from 'components/CompCard/SideA'
import CompCardSideB from 'components/CompCard/SideB'

const { bool, string, object, func, arrayOf } = PropTypes

const styles = {
  paper: {
    backgroundColor: grey50,
  },
  paperSm: {
    padding: 16,
  },
  wrapper: {
    display: 'flex',
    flexWrap: 'wrap',
  },
  printWrapper: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
}

@connect(
  state => ({
    pdfGenerating: state.profile.pdfGenerating,
    pdfUrl: state.profile.pdfUrl,
  }),
  {
    generatePdf: profileActions.generatePdf,
  },
)
export default class CompCard extends PureComponent {
  static propTypes = {
    mutable: bool.isRequired,
    printable: bool,
    profile: object.isRequired,
    pdfGenerating: bool.isRequired,
    pdfUrl: string.isRequired,
    generatePdf: func.isRequired,
    mediaUploads: arrayOf(object),
  }

  static contextTypes = {
    mediaQueries: object,
  }

  static defaultProps = {
    printable: false,
  }

  handlePrintButton = () => {
    const { profile: { slug } } = this.props

    window.open(`#/${slug}/comp-card?print=1`)
  }

  handlePdfButton = async () => {
    const { profile: { slug }, generatePdf } = this.props

    const { url } = await generatePdf(slug)
    window.open(url)
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { mutable, printable, profile, pdfGenerating, mediaUploads } = this.props

    return (
      <div>
        <Paper
          style={!printable ? sm(styles.paper, !xs && styles.paperSm) : {}}
          rounded={false}
          zDepth={0}
        >
          <div style={sm(!xs && styles.wrapper)}>
            <CompCardSideA
              mutable={mutable}
              mediaUploads={mediaUploads.filter(item =>
                ['compCardA', 'compCardB', 'compCardC', 'compCardD'].includes(item.fieldName),
              )}
              {...profile}
            />
            <CompCardSideB
              mutable={mutable}
              mediaUploads={mediaUploads.filter(item => item.fieldName === 'compCardCover')}
              {...profile}
            />
          </div>
          {!printable
            ? <div style={styles.printWrapper}>
              <FlatButton
                label={pdfGenerating ? 'Generating PDF..' : 'Download PDF'}
                secondary
                icon={
                    pdfGenerating
                      ? <CircularProgress size={25} thickness={2} />
                      : <FileFileDownloadIcon />
                  }
                onTouchTap={this.handlePdfButton}
              />
              <FlatButton
                label="Print"
                secondary
                icon={<ActionPrintIcon />}
                onTouchTap={this.handlePrintButton}
              />
            </div>
            : <style
              dangerouslySetInnerHTML={{
                __html: ['body {', `  background: ${white}`, '}'].join('\n'),
              }}
            />}
        </Paper>
      </div>
    )
  }
}
