import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import IconButton from 'material-ui/IconButton'
import { RadioButton, RadioButtonGroup } from 'material-ui/RadioButton'
import RaisedButton from 'material-ui/RaisedButton'
import FlatButton from 'material-ui/FlatButton'
import Drawer from 'material-ui/Drawer'
import NavigationArrowBackIcon from 'material-ui/svg-icons/navigation/arrow-back'
import ActionSearchIcon from 'material-ui/svg-icons/action/search'
import NavigationCloseIcon from 'material-ui/svg-icons/navigation/close'
import { white } from 'material-ui/styles/colors'

import { countValues } from 'helpers/object'
import * as sessionActions from 'redux/modules/session'
import * as profilesActions from 'redux/modules/profiles'
import * as castingActions from 'redux/modules/casting'
import * as bookingActions from 'redux/modules/booking'
import AddToListPrompt from 'components/Dialogs/AddToListPrompt'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'
import ZeroResults from 'components/ZeroResults/ZeroResults'
import OnReady from 'components/OnReady/OnReady'
import ResultItem from 'components/Profile/ResultItem'
import FilterSummary from 'components/Filter/FilterSummary'
import {
  hairColorOptions, eyeColorOptions,
  ethnicityOptions, cityOptions,
  femaleHeightOptions, maleHeightOptions,
  femaleWaistOptions, maleWaistOptions,
  femaleHipsOptions, femaleDressOptions,
  femaleBustOptions,
  femaleShoeOptions, maleShoeOptions,
  maleSuitOptions,
  maleInseamOptions, femaleInseamOptions,
} from 'components/Profile/Profile'

const { bool, array, object, func } = PropTypes

const initialProfileCriteria = { gender: undefined }

const styles = {
  paper: {
    margin: '64px 0 0 0',
    padding: '2%',
    backgroundColor: white,
  },
  wrapper: {
    display: 'flex',
    flexWrap: 'wrap',
  },
  filters: {
    flex: '0 0 auto',

  },
  gridList: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    overflowX: 'auto',
    height: 'calc(100vh - 8em)',
  },
  gridListItem: {
    flex: '0 0 200px',
    height: 200,
    width: 200,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
  },
  gridListImage: {
    height: '100%',
    transform: 'translateX(-50%)',
    position: 'relative',
    left: '50%',
  },
  resultsGrid: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  field: {
    width: '50%',
    flex: 1,
  },
  filterForm: {
    padding: 'calc(64px + 1em) 1em 0',
  },
  filterField: {
    margin: '1em 0 1em',
  },
  filtersApply: {
    marginTop: '2em',
  },
  clearFiltersWrapper: {
    display: 'flex',
    justifyContent: 'flex-end',
    marginBottom: '0.5em',
  },
}

@connect(
  state => ({
    profilesError: state.profiles.errors,
    profiles: state.profiles.data,
    profilesLoaded: state.profiles.loaded,
    profilesLoading: state.profiles.loading,
    profileCriteria: state.session.profileCriteria,
  }),
  {
    profilesLoad: profilesActions.load,
    sessionSet: sessionActions.set,
    castingAdd: castingActions.addMember,
    bookingAdd: bookingActions.addMember,
  })
export default class ProfileSearch extends Component {
  static propTypes = {
    params: object,
    location: object,
    handleDrawerNavToggle: func,
    profiles: array,
    profilesError: array,
    profilesLoaded: bool,
    profilesLoading: bool,
    profilesLoad: func,
    profileCriteria: object,
    sessionSet: func,
    castingAdd: func.isRequired,
    bookingAdd: func.isRequired,
  }

  static contextTypes = {
    router: object,
    store: object,
  }

  static defaultProps = {
    profileCriteria: initialProfileCriteria,
  }

  state = {
    openAddToListPrompt: false,
    filterDrawerOpen: false,
    addProfile: {},
  }

  componentWillMount() {
    const { profilesLoad, profileCriteria } = this.props

    profilesLoad({ type: 'model', ...profileCriteria })
  }

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle()

  handleAppBarLeftIconButtonTouchTap = () => {
    const { params: { projectId, listType, listId } } = this.props

    this.context.router.push(`/projects/${projectId}/${listType}/${listId}`)
  }

  handleItemTouchTap = (profileSlug, tabSlug) => () => {
    this.context.router.push(`/${profileSlug}/${tabSlug}/?returnTo=${this.props.location.pathname}`)
  }

  handleAddToListPromptOpen = profile => () => {
    const { params: { listType, listId } } = this.props

    if (listId) {
      this.handleAddToListPromptConfirm(profile.id, listType, listId)
    } else {
      this.setState({ addProfile: profile, openAddToListPrompt: true })
    }
  }

  handleAddToListPromptClose = () => this.setState({ openAddToListPrompt: false })

  handleAddToListPromptConfirm = (profileId, listType, listId) => {
    const actionKey = `${listType}Add`

    this.props[actionKey](listId, profileId)
  }

  handleFilterGenderChange = (event, filterGender) =>
    this.context.store
      .dispatch(this.handleFilterForm({
        gender: (filterGender.length && filterGender) || undefined,
      }))

  handleFilterForm = (criteria) => {
    const { profileCriteria } = this.props
    const newCriteria = { ...profileCriteria, ...criteria }
    delete newCriteria.id

    return sessionActions.set('profileCriteria', newCriteria)
  }

  handleFilterButton = () => {
    const { profilesLoad, profileCriteria } = this.props

    profilesLoad({ type: 'model', ...profileCriteria })
    this.handleFilterDrawerToggle()
  }

  handleFilterDrawerToggle = (open) => {
    if (open === false) {
      return this.handleFilterButton()
    }

    return this.setState({ filterDrawerOpen: !this.state.filterDrawerOpen })
  }

  handleResetFilters = () => {
    const { sessionSet, profilesLoad } = this.props
    const criteria = { ...initialProfileCriteria }

    sessionSet('profileCriteria', criteria)
    profilesLoad({ type: 'model', ...criteria })
  }

  render() {
    const { params, profiles, profilesLoading, profileCriteria } = this.props
    const { addProfile, openAddToListPrompt, filterDrawerOpen } = this.state
    const { listType = 'project' } = params
    const appBarProps = {}

    if (params.projectId) {
      appBarProps.iconElementLeft = (
        <IconButton onTouchTap={this.handleAppBarLeftIconButtonTouchTap}>
          <NavigationArrowBackIcon/>
        </IconButton>
      )
    } else {
      appBarProps.onLeftIconButtonTouchTap = this.handleDrawerNavToggle
    }

    return (
      <div>
        <style
          dangerouslySetInnerHTML={{
            __html: [
              'body {',
              `  background: ${white}`,
              '}',
            ].join('\n'),
          }}
        />
        <AppBar
          title="Talent Search"
          {...appBarProps}
          iconElementRight={
            <IconButton onTouchTap={this.handleFilterDrawerToggle}>
              <ActionSearchIcon/>
            </IconButton>
          }
        />

        <Paper style={styles.paper} rounded={false} zDepth={0}>
          <OnReady ready={!profilesLoading}>
            <div>
              <ZeroResults results={profiles.length} />
              <FilterSummary filters={countValues(profileCriteria)} results={profiles.length} />

              <div style={styles.resultsGrid}>
                { profiles.map(profile =>
                  <ResultItem
                    key={profile.id}
                    onItemTouchTap={this.handleItemTouchTap(profile.slug, 'portfolio')}
                    onAddToListPromptOpen={this.handleAddToListPromptOpen(profile)}
                    tooltip={`Add to ${listType}`}
                    {...profile}
                  />
                ) }
              </div>
            </div>
          </OnReady>
        </Paper>

        <AddToListPrompt
          open={openAddToListPrompt}
          onAddConfirm={this.handleAddToListPromptConfirm}
          onCloseCancel={this.handleAddToListPromptClose}
          profile={addProfile}
        />

        <Drawer width={320} zDepth={1} openSecondary open={filterDrawerOpen} onRequestChange={this.handleFilterDrawerToggle} >
          <AppBar
            title="Filters"
            noSearch
            titleStyle={styles.appBarTitle}
            iconElementLeft={<IconButton onTouchTap={this.handleFilterDrawerToggle}><NavigationCloseIcon /></IconButton>}
            replaceIconsElementRight
          />

          <div style={styles.filterForm}>

            <div style={styles.clearFiltersWrapper}>
              <FlatButton label="clear all filters" secondary onTouchTap={this.handleResetFilters} />
            </div>

            <Form layout={false} action={this.handleFilterForm} data={profileCriteria} fieldSetStyle={styles.filters} fieldStyle={styles.field}>
              <RadioButtonGroup name="gender" defaultSelected={profileCriteria.gender} value={profileCriteria.gender} style={{ display: 'flex' }} onChange={this.handleFilterGenderChange}>
                <RadioButton
                  value=""
                  label="Any"
                  style={{ width: 'initial', marginRight: '1em' }}
                />
                <RadioButton
                  value="female"
                  label="Female"
                  style={{ width: 'initial', marginRight: '1em' }}
                />
                <RadioButton
                  value="male"
                  label="Male"
                  style={{ width: 'initial' }}
                />
              </RadioButtonGroup>

              <Field mutable type="select" label="Current location" name="city" options={cityOptions} fullWidth style={{ width: '100%' }} />
            </Form>

            { profileCriteria.gender === 'female' &&
              <Form layout={false} action={this.handleFilterForm} data={profileCriteria} fieldSetStyle={styles.filters} fieldStyle={styles.field}>

                <Field mutable type="select" label="Min height" name="minHeight" options={femaleHeightOptions} />
                <Field mutable type="select" label="Max height" name="maxHeight" options={femaleHeightOptions} />

                <Field mutable type="select" label="Min bust" name="minBust" options={femaleBustOptions} />
                <Field mutable type="select" label="Max bust" name="maxBust" options={femaleBustOptions} />

                <Field mutable type="select" label="Min waist" name="minWaist" options={femaleWaistOptions} />
                <Field mutable type="select" label="Max waist" name="maxWaist" options={femaleWaistOptions} />

                <Field mutable type="select" label="Min inseam" name="minInseam" options={femaleInseamOptions} />
                <Field mutable type="select" label="Max inseam" name="maxInseam" options={femaleInseamOptions} />

                <Field mutable type="select" label="Min hips" name="minHips" options={femaleHipsOptions} />
                <Field mutable type="select" label="Max hips" name="maxHips" options={femaleHipsOptions} />

                <Field mutable type="select" label="Min dress" name="minDressSize" options={femaleDressOptions} />
                <Field mutable type="select" label="Max dress" name="maxHDressSize" options={femaleDressOptions} />

                <Field mutable type="select" label="Min shoe" name="minShoeSize" options={femaleShoeOptions} />
                <Field mutable type="select" label="Max shoe" name="maxShoeSize" options={femaleShoeOptions} />
              </Form>
            }

            { profileCriteria.gender === 'male' &&
              <Form layout={false} action={this.handleFilterForm} data={profileCriteria} fieldSetStyle={styles.filters} fieldStyle={styles.field}>
                <Field mutable type="select" label="Min height" name="minHeight" options={maleHeightOptions} />
                <Field mutable type="select" label="Max height" name="maxHeight" options={maleHeightOptions} />

                <Field mutable type="select" label="Min suit" name="minSuit" options={maleSuitOptions} />
                <Field mutable type="select" label="Max suit" name="maxSuit" options={maleSuitOptions} />

                <Field mutable type="select" label="Min inseam" name="minInseam" options={maleInseamOptions} />
                <Field mutable type="select" label="Max inseam" name="maxInseam" options={maleInseamOptions} />

                <Field mutable type="select" label="Min waist" name="minWaist" options={maleWaistOptions} />
                <Field mutable type="select" label="Max waist" name="maxWaist" options={maleWaistOptions} />

                <Field mutable type="select" label="Min shoe" name="minShoeSize" options={maleShoeOptions} />
                <Field mutable type="select" label="Max shoe" name="maxShoeSize" options={maleShoeOptions} />
              </Form>
            }

            <Form layout={false} action={this.handleFilterForm} data={profileCriteria} fieldSetStyle={styles.filters} fieldStyle={styles.field}>
              <Field mutable type="select" label="Eye Color" name="eyeColor" options={eyeColorOptions} />
              <Field mutable type="select" label="Hair Color" name="hairColor" options={hairColorOptions} />

              <Field mutable type="number" label="Min age" name="minBirthYear" />
              <Field mutable type="number" label="Max age" name="maxBirthYear" />

              <Field mutable type="select" label="Ethnicity" name="ethnicity" options={ethnicityOptions} />
              <div />

              <Field mutable type="checkbox" label="Tatoos" name="tattoos" />
              <Field mutable type="checkbox" label="Piercings" name="piercings" />

            </Form>

            <div style={styles.filterField}>
              <RaisedButton label="Apply" secondary fullWidth style={styles.filtersApply} onTouchTap={this.handleFilterButton} />
            </div>
          </div>
        </Drawer>
      </div>
    )
  }
}
