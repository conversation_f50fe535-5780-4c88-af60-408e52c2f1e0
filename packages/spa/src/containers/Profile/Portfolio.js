import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import Paper from 'material-ui/Paper'
import RaisedButton from 'material-ui/RaisedButton'
import ContentAddIcon from 'material-ui/svg-icons/content/add'
import { grey50 } from 'material-ui/styles/colors'
import sm from 'helpers/stylesMerge'
import * as profileActions from 'redux/modules/profile'
import MediaGrid from 'components/Media/Grid'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'
import Slideshow from 'components/media/Slideshow'

const { bool, object, arrayOf } = PropTypes

const styles = {
  paper: {
    backgroundColor: grey50,
    height: '100vh',
  },
  paperSm: {
    padding: '0px 54px',
  },
  wrapper: {
    paddingBottom: 10,
    boxShadow: 'none',
  },
  buttonWrapper: {
    textAlign: 'right',
    marginBottom: '1em',
  },
  buttonWrapperXs: {
    margin: '1em',
  },
  upload: {
    textAlign: 'center',
    position: 'absolute',
    top: 0,
    right: 0,
  },
  uploadForm: {
    display: 'none',
  },
}

export default class Portfolio extends PureComponent {
  static propTypes = {
    mutable: bool,
    profile: object,
    mediaUploads: arrayOf(object),
  }

  static contextTypes = {
    mediaQueries: object,
  }

  state = {
    slideShowOpen: false,
    slideShowIndex: 0,
  }

  handleOpenFileSelector = () => document.getElementById('file-upload-media').click()

  onGridItemClick = (index) => {
    this.setState({
      slideShowOpen: true,
      slideShowIndex: index,
    })
  }

  onCloseLightbox = () => this.setState({ slideShowOpen: false })

  render() {
    const { mediaQueries: { xs } } = this.context
    const { mutable, profile, mediaUploads } = this.props
    const uploads = mediaUploads.filter(item => item.fieldName === 'media') || []
    const media = [...uploads, ...profile.media] || []

    return (
      <Paper style={sm(styles.paper, !xs && styles.paperSm)} rounded={false} zDepth={0}>
        {mutable
          ? <div style={sm(styles.buttonWrapper, xs && styles.buttonWrapperXs)}>
            <RaisedButton
              label="Add Photo"
              secondary
              icon={<ContentAddIcon />}
              onTouchTap={this.handleOpenFileSelector}
            >
              <Form
                style={styles.uploadForm}
                layout={false}
                action={profileActions.attachMedia}
                data={profile}
              >
                <Field
                  mutable
                  value
                  type="file"
                  name="media"
                  id="file-upload-media"
                  layout={false}
                  accept=".jpg,.png,.jpeg,.tif,.tiff, image/png, image/jpeg, image/tiff"
                />
              </Form>
            </RaisedButton>
          </div>
          : null}

        <Paper style={styles.wrapper}>
          <MediaGrid onGridItemClick={(index) => { this.onGridItemClick(index) }} mutable={mutable} profile={profile} media={media} />
        </Paper>
        {mutable
          ? null
          : <Slideshow media={media} isOpen={this.state.slideShowOpen} onCloseLightbox={this.onCloseLightbox} index={this.state.slideShowIndex} />
        }
      </Paper>
    )
  }
}
