import React, { Component, PropTypes } from 'react'
import Paper from 'material-ui/Paper'
import { blueGrey50 } from 'material-ui/styles/colors'

import sm from 'helpers/stylesMerge'
import Profile from 'components/Profile/Profile'

const { bool, object } = PropTypes

const styles = {
  paper: {
    backgroundColor: blueGrey50,
  },
  paperSm: {
    padding: 24,
  },
}

export default class Details extends Component {
  static propTypes = {
    mutable: bool,
    profile: object,
  }

  static contextTypes = {
    mediaQueries: object,
  }

  shouldComponentUpdate(nextProps) {
    const { profile } = this.props

    return profile !== nextProps.profile
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { mutable, profile } = this.props

    return (
      <Paper style={sm(styles.paper, !xs && styles.paperSm)} rounded={false} zDepth={0}>
        <Profile mutable={mutable} {...profile} />
      </Paper>
    )
  }
}
