import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import RaisedButton from 'material-ui/RaisedButton'
import TextField from 'material-ui/TextField'
import { white } from 'material-ui/styles/colors'
import * as sessionActions from 'redux/modules/session'
import * as accountActions from 'redux/modules/account'
import Logo from 'components/Icons/Logo'

const { array, object, func } = PropTypes

const styles = {
  wrapper: {
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    height: '100vh',
  },
  center: {
    width: '30%',
    minWidth: 300,
    maxWidth: 425,
  },
  logo: {
    fontSize: 24,
    height: 'auto',
    width: '40%',
    display: 'block',
    margin: '2em auto',
  },
  paper: {
    backgroundColor: white,
    padding: '1em',
  },
  input: {
    width: '100%',
  },
  submit: {
    textAlign: 'right',
    marginTop: 24,
  },
}

@connect(
  state => ({
    session: state.session,
    sessionErrors: state.session.error,
  }),
  {
    sessionCreate: sessionActions.create,
    accountLoad: accountActions.load,
  })
export default class Login extends Component {
  static propTypes = {
    session: object,
    sessionErrors: array,
    sessionCreate: func,
    accountLoad: func,
  }

  static contextTypes = {
    router: object,
  }

  values = {
    email: null,
    passPhrase: null,
  }

  handleLoginButton = () => {
    const { email, passPhrase } = this.values

    this.props.sessionCreate(email, passPhrase, (error, session) => {
      if (!error) {
        setTimeout(() => this.props.accountLoad(session.account), 1)
      }
    })
  }

  handleFieldChange = name => event => this.values[name] = event.target.value

  handleFieldEnterKey = event => event.keyCode === 13 ? this.handleLoginButton() : false

  render() {
    const { sessionErrors } = this.props

    return (
      <div style={styles.wrapper}>
        <div style={styles.center}>
          <Logo style={styles.logo} />

          <Paper style={styles.paper} zDepth={2}>
            <TextField
              hintText="Email"
              floatingLabelText="Email"
              style={styles.input}
              onChange={this.handleFieldChange('email')}
              onKeyDown={this.handleFieldEnterKey}
            />
            <TextField
              hintText="Pass phrase"
              floatingLabelText="Pass phrase"
              style={styles.input}
              type="password"
              onChange={this.handleFieldChange('passPhrase')}
              onKeyDown={this.handleFieldEnterKey}
            />
            <RaisedButton label="Login" secondary style={styles.submit} onTouchTap={this.handleLoginButton} />
            { sessionErrors && sessionErrors.length ? sessionErrors.map(error => <p>{error.message}</p>) : <div /> }
          </Paper>
        </div>
      </div>
    )
  }
}
