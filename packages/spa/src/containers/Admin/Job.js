import React, { Children, PureComponent, PropTypes } from 'react'
import Sugar from 'sugar-date'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import Toggle from 'material-ui/Toggle'
import IconButton from 'material-ui/IconButton'
import NavigationArrowBackIcon from 'material-ui/svg-icons/navigation/arrow-back'
import { white } from 'material-ui/styles/colors'

import * as adminActions from 'redux/modules/admin'
import getMediaUrl from 'helpers/getMediaUrl'
import sm from 'helpers/stylesMerge'
import noop from 'helpers/noop'
import If from 'components/If'
import OnReady from 'components/OnReady/OnReady'
import JobHeading from 'components/Jobs/Heading'
import ZeroResults from 'components/ZeroResults/ZeroResults'
import CastingDetails from 'components/Jobs/CastingDetails'
import Heading from 'components/Heading/Heading'

const { bool, object, func, node } = PropTypes

const styles = {
  paper: {
    margin: '64px 0 0',
    padding: '8px 8px 2em 8px',
  },
  paperSm: {
    padding: '24px 24px 3em 24px',
  },
  avatarPreview: {
    float: 'right',
    width: 500,
  },
}

const Main = ({ job, content, ...rest }) => {
  const {
    visibility,
    avatar,
    rateType,
    rate,
    currency,
    buyout,
    travel,
    objectives,
    type,
    usage,
    otherUsage,
    location,
    notes,
    dates = [],
    castings = [],
  } = job

  const leftBlank = <i>Field was left blank</i>

  return (
    <Paper rounded={false} zDepth={0}>
      <JobHeading job={job} />

      <Heading text="Booking details" weight={400} size={1.25} style={styles.heading} />

      <div style={styles.avatarPreview}>
        {avatar ? <img src={getMediaUrl(avatar, 512)} /> : 'No job image provided'}
      </div>

      <ul>
        <li><strong>Visibility: </strong>{visibility || leftBlank}</li>
        <li><strong>Location: </strong>{location || leftBlank}</li>
        <li>
          <strong>Dates: </strong>
          {dates.length
            ? <ol>
              {dates.map(date => (
                <li key={`${date.start}-${date.end}`}>
                  {Sugar.Date.format(new Date(date.start), '{full}')}
                </li>
                ))}
            </ol>
            : leftBlank}
        </li>
        <li><strong>Rate: </strong>{rateType || leftBlank}</li>
        <li><strong>Currency: </strong>{currency || leftBlank}</li>
        <li><strong>Rate: </strong>{rate || leftBlank}</li>
        <li><strong>Buyout: </strong>{buyout || leftBlank}</li>
        <li><strong>Travel: </strong> paid by {travel}</li>
        <li><strong>Looking for: </strong> {objectives.join(', ')}</li>
        <li><strong>Type: </strong>{type.join(', ')}</li>
        <li><strong>Usage: </strong>{usage.join(', ')}</li>
        <li><strong>Other usage: </strong>{otherUsage || leftBlank}</li>
        <li><strong>Notes: </strong>{notes || leftBlank}</li>
      </ul>

      {castings.length
        ? <div>
          <Heading text="Castings" weight={400} size={1.25} style={styles.heading} />
          <ul>
            {castings.map(casting => (
              <li key={casting.id}>
                <Heading text={casting.name} weight={400} size={1.125} style={styles.heading} />

                <ul>
                  <li><strong>Location: </strong>{casting.location || leftBlank}</li>
                  <li>
                    <strong>Date: </strong>
                    {casting.dates.length
                        ? Sugar.Date.format(new Date(casting.dates[0].start), '{full}')
                        : leftBlank}
                  </li>
                  <li><strong>Notes: </strong>{casting.notes || leftBlank}</li>
                </ul>
              </li>
              ))}
          </ul>
        </div>
        : ''}

    </Paper>
  )
}

@connect(
  state => ({
    job: state.admin.data.job,
    loaded: state.admin.loaded,
    loading: state.admin.loading,
  }),
  {
    load: adminActions.loadJob,
    approve: adminActions.approveJob,
    disapprove: adminActions.disapproveJob,
  },
)
export default class AdminJob extends PureComponent {
  static propTypes = {
    children: node,
    handleDrawerNavToggle: func,
    params: object,
    job: object,
    loaded: bool,
    loading: bool,
    load: func.isRequired,
    approve: func.isRequired,
    disapprove: func.isRequired,
  };

  static contextTypes = {
    router: object,
    mediaQueries: object,
  };

  componentWillMount() {
    const { load, params: { jobId } } = this.props

    load(jobId)
  }

  handleAppBarLeftIconButtonTouchTap = () => {
    this.context.router.push('/admin/approve/jobs')
  };

  handleToggle = (event, approved) => {
    const { params: { jobId }, approve, disapprove } = this.props
    return approved ? approve(jobId) : disapprove(jobId)
  };

  render() {
    const { mediaQueries: { xs } } = this.context
    const { children, loaded, job } = this.props

    return (
      <div>
        <AppBar
          title="Admin Job details"
          iconElementLeft={
            (
              <IconButton onTouchTap={this.handleAppBarLeftIconButtonTouchTap}>
                <NavigationArrowBackIcon/>
              </IconButton>
            )
          }
        />

        <Paper style={sm(styles.paper, !xs && styles.paperSm)} rounded={false} zDepth={0}>
          <OnReady ready={loaded}>
            <If
              if={job && !!job.id}
              then={Toggle}
              defaultToggled={job && job.approved}
              onToggle={this.handleToggle}
              label="Approval"
              labelPosition="right"
              style={{ float: 'right', width: 'initial' }}
            />
            <If if={job && !!job.id} then={Main} job={job} content={children} />
            <If if={!job} then={ZeroResults} text="Job not found" />
          </OnReady>
        </Paper>
      </div>
    )
  }
}
