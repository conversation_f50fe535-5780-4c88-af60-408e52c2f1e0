import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import Avatar from 'material-ui/Avatar'
import { List } from 'material-ui/List'
import { ListItem } from 'material-ui/List'
import Toggle from 'material-ui/Toggle'
import ActionFaceIcon from 'material-ui/svg-icons/action/face'

import getMediaUrl from 'helpers/getMediaUrl'

import * as profileActions from 'redux/modules/profile'
import * as profilesActions from 'redux/modules/profiles'

const { bool, array, object, func } = PropTypes

const styles = {
  paper: {
    margin: '64px 0 0',
    padding: 0,
  },
  floatingAction: {
    position: 'fixed',
    bottom: '1.5em',
    right: '1.5em',
  },
}

@connect(
  state => ({
    profileError: state.profile.errors,
    profiles: state.profiles.data,
    profilesLoaded: state.profiles.loaded,
    profilesLoading: state.profiles.loading,
  }),
  {
    profileSave: profileActions.save,
    profilesLoad: profilesActions.load,
  })
export default class Profiles extends Component {
  static propTypes = {
    params: object,
    handleDrawerNavToggle: func,
    profileError: array,
    profileSave: func,
    profiles: array,
    profilesLoad: func,
    profilesLoaded: bool,
    profilesLoading: bool,
  }

  static contextTypes = {
    router: object,
  }

  static defaultProps = {
    profiles: [],
  }

  componentWillMount() {
    const { profilesLoad, params: { type } } = this.props

    profilesLoad({ type })
  }

  componentWillReceiveProps(nextProps) {
    const { profilesLoad, params: { type } } = this.props
    const { params: { type: nextType } } = nextProps

    if (type !== nextType) {
      profilesLoad({ type: nextType })
    }
  }

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle()

  handleToggle = id => (event, approved) => this.props.profileSave({ id, approved })

  render() {
    const { profiles, params: { type } } = this.props

    return (
      <div>
        <AppBar
          title={`Approve ${type} profiles`}
          onLeftIconButtonTouchTap={this.handleDrawerNavToggle}
        />

        <Paper style={styles.paper} rounded={false} zDepth={0}>

          <List>
            { profiles.map(profile =>
              <ListItem
                key={profile.id}
                primaryText={profile.name}
                leftAvatar={profile.avatar ? <Avatar src={getMediaUrl(profile.avatar, 128)} /> : <Avatar icon={<ActionFaceIcon />} />}
                href={`#/${profile.slug}/`}
                rightIcon={
                  <Toggle defaultToggled={profile.approved} onToggle={this.handleToggle(profile.id)} />
                }
              />
            ) }
          </List>
        </Paper>
      </div>
    )
  }
}
