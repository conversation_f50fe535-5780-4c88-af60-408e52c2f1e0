import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import Avatar from 'material-ui/Avatar'
import { List } from 'material-ui/List'
import { ListItem } from 'material-ui/List'

import { DEFAULT_PHOTO } from '../../constants'
import getMediaUrl from 'helpers/getMediaUrl'
import * as adminActions from 'redux/modules/admin'
import ZeroResults from 'components/ZeroResults/ZeroResults'
import OnReady from 'components/OnReady/OnReady'

const { bool, array, object, func, arrayOf } = PropTypes

const styles = {
  paper: {
    margin: '64px 0 0',
    padding: 0,
  },
  floatingAction: {
    position: 'fixed',
    bottom: '1.5em',
    right: '1.5em',
  },
}

@connect(
  state => ({
    jobs: state.admin.data.jobs,
    loaded: state.admin.loaded,
    loading: state.admin.loading,
  }),
  {
    load: adminActions.loadJobs,
  },
)
export default class AdminJobs extends Component {
  static propTypes = {
    handleDrawerNavToggle: func.isRequired,
    jobs: arrayOf(object),
    loaded: bool.isRequired,
    loading: bool.isRequired,
    load: func.isRequired,
  };

  static contextTypes = {
    router: object,
  };

  static defaultProps = {
    jobs: [],
  };

  state = {
    displayAlertDialog: false,
  };

  componentWillMount() {
    const { load } = this.props

    load()
  }

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle();

  render() {
    const { jobs, loaded } = this.props

    return (
      <div>
        <AppBar
          title="Approve Jobs"
          onLeftIconButtonTouchTap={this.handleDrawerNavToggle}
        />

        <Paper style={styles.paper} rounded={false} zDepth={0}>
          <OnReady ready={loaded}>
            <List>
              {jobs.map(job => (
                <ListItem
                  key={job.id}
                  primaryText={`${job.projectName}: ${job.name}`}
                  leftIcon={
                    <Avatar src={job.avatar ? getMediaUrl(job.avatar, 128) : DEFAULT_PHOTO} />
                  }
                  rightIconButton={<span>{job.approved ? 'Approved' : ''}</span>}
                  href={`#/admin/approve/job/${job.id}`}
                />
              ))}
            </List>
            <ZeroResults results={jobs.length} text="There are no matching jobs." />
          </OnReady>

        </Paper>
      </div>
    )
  }
}
