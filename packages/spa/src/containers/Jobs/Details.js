import React, { Component, PropTypes } from 'react'
import Sugar from 'sugar-date'
import { connect } from 'react-redux'
import RaisedButton from 'material-ui/RaisedButton'
import { grey200 } from 'material-ui/styles/colors'

import { JOB_STATUS_TEXT_MAP } from '../../constants'
import * as confirmPromptActions from 'redux/modules/confirmPrompt'
import * as jobActions from 'redux/modules/job'
import sm from 'helpers/stylesMerge'
import Heading from 'components/Heading/Heading'
import CastingDetails from 'components/Jobs/CastingDetails'

const { bool, object, func } = PropTypes

const styles = {
  wrapper: {},
  wrapperSm: {
    marginLeft: 150,
    borderTop: `1px solid ${grey200}`,
    paddingTop: '1em',
  },
  heading: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    textTransform: 'capitalize',
  },
  confirmDecline: {
    margin: '1em',
  },
  statusText: {
    margin: '1em',
  },
  ul: {
    margin: 0,
    padding: 0,
    listStyle: 'none',
  },
}

@connect(
  state => ({
    job: state.job.data,
    account: state.account.data,
  }),
  {
    confirm: jobActions.confirm,
    decline: jobActions.decline,
    applyFor: jobActions.applyFor,
    openConfirmPrompt: confirmPromptActions.open,
    closeConfirmPrompt: confirmPromptActions.close,
  },
)
export default class JobDetails extends Component {
  static propTypes = {
    params: object.isRequired,
    account: object.isRequired,
    job: object.isRequired,
    confirm: func.isRequired,
    decline: func.isRequired,
    applyFor: func.isRequired,
    openConfirmPrompt: func.isRequired,
    closeConfirmPrompt: func.isRequired,
  };

  static contextTypes = {
    mediaQueries: object,
  };

  handleConfirmButton = () => {
    const { params: { jobId }, confirm, job: { membership } } = this.props
    const membershipId = membership.id

    confirm(jobId, membershipId)
  };

  handleDeclineButton = () => {
    const {
      params: { jobId },
      openConfirmPrompt,
      closeConfirmPrompt,
      decline,
      job: { membership },
    } = this.props
    const membershipId = membership.id

    return openConfirmPrompt(
      'Decline job?',
      'Are you sure you want to decline this job? This cannot be undone.',
      (confirmed) => {
        closeConfirmPrompt()

        if (confirmed) {
          decline(jobId, membershipId)
        }
      },
    )
  };

  handleApplyButton = () => {
    const { params: { jobId }, account, applyFor } = this.props
    const profileId = account && account.profiles[0] && account.profiles[0].id

    if (profileId) {
      applyFor(jobId, profileId)
    }
  };

  render() {
    const { mediaQueries: { xs } } = this.context
    const {
      job: {
        name,
        rateType,
        rate,
        currency,
        buyout,
        travel,
        objectives,
        type,
        usage,
        otherUsage,
        projectNotes,
        location,
        notes,
        dates = [],
        membership,
        castings = [],
      },
    } = this.props

    const status = membership && membership.status

    return (
      <div style={sm(styles.wrapper, !xs && styles.wrapperSm)}>

        <Heading text={name} weight={400} size={1.5} />

        <ul style={styles.ul}>
          <li><strong>Looking for</strong>: {objectives.join(', ')}</li>
          <li><strong>Rate</strong>: {currency.toUpperCase()} {rate} / {rateType} {buyout}</li>
          <li><strong>Travel</strong>: paid by {travel}</li>
          <li><strong>Job Location</strong>: {location}</li>
          <li>
            <strong>Call Time</strong>:
            {dates.length
              ? <ol>
                {dates.map(date => (
                  <li key={`${date.start}-${date.end}`}>
                    {Sugar.Date.format(new Date(date.start), '{full}')}
                  </li>
                  ))}
              </ol>
              : ''}
          </li>
          {notes && notes.length
            ? <li>
              <strong>Job notes</strong>:
                <blockquote>{notes.split('\n').map(line => <p>{line}</p>)}</blockquote>
            </li>
            : null}
          {projectNotes && projectNotes.length
            ? <li>
              <strong>Project notes</strong>:
                <blockquote>
                  {projectNotes.split('\n').map(line => <p>{line}</p>)}
                </blockquote>
            </li>
            : null}

        </ul>

        {membership && status === 'invited'
          ? <div style={styles.confirmDecline}>
            <Heading
              text="Congratulations! You have a Job Request:"
              weight={400}
              size={1.125}
              style={{ margin: '1em 0' }}
            />

            <RaisedButton
              primary={status === 'confirmed'}
              disabled={status === 'confirmed'}
              label="Confirm"
              onTouchTap={this.handleConfirmButton}
            />
            <RaisedButton
              primary={status === 'declined'}
              disabled={status === 'declined'}
              label="Decline"
              onTouchTap={this.handleDeclineButton}
            />
          </div>
          : null}

        {membership && status === 'confirmed'
          ? <div style={styles.confirmDecline}>
            <RaisedButton label="Cancel this job" onTouchTap={this.handleDeclineButton} />
          </div>
          : null}

        <div style={styles.statusText}>
          {JOB_STATUS_TEXT_MAP[status]}
        </div>

        {!membership && castings.length === 0 && !['released'].includes(status)
          ? <div style={styles.confirmDecline}>
            <RaisedButton
              primary={status === 'applied'}
              disabled={status === 'applied'}
              label="Apply for this job"
              onTouchTap={this.handleApplyButton}
            />
          </div>
          : ''}

        {!membership && castings
          ? <div>
            <Heading
              text="Congratulations! You have a Casting Request:"
              weight={400}
              size={1.125}
              style={{ margin: '1em 0' }}
            />
            {castings.map(casting => <CastingDetails key={casting.id} {...casting} />)}
          </div>
          : null}
      </div>
    )
  }
}
