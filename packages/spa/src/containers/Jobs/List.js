import React, { PureComponent, PropTypes } from 'react'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import IconButton from 'material-ui/IconButton'
import ActionViewListIcon from 'material-ui/svg-icons/action/view-list'
import ActionViewModuleIcon from 'material-ui/svg-icons/action/view-module'

import { LIST_JOB_STATUS_CODE_MAP } from '../../constants'
import * as jobsActions from 'redux/modules/jobs'
import sm from 'helpers/stylesMerge'
import If from 'components/If'
import JobCard from 'components/Jobs/JobCard'
import JobItem from 'components/Jobs/JobItem'
import ZeroResults from 'components/ZeroResults/ZeroResults'
import OnReady from 'components/OnReady/OnReady'
import Pills from 'components/Jobs/Pills'

const { bool, array, object, func, arrayOf } = PropTypes

const styles = {
  paper: {
    margin: '64px 0 0',
    padding: 0,
  },
  paperSm: {
    padding: '24px 24px 3em 24px',
  },
  gridWrapper: {
    display: 'flex',
    flexWrap: 'wrap',
    // justifyContent: 'space-around',
  },
  listWrapper: {},
}

const GridList = ({ type, filter, jobs = [], results = [], refresh, viewMode, setViewMode }) => {
  const data = (type === 'my-jobs' ? jobs : results).reduce(
    (list, { membership, castings, ...job }) =>
      !membership && castings && castings.length
        ? [...list, ...castings.map(casting => ({ ...job, ...casting, job, casting: true }))]
        : [...list, { ...job, membership, casting: false }],
    [],
  )
  const filteredData = data.filter((job) => {
    const status = job.membership && job.membership.status

    switch (filter) {
      case 'confirmed':
      case 'invited':
      case 'pending':
      case 'declined':
      case 'applied':
      case 'released':
        return filter === status
      case 'not-applied':
        return !job.membership && !job.casting && !['released'].includes(status)
      default:
        return true
    }
  })

  if (type === 'my-jobs') {
    filteredData.sort((first, second) => {
      const firstStatus = first.membership && first.membership.status
      const secondStatus = second.membership && second.membership.status

      return LIST_JOB_STATUS_CODE_MAP[secondStatus] - LIST_JOB_STATUS_CODE_MAP[firstStatus]
    }) // order by status, descending
  }

  return (
    <div>
      <div style={{ float: 'right' }}>
        <IconButton touch onTouchTap={setViewMode('list')} disabled={viewMode === 'list'}>
          <ActionViewListIcon />
        </IconButton>
        <IconButton touch onTouchTap={setViewMode('grid')} disabled={viewMode === 'grid'}>
          <ActionViewModuleIcon />
        </IconButton>
      </div>

      <Pills type={type} selection={filter} jobs={data} />
      <div style={viewMode === 'list' ? styles.listWrapper : styles.gridWrapper}>
        {filteredData.map(
          job =>
            viewMode === 'list'
              ? <JobItem key={job.id} refresh={refresh} publicCard={type === 'jobs'} {...job} />
              : <JobCard key={job.id} refresh={refresh} publicCard={type === 'jobs'} {...job} />,
        )}
      </div>
    </div>
  )
}

@connect(
  state => ({
    account: state.account.data,
    jobs: state.jobs.data.jobs,
    results: state.jobs.data.search,
    jobsLoaded: state.jobs.loaded,
    jobsLoading: state.jobs.loading,
  }),
  {
    jobsLoad: jobsActions.load,
  },
)
export default class JobsList extends PureComponent {
  static propTypes = {
    handleDrawerNavToggle: func,
    params: object,
    routes: arrayOf(object),
    account: object,
    jobs: array,
    results: array,
    jobsLoad: func,
    jobsLoaded: bool,
    jobsLoading: bool,
  }

  static contextTypes = {
    mediaQueries: object,
  }

  static defaultProps = {}

  state = {
    displayAlertDialog: false,
    viewMode: this.context.mediaQueries.xs ? 'list' : 'grid',
  }

  componentWillMount() {
    this.refresh()
  }

  componentWillReceiveProps(nextProps) {
    const { account, jobsLoad } = this.props
    const { account: nextAccount } = nextProps
    const profileId = nextAccount && nextAccount.profiles[0].id

    if (account !== nextAccount && profileId) {
      jobsLoad(profileId, {})
    }
  }

  setViewMode = viewMode => () => this.setState({ viewMode })

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle()

  refresh = (query = {}) => {
    const { account, jobsLoad } = this.props
    const profileId = account && account.profiles[0] && account.profiles[0].id

    if (profileId) {
      jobsLoad(profileId, query)
    }
  }

  get type() {
    const { routes } = this.props
    return routes[1].path.split('/')[0]
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { params: { filter = false }, account, jobs, results } = this.props
    const { viewMode } = this.state
    const isApproved = account && account.profiles[0] && account.profiles[0].approved

    return (
      <div>
        <AppBar
          title={this.type === 'my-jobs' ? 'My Jobs' : 'Public Jobs'}
          onLeftIconButtonTouchTap={this.handleDrawerNavToggle}
        />

        <Paper style={sm(styles.paper, !xs && styles.paperSm)} rounded={false} zDepth={0}>
          <OnReady ready={!!jobs && !!results}>
            <If
              if={isApproved && !!jobs && !!results}
              then={GridList}
              type={this.type}
              filter={filter}
              jobs={jobs}
              results={results}
              refresh={this.refresh}
              viewMode={viewMode}
              setViewMode={this.setViewMode}
            />
            <If
              if={!isApproved || (!jobs && !results)}
              then={ZeroResults}
              text={
                !isApproved
                  ? 'Your profile is pending approval. You will be able to view jobs after approval.'
                  : 'No jobs available'
              }
            />
          </OnReady>

        </Paper>
      </div>
    )
  }
}
