import React, { Children, PureComponent, PropTypes } from 'react'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import IconButton from 'material-ui/IconButton'
import NavigationArrowBackIcon from 'material-ui/svg-icons/navigation/arrow-back'
import { white } from 'material-ui/styles/colors'

import * as jobActions from 'redux/modules/job'
import sm from 'helpers/stylesMerge'
import noop from 'helpers/noop'
import If from 'components/If'
import OnReady from 'components/OnReady/OnReady'
import JobHeading from 'components/Jobs/Heading'
import ZeroResults from 'components/ZeroResults/ZeroResults'

const { bool, object, func, node } = PropTypes

const styles = {
  paper: {
    margin: '64px 0 0',
    padding: '8px 8px 2em 8px',
  },
  paperSm: {
    padding: '24px 24px 3em 24px',
  },
}

const Main = ({ job, content, returnTo, ...rest }) => (
  <Paper rounded={false} zDepth={0}>
    <JobHeading job={job} returnTo={returnTo} />
    {Children.map(content, child => React.cloneElement(child, rest))}
  </Paper>
)

@connect(
  state => ({
    job: state.job.data,
    loaded: state.job.loaded,
    loading: state.job.loading,
  }),
  {
    load: jobActions.load,
  },
)
export default class Project extends PureComponent {
  static propTypes = {
    children: node,
    handleDrawerNavToggle: func,
    params: object,
    location: object,
    job: object,
    loaded: bool,
    loading: bool,
    load: func,
  }

  static contextTypes = {
    router: object,
    mediaQueries: object,
  }

  componentWillMount() {
    const { load, params: { jobId } } = this.props

    load(jobId)
  }

  handleAppBarLeftIconButtonTouchTap = () => {
    const { location: { query: { returnTo } } } = this.props
    this.context.router.push(returnTo || '/jobs')
  }

  get title() {
    const { params: { castingId } } = this.props

    if (castingId) return 'Casting details'

    return 'Job details'
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const {
      location: { pathname, search, query: { returnTo } },
      children,
      loaded,
      job,
    } = this.props
    const doubleReturnTo = returnTo ? escape(pathname + search) : undefined

    return (
      <div>
        <AppBar
          title={this.title}
          titleStyle={styles.appBarTitle}
          iconElementLeft={
            <IconButton onTouchTap={this.handleAppBarLeftIconButtonTouchTap}>
              <NavigationArrowBackIcon/>
            </IconButton>
          }
        />

        <Paper style={sm(styles.paper, !xs && styles.paperSm)} rounded={false} zDepth={0}>
          <OnReady ready={loaded}>
            <If
              if={job && !!job.id}
              then={Main}
              job={job}
              content={children}
              returnTo={doubleReturnTo}
            />
            <If if={!job} then={ZeroResults} text="Job not found" />
          </OnReady>
        </Paper>
      </div>
    )
  }
}
