import React from 'react'

import AppBar from 'containers/AppBar/AppBar'

const styles = {
  paper: {
    margin: '64px 0 0 0',
    padding: '1.25em',
  },
}

export default function NotFound() {
  return (
    <div style={{ height: 'initial', backgroundColor: 'none' }}>
      <style dangerouslySetInnerHTML={{
        __html: `

          html, body, #root {
            height: 100%
          }

          #root {
            display: flex
            justify-content: space-around
            align-items: center

          }

          #loader {
            flex: 1 0 auto
          }


          /* via http://projects.lukehaas.me/css-loaders/ */
          .loader:before,
          .loader:after,
          .loader {
            border-radius: 50%
            width: 2.5em
            height: 2.5em
            -webkit-animation-fill-mode: both
            animation-fill-mode: both
            -webkit-animation: spinner 1.8s infinite ease-in-out
            animation: spinner 1.8s infinite ease-in-out
          }

          .loader {
            font-size: 1em
            margin: -3.25em auto
            position: relative
            text-indent: -9999em
            -webkit-transform: translateZ(0)
            -ms-transform: translateZ(0)
            transform: translateZ(0)
            -webkit-animation-delay: -0.16s
            animation-delay: -0.16s
          }

          .loader:before {
            left: -3.5em
            -webkit-animation-delay: -0.32s
            animation-delay: -0.32s
          }

          .loader:after {
            left: 3.5em
          }

          .loader:before,
          .loader:after {
            content: ''
            position: absolute
            top: 0
          }

          @-webkit-keyframes spinner {
            0%,
            80%,
            100% {
              box-shadow: 0 2.5em 0 -1.3em #ff034e
            }
            40% {
              box-shadow: 0 2.5em 0 0 #ff034e
            }
          }

          @keyframes spinner {
            0%,
            80%,
            100% {
              box-shadow: 0 2.5em 0 -1.3em #ff034e
            }
            40% {
              box-shadow: 0 2.5em 0 0 #ff034e
            }
          }`,
      }}
      />

      <AppBar
        title=""
        onLeftIconButtonTouchTap={this.handleDrawerNavToggle}
      />

      <div id="loader">
        <div className="loader">Loading...</div>
      </div>
    </div>
  )
}
