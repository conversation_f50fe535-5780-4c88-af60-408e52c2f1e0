import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import { DEFAULT_AVATAR } from '../../constants'
import { white, blueGrey50 } from 'material-ui/styles/colors'

import Paper from 'material-ui/Paper'
import { List } from 'material-ui/List'

import AvRecentActorsIcon from 'material-ui/svg-icons/av/recent-actors'
import ActionWorkIcon from 'material-ui/svg-icons/action/work'
import EditorInsertCommentIcon from 'material-ui/svg-icons/editor/insert-comment'
import AppBar from 'containers/AppBar/AppBar'

import * as profileActions from 'redux/modules/profile'
import * as projectsActions from 'redux/modules/projects'


import Heading from 'components/Heading/Heading'
import JobItem from 'components/Jobs/JobItem'
import ProjectItem from 'components/Projects/ProjectItem'
import { CoverName } from 'components/Profile/Profile'
import Feed from 'components/Feed/Feed'

import sm from 'helpers/stylesMerge'
import getMediaUrl from 'helpers/getMediaUrl'
import Theme from 'theme/mui-theme'

const { array, object, func } = PropTypes

const { primary1Color, primary2Color } = Theme.palette

const styles = {
  wrapper: {
    display: 'flex',
    flexWrap: 'wrap',
    alignContent: 'stretch',
    alignItems: 'stretch',
  },
  paper: {
    margin: '64px 0 0 0',
    backgroundColor: blueGrey50,
  },
  wrapperPaper: {
    paddingTop: 24,
    backgroundColor: blueGrey50,
  },
  bookings: {
    backgroundColor: blueGrey50,
  },
  bookingsSm: {
    width: '50%',
  },
  main: {
    backgroundColor: blueGrey50,
  },
  mainSm: {
    flex: 1,
    marginLeft: 24,
  },
  iconGrid: {
    margin: '0 auto',
    display: 'flex',
    flexWrap: 'wrap',
    alignItems: 'stretch',
    justifyContent: 'space-around',
  },
  iconGridItem: {
    flex: '0 0 auto',
    borderRadius: 3,
    margin: 10,
  },
  iconGridItemInsignia: {
    padding: '32px 40px',
    background: white,
  },
  iconButton: {
    padding: '32px 40px',
    width: 'initial',
    height: 'initial',
    background: '#fafafa',
  },
  icon: {
    fontSize: 24,
    width: '4em',
    height: '4em',
    display: 'block',
    margin: '0 auto',
  },
  coverWrapper: {
    position: 'relative',
    display: 'flex',
    height: 216,
    margin: '1em 0 1em 0',
  },
  coverInfo: {
    flex: 1,
    textAlign: 'right',
    width: 'calc(100% - 200px)',
    height: 156,
    background: white,
    margin: '30px 0 30px 0',
    alignItems: 'center',
    display: 'flex',
    paddingRight: 80,
  },
  coverSpacer: {
    flex: '0 0 150px',
    height: 156,
    margin: '30px 0 30px 0',
  },
  avatar: {
    width: 200,
    height: 200,
    backgroundColor: primary2Color,
    backgroundSize: 'cover',
    backgroundPosition: 'top center',
    border: `8px solid ${blueGrey50}`,
    borderRadius: '100%',
    right: 0,
    zIndex: 1,
    position: 'absolute',
  },
  newsWrapper: {},
  newsItem: {
    display: 'flex',
    background: white,
    margin: '3em 0 3em',
    position: 'relative',
  },
  newsAvatar: {
    width: 120,
    height: 120,
    backgroundColor: primary2Color,
    backgroundSize: 'cover',
    backgroundPosition: 'top center',
    border: `8px solid ${blueGrey50}`,
    borderRadius: '100%',
    zIndex: 1,
    position: 'absolute',
    top: -20,
  },
  newsItemDetails: {
    paddingLeft: 140,
  },
}

@connect(
  state => ({
    session: state.session.data,
    account: state.account.data,
    profile: state.profile.data,
    projects: state.projects.data,
  }),
  {
    profileLoad: profileActions.load,
    projectsLoad: projectsActions.load,
  },
)
export default class Dashboard extends Component {
  static propTypes = {
    handleDrawerNavToggle: func,
    session: object,
    account: object,
    profile: object,
    projects: array,
    profileLoad: func,
    projectsLoad: func,
  };

  static contextTypes = {
    router: object,
    session: object,
    mediaQueries: object,
  };

  componentWillMount() {
    const { account, profileLoad, projectsLoad } = this.props

    if (account && account.profiles && account.profiles.length) {
      if (account.permissions.representative) {
        projectsLoad()
      }

      profileLoad(account.profiles[0].id, false, true)
    }
  }

  componentWillReceiveProps({ account: nextAccount }) {
    const { account, profileLoad, projectsLoad } = this.props

    if (account !== nextAccount) {
      if (nextAccount && nextAccount.profiles && nextAccount.profiles.length) {
        if (account.permissions.representative) {
          projectsLoad()
        }

        profileLoad(nextAccount.profiles[0].id, false, true)
      }
    }
  }

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle();

  render() {
    const { mediaQueries: { xs } } = this.context
    const { account, profile, projects } = this.props
    const isRepresentative = account && account.permissions && account.permissions.representative

    let jobs = []
    let castings = []

    if (profile && profile.jobs && profile.jobs.length) {
      jobs = profile.jobs.filter(job => !job.castings.length)
      castings = profile.jobs.filter(job => job.castings.length)
    }

    return (
      <div>
        <AppBar
          title="Dashboard"
          onLeftIconButtonTouchTap={this.handleDrawerNavToggle}
        />

        <Paper style={styles.paper} rounded={false} zDepth={0}>

          <Paper style={styles.wrapperPaper} rounded={false} zDepth={0}>
            <div style={sm(!xs && styles.wrapper)}>

              <Paper
                style={sm(styles.bookings, !xs && styles.bookingsSm)}
                rounded={false}
                zDepth={0}
              >
                <div style={styles.coverWrapper}>
                  <div style={styles.coverInfo}>
                    <div style={{ flex: 1 }}>
                      <CoverName
                        type={
                          (profile && profile.type) ||
                            (account && account.permissions && account.permissions.admin && 'admin')
                        }
                        profile={
                          profile ||
                            (account &&
                            account.permissions &&
                            account.permissions.admin && {
                              name: `${account.firstName} ${account.lastName}`,
                            })
                        }
                        style={{ flex: 1 }}
                      />
                    </div>
                  </div>
                  <div style={styles.coverSpacer} />
                  <div
                    style={sm(styles.avatar, {
                      backgroundImage: `url('${profile && profile.avatar && profile.avatar.length ? getMediaUrl(profile.avatar) : DEFAULT_AVATAR}')`,
                    })}
                  />
                </div>

                {jobs.length
                  ? <div>
                    <Heading
                      size={2}
                      text={<span><ActionWorkIcon color={primary1Color} /> My Jobs</span>}
                      margin
                    />

                    <List style={{ background: white }}>
                      {jobs.map(job => <JobItem key={job.id} returnTo="/" {...job} />)}
                    </List>

                  </div>
                  : <div />}

                {castings.length
                  ? <div>
                    <Heading
                      size={2}
                      text={<span><ActionWorkIcon color={primary1Color} /> My Castings</span>}
                      margin
                    />

                    <List style={{ background: white }}>
                      {castings.map(job => <JobItem key={job.id} returnTo="/" {...job} />)}
                    </List>

                  </div>
                  : <div />}

                {profile && profile.projects && profile.projects.length
                  ? <div>
                    <Heading
                      size={2}
                      text={<span><ActionWorkIcon color={primary1Color} /> Latest Jobs</span>}
                      margin
                    />

                    <List style={{ background: white }}>
                      {profile.projects.map(job => (
                        <JobItem key={job.id} returnTo="/" {...job} />
                        ))}
                    </List>

                  </div>
                  : <div />}

                {isRepresentative && projects && projects.length
                  ? <div>
                    <Heading
                      size={2}
                      text={<span><AvRecentActorsIcon color={primary1Color} /> Projects</span>}
                      margin
                    />

                    <List style={{ background: white }}>
                      {projects.map(job => <ProjectItem key={job.id} returnTo="/" {...job} />)}
                    </List>

                  </div>
                  : <div />}

              </Paper>

              <Paper style={sm(styles.main, !xs && styles.mainSm)} rounded={false} zDepth={0}>
                <Heading
                  size={2}
                  text={<span><EditorInsertCommentIcon color={primary1Color} /> Blog</span>}
                  margin
                />

                <Feed />
              </Paper>
            </div>
          </Paper>
        </Paper>
      </div>
    )
  }
}
