import React, { Children, Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import IconButton from 'material-ui/IconButton'
import FlatButton from 'material-ui/FlatButton'
import IconMenu from 'material-ui/IconMenu'
import MenuItem from 'material-ui/MenuItem'
import NavigationArrowBackIcon from 'material-ui/svg-icons/navigation/arrow-back'
import MoreVertIcon from 'material-ui/svg-icons/navigation/more-vert'
import { white, grey400 } from 'material-ui/styles/colors'

import * as projectActions from 'redux/modules/project'
import * as bookingActions from 'redux/modules/booking'
import * as castingActions from 'redux/modules/casting'
import { parentPath } from 'helpers/routing'
import sm from 'helpers/stylesMerge'
import noop from 'helpers/noop'
import If from 'components/If'
import OnReady from 'components/OnReady/OnReady'
import ProjectHeading from 'components/Projects/Heading'
import ConfirmPrompt from 'components/Dialogs/Confirm'
import AlertPrompt from 'components/Dialogs/Alert'

const { bool, array, object, func, node, arrayOf } = PropTypes

const styles = {
  paper: {
    margin: '64px 0 0',
    padding: '0 0 2em',
  },
  paperSm: {
    padding: '24px 24px 3em 24px',
  },
}

const Main = ({ project, content, isDashboard, isProjectEdit, ...rest }) => (
  <Paper rounded={false} zDepth={0}>
    <ProjectHeading isDashboard={isDashboard} mutable={isProjectEdit} project={project} />
    {Children.map(content, child => React.cloneElement(child, rest))}
  </Paper>
)

@connect(
  state => ({
    projectError: state.project.errors,
    project: state.project.data,
    loaded: state.project.loaded,
    loading: state.project.loading,
  }),
  {
    projectLoad: projectActions.load,
    projectMoveToList: projectActions.moveToList,
    projectSave: projectActions.save,
    projectCancel: projectActions.cancel,
    bookingCancel: bookingActions.cancel,
    castingCancel: castingActions.cancel,
  },
)
export default class Project extends Component {
  static propTypes = {
    children: node,
    handleDrawerNavToggle: func,
    params: object,
    location: object,
    routes: arrayOf(object),
    projectError: array,
    project: object,
    loaded: bool,
    loading: bool,
    projectLoad: func.isRequired,
    castingCancel: func.isRequired,
    bookingCancel: func.isRequired,
    projectCancel: func.isRequired,
  };

  static contextTypes = {
    router: object,
    store: object,
    mediaQueries: object,
  };

  state = {
    nextAction: noop,
    nextActionButton: null,
    nextActionButtonIsDisabled: false,
    confirmDeleteDialog: {},
    alertPromptDialog: {},
  };

  componentWillMount() {
    const { projectLoad, params: { projectId } } = this.props

    projectLoad(projectId)

    window.scrollTo(0, 0)
  }

  componentWillReceiveProps(nextProps) {
    const { children } = this.props

    if (children !== nextProps.children) this.setNextAction()

    // this.requireBookingOrRedirect(nextProps)
  }

  setNextAction = (label = null, enabled = false, action = noop) => {
    this.setState({
      nextAction: action,
      nextActionButton: label,
      nextActionButtonIsDisabled: !enabled,
    })
  };

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle();

  handleAppBarBackButtonTouchTap = () => {
    const {
      location: { pathname },
      params: { listType, castingId, bookingId, castingsOrBookings },
    } = this.props
    const parent = parentPath(pathname)

    if (listType) return this.context.router.push(parentPath(parent))

    return this.context.router.push(parent)
  };

  handleNextActionButton = () => {
    const { nextAction } = this.state

    nextAction()
  };

  handleRightMenuEdit = () => {
    const {
      params: { projectId, listType, listId },
    } = this.props

    if (this.isDashboard) return this.context.router.push(`/projects/${projectId}/edit`)

    return this.context.router.push(`/projects/${projectId}/${listType}/${listId}/edit`)
  };

  handleRightMenuDelete = () => {
    const { params: { listType, listId }, project } = this.props
    const type = this.isDashboard ? 'project' : listType
    const bookings = project.bookings

    if (type === 'project') {
      if (bookings.length) {
        return this.setState({
          alertPromptDialog: {
            onClose: this.handleAlertPromptDialog,
            title: 'Oops!',
            message: 'You must first delete all bookings in this project before you can delete the project.',
          },
        })
      }
    } else if (type === 'booking') {
      const castings = project.castings.filter(casting => casting.booking.id === listId)

      if (castings.length) {
        return this.setState({
          alertPromptDialog: {
            onClose: this.handleAlertPromptDialog,
            title: 'Oops!',
            message: 'You must first delete all castings linked with this booking before you can delete the booking.',
          },
        })
      }
    }

    return this.setState({
      confirmDeleteDialog: {
        onConfirm: this.handleConfirmDeleteDialog,
        title: `Delete ${type}`,
        message: `Are you sure you want to delete this ${type}? This cannot be undone.`,
      },
    })
  };

  handleAlertPromptDialog = () => this.setState({ alertPromptDialog: {} });

  handleConfirmDeleteDialog = (confirmed) => {
    const {
      params: { projectId, listType, listId },
      castingCancel,
      bookingCancel,
      projectCancel,
    } = this.props

    if (confirmed) {
      if (this.isDashboard) {
        projectCancel(projectId).then(() => {
          this.context.router.push('/projects')
        })
      } else {
        const cancel = listType === 'casting' ? castingCancel : bookingCancel

        cancel(listId).then(() => {
          this.context.router.push(`/projects/${projectId}/`)
        })
      }
    }

    this.setState({ confirmDeleteDialog: {} })
  };

  requireBookingOrRedirect = (props) => {
    const { routes, params: { projectId }, project, loading } = props

    if (
      !loading &&
      project &&
      !project.bookings.length &&
      !['edit', 'booking/create'].includes(routes[2].path)
    ) {
      this.context.router.replace(`/projects/${projectId}/booking/create`)
    }
  };

  get title() {
    const {
      routes,
      params: { listType, castingId, bookingId, castingsOrBookings },
    } = this.props

    if (castingId) return 'Edit casting details'
    if (bookingId) return 'Edit booking details'
    if (castingsOrBookings) return `Project ${castingsOrBookings}`
    if (listType) return `${listType.slice(0, 1).toUpperCase()}${listType.slice(1)}`
    if (routes[2].path === 'edit') return 'Edit project details'

    return 'Project'
  }

  get isDashboard() {
    const { routes } = this.props

    return routes[2].path === ':castingsOrBookings' || typeof routes[2].path === 'undefined'
  }

  get isProjectEdit() {
    const { routes } = this.props

    return routes[2].path === 'edit'
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { children, loaded, project } = this.props
    const {
      nextActionButton,
      nextActionButtonIsDisabled,
      confirmDeleteDialog,
      alertPromptDialog,
    } = this.state

    return (
      <div>
        <AppBar
          title={this.title}
          onLeftIconButtonTouchTap={this.isDashboard ? this.handleDrawerNavToggle : null}
          iconElementLeft={
            !this.isDashboard
              ? <IconButton onTouchTap={this.handleAppBarBackButtonTouchTap}>
                <NavigationArrowBackIcon/>
              </IconButton>
              : null
          }
          iconElementRight={
            nextActionButton
              ? <FlatButton
                label={nextActionButton}
                onTouchTap={this.handleNextActionButton}
                style={sm(nextActionButtonIsDisabled && { opacity: 0.4 })}
                disabled={nextActionButtonIsDisabled}
              />
              : <IconMenu
                iconButtonElement={
                  <IconButton touch>
                    <MoreVertIcon color={grey400} />
                  </IconButton>
                  }
              >
                <MenuItem onTouchTap={this.handleRightMenuEdit}>Edit</MenuItem>
                <MenuItem onTouchTap={this.handleRightMenuDelete}>Delete</MenuItem>
              </IconMenu>
          }
        />

        <Paper style={sm(styles.paper, !xs && styles.paperSm)} rounded={false} zDepth={0}>
          <OnReady ready={loaded && !!project}>
            <If
              if={loaded && project && !!project.id}
              then={Main}
              project={project}
              content={children}
              setNextAction={this.setNextAction}
              isDashboard={this.isDashboard}
              isProjectEdit={this.isProjectEdit}
            />
          </OnReady>
        </Paper>

        {/* TODO: this should be refactored to use the confirmPrompt redux action and shared ConfirmPrompt in App */}
        <ConfirmPrompt
          open={!!confirmDeleteDialog.onConfirm}
          onUserResponse={this.handleConfirmDeleteDialog}
          title={confirmDeleteDialog.title}
          message={confirmDeleteDialog.message}
        />

        <AlertPrompt
          show={!!alertPromptDialog.onClose}
          title={alertPromptDialog.title}
          message={alertPromptDialog.message}
          onClose={alertPromptDialog.onClose}
        />
      </div>
    )
  }
}
