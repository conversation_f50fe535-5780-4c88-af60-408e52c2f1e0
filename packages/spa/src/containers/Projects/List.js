import React, { Component, PropTypes } from 'react'
import { connect } from 'react-redux'
import AppBar from 'containers/AppBar/AppBar'
import Paper from 'material-ui/Paper'
import FloatingActionButton from 'material-ui/FloatingActionButton'
import { List } from 'material-ui/List'
import ContentAddIcon from 'material-ui/svg-icons/content/add'

import * as projectActions from 'redux/modules/project'
import * as projectsActions from 'redux/modules/projects'
import PromptDialog from 'components/Dialogs/Prompt'
import AlertDialog from 'components/Dialogs/Alert'
import ProjectItem from 'components/Projects/ProjectItem'
import ZeroResults from 'components/ZeroResults/ZeroResults'
import OnReady from 'components/OnReady/OnReady'

const { bool, array, object, func } = PropTypes

const styles = {
  paper: {
    margin: '64px 0 0',
    padding: 0,
  },
  floatingAction: {
    position: 'fixed',
    bottom: '1.5em',
    right: '1.5em',
    zIndex: 1000,
  },
}

@connect(
  state => ({
    account: state.account.data,
    projectError: state.project.errors,
    projects: state.projects.data,
    projectsLoaded: state.projects.loaded,
    projectsLoading: state.projects.loading,
  }),
  {
    projectCreate: projectActions.create,
    projectsLoad: projectsActions.load,
  },
)
export default class Projects extends Component {
  static propTypes = {
    handleDrawerNavToggle: func,
    projectError: array,
    projectCreate: func,
    account: object,
    projects: array,
    projectsLoad: func,
    projectsLoaded: bool,
    projectsLoading: bool,
  };

  static contextTypes = {
    router: object,
  };

  static defaultProps = {
    projects: [],
  };

  state = {
    showCreateProjectPrompt: false,
    displayAlertDialog: false,
  };

  componentWillMount() {
    const { projectsLoad } = this.props
    projectsLoad()
  }

  handleDrawerNavToggle = () => this.props.handleDrawerNavToggle();

  handleErrorAlertClose = () => this.setState({ displayAlertDialog: false });

  handleCreatePromptShow = () => this.setState({ showCreateProjectPrompt: true });

  handleCreatePromptConfirm = (name) => {
    const { projectCreate, account } = this.props
    const profileId = account.profiles[0].id

    projectCreate(name, profileId, (error, project) => {
      if (error) {
        return this.setState({ showCreateProjectPrompt: false, displayAlertDialog: true })
      }

      this.setState({ showCreateProjectPrompt: false, dirty: false })
      return this.context.router.push(`/projects/${project.id}/edit`)
    })
  };

  handleCreatePromptCancel = () => this.setState({ showCreateProjectPrompt: false });

  render() {
    const { projectError, projects, projectsLoaded, account } = this.props
    const { showCreateProjectPrompt, displayAlertDialog } = this.state
    const isApproved = account && account.profiles && account.profiles[0].approved
    // const isApproved = false

    return (
      <div>
        <AppBar
          title="Projects"
          onLeftIconButtonTouchTap={this.handleDrawerNavToggle}
        />

        <Paper style={styles.paper} rounded={false} zDepth={0}>
          <OnReady ready={projectsLoaded}>
            <List>
              {isApproved && projects.map(project => <ProjectItem key={project.id} {...project} />)}
            </List>
            <ZeroResults
              results={isApproved && projects.length}
              text={
                isApproved
                  ? 'There are no projects.'
                  : 'Your profile is pending approval. You will be able to view and create projects after approval.'
              }
            />
          </OnReady>

          {isApproved
            ? <div style={styles.floatingAction}>
              <FloatingActionButton onTouchTap={this.handleCreatePromptShow}>
                <ContentAddIcon />
              </FloatingActionButton>
            </div>
            : ''}

        </Paper>

        <PromptDialog
          title="Enter project name"
          show={showCreateProjectPrompt}
          labelText="Project Name"
          hintText="Enter a name to identify your project."
          onCloseConfirm={this.handleCreatePromptConfirm}
          onCloseCancel={this.handleCreatePromptCancel}
        />

        <AlertDialog
          title="Uh oh!"
          show={displayAlertDialog}
          message="There was an error creating the project."
          errors={projectError}
          onClose={this.handleErrorAlertClose}
        />
      </div>
    )
  }
}
