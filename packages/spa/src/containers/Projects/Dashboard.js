import React, { PureComponent, PropTypes } from 'react'
import { findDOMNode } from 'react-dom'
import { connect } from 'react-redux'
import FloatingActionButton from 'material-ui/FloatingActionButton'
import { Popover, PopoverAnimationVertical } from 'material-ui/Popover'
import ContentAddIcon from 'material-ui/svg-icons/content/add'
import { grey400, grey600 } from 'material-ui/styles/colors'

import * as projectActions from 'redux/modules/project'
import sm from 'helpers/stylesMerge'
import If from 'components/If'
import Pills from 'components/Projects/DashboardPills'
import TalentOverviewGrid from 'components/Projects/TalentOverviewGrid'

const { bool, array, object, func } = PropTypes

const styles = {
  fab: {
    position: 'fixed',
    bottom: '1.5em',
    right: '1.5em',
    zIndex: 1000,
  },
  popover: {
    backgroundColor: 'transparent',
    boxShadow: 'none',
  },
  popoverInner: {
    padding: 8,
  },
  popoverInnerRow: {
    display: 'flex',
  },
  popoverLabel: {
    width: 60,
    // margin: 8,
    textAlign: 'right',
    textShadow: 'rgba(0, 0, 0, 0.156863) 0px 3px 10px, rgba(0, 0, 0, 0.227451) 0px 3px 10px',
    backgroundColor: 'white',
    borderRadius: 8,
    height: 22,
    padding: '1px 18px 1px 3px',
    marginTop: 8,
    marginRight: -10,
  },
  popoverButton: {
    marginBottom: 10,
  },
  popoverSpacer: {
    width: 70, //67,
  },
}

@connect(
  state => ({
    project: state.project.data,
  }),
  {},
)
export default class ProjectDashboard extends PureComponent {
  static propTypes = {
    project: object,
    params: object,
  };

  state = {
    floatingActionButtonOpen: false,
  };

  toggleFloatingActionButton = () =>
    this.setState({
      floatingActionButtonOpen: !this.state.floatingActionButtonOpen,
    });

  hideFloatingActionButton = () => this.setState({ floatingActionButtonOpen: false });

  handleSetButtonPopOver = (popover) => {
    this.popover = popover
  };

  makeFabRef = (node) => {
    this.actionButton = findDOMNode(node)
  };

  render() {
    const { params: { projectId, castingsOrBookings }, project } = this.props
    const { floatingActionButtonOpen } = this.state

    return (
      <div>
        <Pills projectId={projectId} selection={castingsOrBookings} />
        <TalentOverviewGrid type={castingsOrBookings} projectId={projectId} project={project} />

        <FloatingActionButton
          secondary
          style={styles.fab}
          onTouchTap={this.toggleFloatingActionButton}
          ref={this.makeFabRef}
        >
          <ContentAddIcon />
        </FloatingActionButton>

        <Popover
          open={floatingActionButtonOpen}
          anchorOrigin={{ horizontal: 'middle', vertical: 'top' }}
          targetOrigin={{ horizontal: 'middle', vertical: 'bottom' }}
          anchorEl={this.actionButton}
          animated
          animation={PopoverAnimationVertical}
          useLayerForClickAway={false}
          autoCloseWhenOffScreen={false}
          style={styles.popover}
          onRequestClose={this.hideFloatingActionButton}
          ref={this.handleSetButtonPopOver}
        >
          <div style={styles.popoverInner}>
            <div style={styles.popoverInnerRow}>
              <div style={styles.popoverLabel}>
                Casting
              </div>
              <FloatingActionButton
                href={`#/projects/${projectId}/casting/create`}
                mini
                backgroundColor={grey600}
                style={styles.popoverButton}
              >
                <ContentAddIcon />
              </FloatingActionButton>
              <div style={styles.popoverSpacer} />
            </div>
            <div style={styles.popoverInnerRow}>
              <div style={styles.popoverLabel}>
                Booking
              </div><FloatingActionButton
                href={`#/projects/${projectId}/booking/create`}
                mini
                backgroundColor={grey600}
                style={styles.popoverButton}
              >
                <ContentAddIcon />
              </FloatingActionButton>
              <div style={styles.popoverSpacer} />
            </div>
          </div>
        </Popover>

      </div>
    )
  }
}
