import React, { Component, PropTypes } from 'react'
import ReactCSSTransitionGroup from 'react-addons-css-transition-group'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import RaisedButton from 'material-ui/RaisedButton'
import Toggle from 'material-ui/Toggle'
import { RadioButton, RadioButtonGroup } from 'material-ui/RadioButton'
import { grey400 } from 'material-ui/styles/colors'

import * as bookingActions from 'redux/modules/booking'
import sm from 'helpers/stylesMerge'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'

const { bool, array, object, func } = PropTypes

const styles = {
  wrapper: {
    transition: 'all 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms',
  },
  wrapperSm: {
    margin: '0 10vw',
    border: `2em solid ${grey400}`,
  },
  form: {
    margin: 0,
  },
  formSm: {
    padding: '2em',
  },
  saveButton: {
    float: 'right',
  },
}

const rateTypeOptions = [{ value: 'day', label: 'Day Rate' }, { value: 'hour', label: 'Hourly' }]

const currencyOptions = [{ value: 'usd', label: 'US Dollar' }]

const objectiveOptions = [
  { value: 'female', label: 'Female model' },
  { value: 'male', label: 'Male model' },
  { value: 'influencer', label: 'Influencer' },
]

const travelOptions = [
  { value: 'client', label: 'Paid by client' },
  { value: 'talent', label: 'Paid by talent' },
]

const visibilityOptions = [
  { value: 'public', label: 'Public booking' },
  { value: 'private', label: 'Private booking' },
]

const initialState = {
  form: {},
  valid: false,
  dirty: false,
}

@connect(
  state => ({
    project: state.project.data,
    updates: state.booking.data,
  }),
  {
    save: bookingActions.save,
    unload: bookingActions.unload,
  },
)
export default class ProjectBookingEdit extends Component {
  static propTypes = {
    params: object,
    project: object,
    updates: object,
    save: func,
    unload: func.isRequired,
    setNextAction: func.isRequired,
  };

  static contextTypes = {
    router: object,
    store: object,
    mediaQueries: object,
  };

  state = initialState;

  componentWillMount() {
    const { unload, setNextAction } = this.props

    unload()

    const valid = this.validate()

    this.setState({ valid })

    setNextAction('Save', false)
  }

  componentWillUnmount() {
    this.props.unload()
  }

  handleFormUpdates = formUpdates =>
    this.setState((state) => {
      const form = { ...state.form, ...formUpdates }
      const valid = this.validate(form)

      this.props.setNextAction('Save', valid, this.handleSave)
      return { form, valid, dirty: true }
    });

  handleSave = () => {
    const { save, project, updates } = this.props
    const data = { ...this.state.form }

    if (!this.validate(data)) return false

    if (!this.booking && !updates.id) {
      data.project = project.id
    }

    return save(data).then(({ data: result }) => {
      const { params: { projectId } } = this.props
      const nextHref = `/projects/${projectId}/booking/${result.id}`

      this.setState(initialState)
      this.context.router.push(nextHref)
    })
  };

  validate = (input = {}) => {
    const { updates } = this.props
    const data = { ...this.booking, ...updates, ...input }

    if (Object.keys(data).length === 0) return false

    return ['name', 'location', 'rate'].reduce(
      (valid, field) =>
        valid && data[field] && (data[field].length || typeof data[field] === 'number'),
      true,
    )
  };

  get valid() {
    return this.state.valid
  }

  get booking() {
    const { params: { bookingId }, project } = this.props

    return project.bookings.find(({ id }) => id === bookingId)
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { updates } = this.props
    const { form, dirty } = this.state
    const defaults = !this.booking
      ? { seats: 1, rateType: 'day', currency: 'usd', visibility: 'public', travel: 'talent' }
      : {}
    const data = { ...defaults, ...this.booking, ...form, ...updates }

    return (
      <ReactCSSTransitionGroup
        transitionName="swipe"
        transitionAppear
        transitionAppearTimeout={500}
        transitionEnter={false}
        transitionLeave={false}
      >
        <Paper key="paper" style={sm(styles.wrapper, !xs && styles.wrapperSm)} zDepth={0}>
          <Form
            legend="Create a booking for this project"
            shouldDispatch={false}
            action={this.handleFormUpdates}
            data={data}
            style={sm(styles.form, !xs && styles.formSm)}
          >
            <Field
              mutable
              label="Booking name"
              name="name"
              fullWidth
              errorText={!data.name && 'A name is required.'}
            />
            <Field
              mutable
              label="Location"
              name="location"
              fullWidth
              errorText={!data.location && 'A location is required.'}
            />
            <Field
              mutable
              type="set"
              label="What are you looking for?"
              name="objectives"
              fullWidth
              options={objectiveOptions}
            />
            <Field type="number" mutable label="Number of models" name="seats" min={1} />
            <Field
              mutable
              type="select"
              label="Currency"
              name="currency"
              options={currencyOptions}
            />
            <Field
              mutable
              type="select"
              label="Job rate"
              name="rateType"
              options={rateTypeOptions}
            />
            <Field
              type="number"
              mutable
              label={data.rateType === 'hour' ? 'Hourly rate' : 'Day rate'}
              name="rate"
              errorText={!data.rate && 'A rate is required.'}
              min={0}
            />
            <Field type="number" mutable label="Buyout Fee" name="buyout" min={0} />
            <Field
              type="list"
              of="event"
              mutable
              label="Booking dates"
              name="dates"
              max={5}
              fullWidth
            />
            <Field
              mutable
              type="radio"
              label="Travel Arrangement"
              name="travel"
              fullWidth
              options={travelOptions}
            />
            <Field
              mutable
              type="radio"
              label="Visibility"
              name="visibility"
              fullWidth
              options={visibilityOptions}
            />
            <Field type="textarea" mutable rows={3} label="Notes" name="notes" fullWidth />

            {!xs && dirty
              ? <RaisedButton
                label="Save"
                primary
                style={styles.saveButton}
                disabled={!this.valid}
                onTouchTap={this.handleSave}
              />
              : null}
          </Form>
        </Paper>
      </ReactCSSTransitionGroup>
    )
  }
}
