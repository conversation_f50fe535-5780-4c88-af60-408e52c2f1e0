import React, { Component, PropTypes } from 'react'
import Sugar from 'sugar-date'
import { connect } from 'react-redux'
import FloatingActionButton from 'material-ui/FloatingActionButton'
import IconButton from 'material-ui/IconButton'
import ContentAddIcon from 'material-ui/svg-icons/content/add'
import EditorModeEditIcon from 'material-ui/svg-icons/editor/mode-edit'
import ActionThumbsUpDownIcon from 'material-ui/svg-icons/action/thumbs-up-down'
import { red500, grey600 } from 'material-ui/styles/colors'

import { LIST_MEMBER_STATUS_CODE_MAP } from '../../constants'
import Heading from 'components/Heading/Heading'
import Pills from 'components/Projects/TalentListFilterPills'
import TalentListGrid from 'components/Projects/TalentListGrid'

const { bool, array, object, func } = PropTypes

const styles = {
  wrapper: {},
  fab: {
    position: 'fixed',
    bottom: '1.5em',
    right: '1.5em',
    zIndex: 1000,
  },
  summary: {
    padding: 3,
  },
  heading: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    textTransform: 'capitalize',
  },
  row1: {
    display: 'flex',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  unapprovedAlert: {
    fontSize: '1.25em',
    color: red500,
  },
  subtitle: {
    color: grey600,
    fontSize: '90%',
  },
}

@connect(
  state => ({
    project: state.project.data,
  }),
  {},
)
export default class ProjectTalentList extends Component {
  static propTypes = {
    project: object,
    params: object,
  };

  static contextTypes = {
    mediaQueries: object,
  };

  get list() {
    const { params: { listType, listId }, project } = this.props
    const list = project[`${listType}s`].find(({ id }) => id === listId)

    return list || {}
  }

  get approved() {
    const { params: { listType } } = this.props

    return listType === 'casting'
      ? this.list.booking && this.list.booking.approved
      : this.list.approved
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { params: { projectId, listType, listId, filter } } = this.props
    const {
      name,
      location,
      visibility,
      dates = [],
      members = [],
      rateType,
      currency,
      rate,
      buyout,
    } = this.list

    const talentList = (filter &&
      members.filter((item) => {
        switch (filter) {
          case 'confirmed':
          case 'invited':
          case 'pending':
          case 'declined':
          case 'applied':
          case 'released':
            return item.status === filter
          case 'just-added':
            return item.updated > Date.now() - 1000 * 60 * 60 || item.status === 'pending'
          default:
            return true
        }
      })) ||
      members

    talentList.sort(
      (first, second) =>
        LIST_MEMBER_STATUS_CODE_MAP[second.status] - LIST_MEMBER_STATUS_CODE_MAP[first.status],
    ) // order by status, descending

    return (
      <div style={styles.wrapper}>

        <div style={styles.summary}>
          <div style={styles.row1}>
            <Heading
              text={`${listType}: ${name}`}
              weight={400}
              size={1.25}
              style={styles.heading}
            />

            {!this.approved
              ? <div style={styles.unapprovedAlert}>
                <ActionThumbsUpDownIcon
                  style={{ verticalAlign: 'middle', marginRight: 10 }}
                  color={red500}
                />
                  Pending Approval
                </div>
              : null}
          </div>
          <div style={styles.subtitle}>
            {location}
            {dates.length ? Sugar.Date.format(new Date(dates[0].start), ' on {full}') : ''}

            <IconButton
              href={`#/projects/${projectId}/${listType}/${listId}/edit`}
              iconStyle={{
                width: 20,
                height: 20,
              }}
            >
              <EditorModeEditIcon />
            </IconButton>

            <br />

            {listType === 'booking'
              ? <div>{currency && currency.toUpperCase()} {rate} / {rateType} {buyout}</div>
              : null}

          </div>
        </div>

        <Pills
          type={listType}
          projectId={projectId}
          listId={listId}
          selection={filter}
          members={members}
          visibility={visibility}
        />

        <TalentListGrid
          projectId={projectId}
          listId={listId}
          type={listType}
          approved={this.approved}
          data={talentList}
        />

        <FloatingActionButton
          style={styles.fab}
          href={`#/projects/${projectId}/${listType}/${listId}/search`}
        >
          <ContentAddIcon />
        </FloatingActionButton>
      </div>
    )
  }
}
