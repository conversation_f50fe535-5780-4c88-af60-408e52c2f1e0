import React, { Component, PropTypes } from 'react'
import ReactCSSTransitionGroup from 'react-addons-css-transition-group'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import RaisedButton from 'material-ui/RaisedButton'

import * as projectActions from 'redux/modules/project'
import sm from 'helpers/stylesMerge'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'

const { object, func } = PropTypes

const styles = {
  wrapper: {},
  wrapperSm: {},
  form: {
    margin: 0,
  },
  formSm: {},
  saveButton: {
    float: 'right',
  },
}

const projectTypeOptions = [
  { value: 'Campaign', label: 'Campaign' },
  { value: 'Catalogue', label: 'Catalogue' },
  { value: 'Commercial', label: 'Commercial' },
  { value: 'E-commerce', label: 'E-commerce' },
  { value: 'Editorial', label: 'Editorial' },
  { value: 'Lookbook', label: 'Lookbook' },
  { value: 'Runway', label: 'Runway' },
  { value: 'Showroom', label: 'Showroom' },
  { value: 'Social Media', label: 'Social Media' },
]

const projectUsageOptions = [
  { value: 'Internet', label: 'Internet' },
  { value: 'Print', label: 'Print' },
  { value: 'Social Media', label: 'Social Media' },
  { value: 'Television', label: 'Television' },
]

const initialState = {
  form: {},
  valid: false,
  dirty: false,
}

@connect(
  state => ({
    project: state.project.data,
  }),
  {
    save: projectActions.save,
  },
)
export default class ProjectEdit extends Component {
  static propTypes = {
    project: object.isRequired,
    save: func.isRequired,
    setNextAction: func.isRequired,
  };

  static contextTypes = {
    router: object,
    store: object,
    mediaQueries: object,
  };

  state = initialState;

  componentWillMount() {
    const valid = this.validate()

    this.setState({ valid })

    if (valid && !this.hasBookings) {
      this.props.setNextAction('Next', valid, this.transitionToBooking)
    } else {
      this.props.setNextAction('Save', false)
    }
  }

  handleFormUpdates = formUpdates =>
    this.setState((state) => {
      const form = { ...state.form, ...formUpdates }
      const valid = this.validate(form)

      this.props.setNextAction(this.hasBookings ? 'Save' : 'Next', valid, this.handleSave)
      return { form, valid, dirty: true }
    });

  handleSave = () => {
    const { save } = this.props
    const data = { ...this.state.form }

    if (!this.validate(data)) return false

    return save(data).then(({ data: result }) => {
      this.setState(initialState)

      if (!this.hasBookings) {
        this.transitionToBooking(result)
      } else {
        this.context.router.push(`/projects/${result.id}/`)
      }
    })
  };

  transitionToBooking = (project) => {
    const id = project ? project.id : this.props.project.id

    this.context.router.push(`/projects/${id}/booking/create`)
  };

  validate = (input = {}) => {
    const { project } = this.props
    const data = { ...project, ...input }

    if (Object.keys(data).length === 0) return false

    return ['name'].reduce((valid, field) => valid && data[field] && data[field].length, true)
  };

  get valid() {
    return this.state.valid
  }

  get hasBookings() {
    return this.props.project.bookings.length
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { project } = this.props
    const { form, dirty } = this.state
    const data = { ...project, ...form }

    return (
      <ReactCSSTransitionGroup
        transitionName="swipe"
        transitionAppear
        transitionAppearTimeout={500}
        transitionEnter={false}
        transitionLeave={false}
      >
        <Paper style={sm(styles.wrapper, !xs && styles.wrapperSm)} zDepth={0}>
          <Form
            shouldDispatch={false}
            action={this.handleFormUpdates}
            data={data}
            style={sm(styles.form, !xs && styles.formSm)}
          >
            <Field
              mutable
              label="Project Name"
              name="name"
              fullWidth
              errorText={!data.name && 'A project name is required.'}
            />
            <Field
              type="set"
              mutable
              label="Project Type"
              name="type"
              options={projectTypeOptions}
            />
            <Field type="set" mutable label="Usage" name="usage" options={projectUsageOptions} />
            {/* <Field mutable label="Other Usage" name="otherUsage" fullWidth /> */}
            <Field type="textarea" mutable rows={3} label="Project notes" name="notes" fullWidth />

            {!xs && dirty
              ? <RaisedButton
                label="Save"
                primary
                style={styles.saveButton}
                disabled={!this.valid}
                onTouchTap={this.handleSave}
              />
              : null}
          </Form>
        </Paper>
      </ReactCSSTransitionGroup>
    )
  }
}
