import React, { Component, PropTypes } from 'react'
import ReactCSSTransitionGroup from 'react-addons-css-transition-group'
import { connect } from 'react-redux'
import Paper from 'material-ui/Paper'
import RaisedButton from 'material-ui/RaisedButton'
import { grey400 } from 'material-ui/styles/colors'

import * as castingActions from 'redux/modules/casting'
import sm from 'helpers/stylesMerge'
import Form from 'components/Form/Form'
import Field from 'components/Form/Field'

const { bool, array, object, func } = PropTypes

const styles = {
  wrapper: {
    transition: 'all 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms',
  },
  wrapperSm: {
    margin: '0 10vw',
    border: `2em solid ${grey400}`,
  },
  form: {
    margin: 0,
  },
  formSm: {
    padding: '2em',
  },
  saveButton: {
    float: 'right',
  },
}

const getBookingSelectOptions = (bookings = []) =>
  bookings.map(booking => ({ value: booking.id, label: booking.name }))

const initialState = {
  form: {},
  valid: false,
  dirty: false,
}

@connect(
  state => ({
    project: state.project.data,
    updates: state.casting.data,
  }),
  {
    save: castingActions.save,
    unload: castingActions.unload,
  },
)
export default class ProjectCastingEdit extends Component {
  static propTypes = {
    params: object.isRequired,
    project: object.isRequired,
    updates: object.isRequired,
    save: func.isRequired,
    unload: func.isRequired,
    setNextAction: func.isRequired,
  };

  static contextTypes = {
    router: object,
    mediaQueries: object,
  };

  state = initialState;

  componentWillMount() {
    const { unload, setNextAction } = this.props

    unload()

    const valid = this.validate()

    this.setState({ valid })

    setNextAction('Save', false)
  }

  componentWillUnmount() {
    this.props.unload()
  }

  handleFormUpdates = formUpdates =>
    this.setState((state) => {
      const form = { ...state.form, ...formUpdates }
      const valid = this.validate(form)

      this.props.setNextAction('Save', valid, this.handleSave)
      return { form, valid, dirty: true }
    });

  handleSave = () => {
    const { save, project, updates } = this.props
    const data = { ...this.state.form }

    if (!this.validate(data)) return false

    if (!this.casting && !updates.id) {
      data.project = project.id
    }

    return save(data).then(({ data: result }) => {
      const { params: { projectId } } = this.props
      const nextHref = `/projects/${projectId}/casting/${result.id}`

      this.setState(initialState)
      this.context.router.push(nextHref)
    })
  };

  validate = (input = {}) => {
    const { updates } = this.props
    const data = { ...this.casting, ...updates, ...input }
    data.booking = data.booking && data.booking.id ? data.booking.id : data.booking

    if (Object.keys(data).length === 0) return false

    return ['booking', 'name', 'location'].reduce(
      (valid, field) => valid && data[field] && data[field].length,
      true,
    )
  };

  get valid() {
    return this.state.valid
  }

  get casting() {
    const { params: { castingId }, project } = this.props

    return project.castings.find(({ id }) => id === castingId)
  }

  render() {
    const { mediaQueries: { xs } } = this.context
    const { project, updates } = this.props
    const { form, dirty } = this.state
    const data = { ...this.casting, ...form, ...updates }
    const bookingOptions = getBookingSelectOptions(project.bookings)

    data.date = data.dates ? data.dates[0] : undefined
    data.booking = data.booking && data.booking.id ? data.booking.id : data.booking

    return (
      <ReactCSSTransitionGroup
        transitionName="swipe"
        transitionAppear
        transitionAppearTimeout={500}
        transitionEnter={false}
        transitionLeave={false}
      >
        <Paper style={sm(styles.wrapper, !xs && styles.wrapperSm)} zDepth={0}>
          <Form
            legend="Create a casting for this project"
            shouldDispatch={false}
            action={this.handleFormUpdates}
            data={data}
            style={sm(styles.form, !xs && styles.formSm)}
          >
            <Field
              mutable
              type="select"
              label="Link with booking"
              name="booking"
              options={bookingOptions}
              fullWidth
              errorText={!data.booking && 'This casing must be linked with a booking.'}
            />

            <Field
              mutable
              label="Casting name"
              name="name"
              fullWidth
              errorText={!data.name && 'A name is required.'}
            />
            <Field
              mutable
              label="Location"
              name="location"
              fullWidth
              errorText={!data.location && 'A location is required.'}
            />

            <Field type="event" mutable label="Date" name="date" clearable={false} fullWidth />

            <Field type="textarea" mutable rows={3} label="Notes" name="notes" fullWidth />

            {!xs && dirty
              ? <RaisedButton
                label="Save"
                primary
                style={styles.saveButton}
                disabled={!this.valid}
                onTouchTap={this.handleSave}
              />
              : null}
          </Form>
        </Paper>
      </ReactCSSTransitionGroup>
    )
  }
}
