import sha512 from 'crypto-js/sha512'
import React, { Component, PropTypes } from 'react'
import MuiThemeProvider from 'material-ui/styles/MuiThemeProvider'
import getMuiTheme from 'material-ui/styles/getMuiTheme'
import Paper from 'material-ui/Paper'
import RaisedButton from 'material-ui/RaisedButton'
import TextField from 'material-ui/TextField'
import { RadioButton, RadioButtonGroup } from 'material-ui/RadioButton'
import { white } from 'material-ui/styles/colors'

import theme from 'theme/mui-theme'
import ApiClient from 'helpers/fetchClient'
import { makeArgumentsString } from 'helpers/graphQL'
import sm from 'helpers/stylesMerge'
import Logo from 'components/Icons/Logo'
import Heading from 'components/Heading/Heading'
import Loading from 'components/Loading/Loading'

const { object } = PropTypes

const muiTheme = getMuiTheme(theme)

const { primary1Color } = theme.palette

const emailRegex = /^[-a-z0-9~!$%^&*_=+}{\'?]+(\.[-a-z0-9~!$%^&*_=+}{\'?]+)*@([a-z0-9_][-a-z0-9_]*(\.[-a-z0-9_]+)*\.(aero|arpa|biz|com|coop|edu|gov|info|int|mil|museum|name|net|org|pro|travel|mobi|[a-z][a-z])|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})?$/i // eslint-disable-line

const apiClient = new ApiClient()

const styles = {
  wrapper: {
    display: 'flex',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: primary1Color,
  },
  center: {
    width: '100%',
    margin: '1em',
    marginBottom: '2em',
    position: 'relative',
  },
  centerSm: {
    width: '30%',
    margin: '0 0 1em',
    marginBottom: '1em',
    minWidth: 300,
    maxWidth: 425,
  },
  alertOverlay: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 5,
    display: 'none',
    background: 'rgba(255,255,255, 0.9)',
  },
  logo: {
    fontSize: 24,
    height: 'auto',
    width: '40%',
    display: 'block',
    margin: '2em auto',
  },
  paper: {
    backgroundColor: white,
    padding: '1em',
    transition: 'all 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms',
  },
  heading: {
    background: primary1Color,
  },
  input: {
    width: '100%',
  },
  submit: {
    display: 'block',
    marginTop: 24,
  },
  twoColumn: {
    display: 'flex',
  },
  show: {
    display: 'block',
  },
  flexWrapper: {
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: '30%',
  },
  errorDiv: {
    padding: '1em',
    flex: 1,
  },
}

export default class SignUp extends Component {

  static contextTypes = {
    mediaQueries: object,
  }

  state = {
    submitted: false,
    loading: false,
    errors: [],

    type: 'model',
    email: null,
    passPhrase: null,
    passPhrase2: null,
    firstName: null,
    lastName: null,
  }

  handleFieldChange = name => event => this.setState({ [name]: event.target.value })

  handleTypeChange = (event, value) => this.setState({ type: value })

  handleSubmitButton = () => {
    const { type, firstName, lastName, email, passPhrase } = this.state
    const data = { firstName, lastName, email, passPhrase: sha512(passPhrase).toString() }

    this.setState({ loading: true })

    apiClient.post('/api/graphql', {
      data: {
        query: `mutation {
            createAccount(type: ${type} ${makeArgumentsString(data)}) {
              id
            }
          }`,
      },
    })
    .then(result => {
      const errors = result.errors || []

      this.setState({ loading: false, submitted: !!result.data.createAccount, errors })
    })
    .catch(error => {
      this.setState({ loading: false, errors: [error] })
    })
  }

  handleLoginButton = () => window.location = './'

  handleDismissAlertButton = () => this.setState({ errors: [] })

  render() {
    const { mediaQueries: { xs } } = this.context
    const { firstName, lastName, email, passPhrase, passPhrase2, loading, submitted, errors } = this.state
    const validEmail = email && emailRegex.test(email)
    const validPassPhrase = passPhrase === passPhrase2
    const canSubmit =
      firstName && firstName.trim().length
      && lastName && lastName.trim().length
      && email && validEmail
      && passPhrase && validPassPhrase

    return (
      <MuiThemeProvider muiTheme={muiTheme}>
        <div style={styles.wrapper}>
          <div>
            <Logo style={styles.logo} color="#fff" />
          </div>
          <div style={sm(styles.center, !xs && styles.centerSm)}>
            <Paper style={sm(styles.paper, styles.alertOverlay, (loading || errors.length) && styles.show)} zDepth={0}>
              <Loading loading={loading} />

              { submitted ?
                <Paper style={styles.flexWrapper} zDepth={1}>
                  <div style={styles.errorDiv}>
                    <p>Your account has been successfully created.</p>
                    <RaisedButton label="Login" primary style={styles.submit} touch onTouchTap={this.handleLoginButton} />
                  </div>
                </Paper>
                : null
              }

              { errors.length ?
                <Paper style={styles.flexWrapper} zDepth={1}>
                  <div style={styles.errorDiv}>
                    <Heading size={2} text="Uh oh" style={{ textAlign: 'center' }} />
                    { errors.map((error, index) => <div key={index}>{error.message}</div>) }
                    <RaisedButton label="Okay" primary style={styles.submit} touch onTouchTap={this.handleDismissAlertButton} />
                  </div>
                </Paper>
                : null
              }
            </Paper>

            <Paper style={styles.paper} zDepth={2}>
              <Heading size={2} text="Create an account" style={{ textAlign: 'center' }} />

              { submitted ?
                <div>
                  <p>Your account has been successfully created.</p>
                  <RaisedButton label="Login" primary style={styles.submit} touch onTouchTap={this.handleLoginButton} />
                </div>
                :
                <div>
                  <div style={sm(!xs && styles.twoColumn)}>
                    <RadioButtonGroup name="type" defaultSelected="model" style={{ display: 'flex', marginTop: '2em' }} onChange={this.handleTypeChange}>
                      <RadioButton
                        value="model"
                        label="Talent"
                        style={{ width: 'initial', marginRight: '1em' }}
                      />
                      <RadioButton
                        value="representative"
                        label="Representative"
                        style={{ width: 'initial' }}
                      />
                    </RadioButtonGroup>
                  </div>
                  <div style={sm(!xs && styles.twoColumn)}>
                    <TextField
                      hintText="First name"
                      floatingLabelText="First name"
                      style={styles.input}
                      onChange={this.handleFieldChange('firstName')}
                      fullWidth={xs}
                    />
                    <TextField
                      hintText="Last name"
                      floatingLabelText="Last name"
                      style={styles.input}
                      onChange={this.handleFieldChange('lastName')}
                      fullWidth={xs}
                    />
                  </div>

                  <div style={sm(!xs && styles.twoColumn)}>
                    <TextField
                      hintText="Email"
                      floatingLabelText="Email"
                      type="email"
                      style={styles.input}
                      onChange={this.handleFieldChange('email')}
                      errorText={email && !validEmail ? 'Please provide a valid email address' : undefined}
                    />
                  </div>

                  <div style={sm(!xs && styles.twoColumn)}>
                    <TextField
                      hintText="Pass phrase"
                      floatingLabelText="Pass phrase"
                      style={styles.input}
                      type="password"
                      onChange={this.handleFieldChange('passPhrase')}
                      fullWidth={xs}
                    />
                    <TextField
                      hintText="Pass phrase (again)"
                      floatingLabelText="Pass phrase (again)"
                      style={styles.input}
                      type="password"
                      onChange={this.handleFieldChange('passPhrase2')}
                      errorText={passPhrase && !validPassPhrase ? 'Doesn\'t match' : undefined}
                      fullWidth={xs}
                    />
                  </div>

                  <RaisedButton disabled={!canSubmit} label="Join Lookbook" primary style={styles.submit} touch onTouchTap={this.handleSubmitButton} />

                  <br />

                  <small>* All fields are required.</small>
                </div>
              }
            </Paper>
          </div>
        </div>
      </MuiThemeProvider>
    )
  }
}
