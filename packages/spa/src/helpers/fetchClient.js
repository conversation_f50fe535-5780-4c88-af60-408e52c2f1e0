import config from '../../config'
import query from 'fbjs/lib/xhrSimpleDataSerializer'

const methods = ['get', 'post', 'put', 'patch', 'del']

// uses XMLHttpRequest instead of Fetch API, so that we can listen to ProgressEvent
// only used for file-uploads
function futch(url, options = {}, onProgress) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open(options.method || 'get', url)

    if (options.headers) {
      Object.entries(options.headers).map(([key, value]) => xhr.setRequestHeader(key, value))
    }

    xhr.onload = (event) => {
      resolve(event.target)
    }

    xhr.onerror = reject

    if (xhr.upload && onProgress) {
      xhr.upload.onprogress = onProgress
    }

    xhr.send(options.body)
  })
}

function formatUrl(path) {
  const adjustedPath = path[0] !== '/' ? `/${path}` : path

  if (path.substr(0, 4) === 'http') {
    return path
  }

  return `//${config.apiHost}${config.apiPort ? `:${config.apiPort}` : ''}${adjustedPath}`
}

function checkStatus(response) {
  if (response.status >= 200 && response.status < 300) {
    return response
  }

  const error = new Error(response.statusText)
  error.response = response

  throw error
}

function parseJSON(response) {
  return typeof response.json === 'function' ? response.json() : JSON.parse(response.responseText)
}

export default class FetchClient {
  token = null

  constructor() {
    methods.forEach(
      method =>
        (this[method] = (path, { params, data } = {}, onProgress) =>
          new Promise((resolve, reject) => {
            let url = formatUrl(path)
            const dataOptions = this.getDataOptions(data)

            if (params) {
              url += `?${query(params)}`
            }

            let headers = {}

            if (dataOptions.headers) {
              headers = { ...dataOptions.headers }
            }

            if (this.token) {
              headers.Authorization = `JWT ${this.token}`
            }

            const options = {
              fetchTimeout: 15000 * 5,
              method,
              credentials: 'include',
              ...dataOptions,
              headers,
            }

            const fetching = onProgress ? futch(url, options, onProgress) : fetch(url, options)

            fetching
              .then(checkStatus)
              .then(parseJSON)
              .then((jsonData) => {
                if (__DEVELOPMENT__) {
                  console.log('fetchClient response:', jsonData) // eslint-disable-line no-console
                }

                return resolve(jsonData)
              })
              .catch((error) => {
                if (__DEVELOPMENT__) {
                  console.log('fetchClient request failed:', error) // eslint-disable-line no-console
                }

                return reject(error)
              })
          })),
    )
  }

  setToken = token => (this.token = token)

  getDataOptions = (data) => {
    if (!data) {
      return {}
    }

    if (data instanceof FormData) {
      return {
        body: data,
      }
    }

    return {
      body: JSON.stringify(data),
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  }
}
