import { pick } from 'helpers/object'
import sanitize from 'helpers/sanitize'

export function makeArgumentsArray(query = {}, enumFields = []) {
  return Object.keys(query)
    .filter(item => typeof query[item] !== 'undefined')
    .map(
      item => `${item}: ${enumFields.includes(item) ? query[item] : sanitize.byType(query[item])}`,
    )
}

export function makeArgumentsString(query = {}, enumFields = []) {
  const args = makeArgumentsArray(query, enumFields)

  return args.length ? `(${args.join(' ')})` : ''
}

export function pickArgumentString(object = {}, paths = [], enumFields = []) {
  return makeArgumentsString(pick(object, paths, { null: false }), enumFields)
}
