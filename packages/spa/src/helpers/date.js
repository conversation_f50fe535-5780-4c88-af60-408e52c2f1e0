// TODO: timezones. and locale
export function formatDate(_date) {
  let date

  date = new Date(_date)

  return date.toLocaleString()
}

export function todayXDaysAgo(days = 1) {
  const date = new Date(new Date().toDateString())
  date.setTime(date.getTime() - (1000 * 60 * 60 * 24 * days))

  return date
}

export function todayOneWeekAgo() {
  return todayXDaysAgo(7)
}

export function isInTheFuture(date = 0, expires = 0) {
  const now = Date.now()

  return date > now || expires > now
}
