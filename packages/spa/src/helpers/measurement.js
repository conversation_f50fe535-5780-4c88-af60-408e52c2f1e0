function round(value, accuracy = 100) {
  return (Math.round(value * accuracy) / accuracy).toFixed(accuracy.toString().length - 1)
}

export const convertUnits = (fromUnits, toUnits, amount) => {
  let converted = amount

  if (fromUnits === 'metric' && toUnits === 'imperial') {
    converted = round(amount * 0.39370)
  }

  if (fromUnits === 'imperial' && toUnits === 'metric') {
    converted = round(amount * 2.54)
  }

  return Number(converted)
}

export const getUnitAbbreviation = units => {
  switch (units) {
    case 'imperial':
      return 'in'
    case 'metric':
    default:
      return 'cm'
  }
}
