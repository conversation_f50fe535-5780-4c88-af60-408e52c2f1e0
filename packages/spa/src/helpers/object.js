export function countValues(object) {
  return Object.values(object)
    .reduce((count, value) => typeof value !== 'undefined' && count + 1 || count, 0)
}

export function pick(object = {}, paths = [], options = { null: true }) {
  return Object.keys(object)
    .filter(key => paths.includes(key))
    .reduce(
      (picked, key) => !object[key] && !options.null ? picked : { ...picked, [key]: object[key] },
      {},
    )
}

export function omit(object = {}, paths = []) {
  return Object.keys(object)
    .filter(key => !paths.includes(key))
    .reduce((picked, key) => ({ ...picked, [key]: object[key] }), {})
}
