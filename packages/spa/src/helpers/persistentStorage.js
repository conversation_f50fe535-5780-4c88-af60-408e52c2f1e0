import localforage from 'localforage'

const wrappedGetItem = storageKey => new Promise(resolve => {
  localforage.getItem(storageKey).then(data => {
    if (!data) data = {} // eslint-disable-line no-param-reassign
    return resolve(data)
  })
})

export default function (storageKey) {
  return {
    load: () => wrappedGetItem(storageKey),
    save: data => localforage.setItem(storageKey, data),
    delete: () => localforage.removeItem(storageKey),
  }
}
