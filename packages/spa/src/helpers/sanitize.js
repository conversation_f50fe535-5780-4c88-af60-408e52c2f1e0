export function text(textString) {
  if (!textString) {
    return ''
  }

  return textString
    .replace(/[\\]/g, '\\\\')
    .replace(/["]/g, '\\"')
    .replace(/[\/]/g, '\\/')
    .replace(/[\b]/g, '\\b')
    .replace(/[\f]/g, '\\f')
    .replace(/[\n]/g, '\\n')
    .replace(/[\r]/g, '\\r')
    .replace(/[\t]/g, '\\t')
}

export function json(jsonString) {
  return jsonString
    .replace(/[\\]/g, '\\\\')
    .replace(/[\"]/g, '\\\"')
    .replace(/[\/]/g, '\\/')
    .replace(/[\b]/g, '\\b')
    .replace(/[\f]/g, '\\f')
    .replace(/[\n]/g, '\\n')
    .replace(/[\r]/g, '\\r')
    .replace(/[\t]/g, '\\t')
}

export function byType(value) {
  switch (typeof value) {
    case 'boolean':
    case 'number':
      return value
    case 'object':
      if (value.length) {
        return `["${value.join('","')}"]`
      }

      return `"${value.toISOString()}"`
    case 'string':
    default:
      return `"${text(value)}"`
  }
}

export default {
  text,
  json,
  byType,
}
