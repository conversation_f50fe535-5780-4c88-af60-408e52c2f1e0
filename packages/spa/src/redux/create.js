import { createStore as _createStore, applyMiddleware, compose } from 'redux'
import { syncHistory } from 'react-router-redux'
import createMiddleware from './middleware/clientMiddleware'
import reducer from './modules/reducer'

export default function createStore(getRoutes, history, client, data) {
  // Sync dispatched route actions to the history
  const reduxRouterMiddleware = syncHistory(history)

  const middleware = [createMiddleware(client), reduxRouterMiddleware]

  let finalCreateStore
  if (__DEVELOPMENT__ && __DEVTOOLS__) {
    const { persistState } = require('redux-devtools')

    finalCreateStore = compose(
      applyMiddleware(...middleware),
      persistState(window.location.href.match(/[?&]debug_session=([^&]+)\b/)),
    )(_createStore)
  } else {
    finalCreateStore = applyMiddleware(...middleware)(_createStore)
  }

  const store = finalCreateStore(reducer, data)

  reduxRouterMiddleware.listenForReplays(store)

  if (__DEVELOPMENT__ && module.hot) {
    module.hot.accept('./modules/reducer', () => {
      store.replaceReducer(require('./modules/reducer'))
    })
  }

  return store
}
