import * as sessionActions from 'redux/modules/session'

export default function clientMiddleware(client) {
  return ({ dispatch, getState }) =>
    next => action => {
      if (typeof action === 'function') {
        return action(dispatch, getState)
      }

      const { promise, types, ...rest } = action
      if (!promise) {
        return next(action)
      }

      if (getState) {
        const session = getState().session
        const jwtToken = session && session.token

        if (jwtToken) {
          client.setToken(jwtToken)
        }
      }

      const [REQUEST, SUCCESS, FAILURE] = types
      next({ ...rest, type: REQUEST })

      const actionPromise = promise(client)

      actionPromise.then(
        result => next({ ...rest, result, type: SUCCESS }),
        error => {
          if (error.response && error.response.status === 401) {
            dispatch(sessionActions.expire())
          }

          return next({ ...rest, error, type: FAILURE })
        }
      ).catch((error) => {
        if (__DEVELOPMENT__) {
          console.error('MIDDLEWARE ERROR:', error) // eslint-disable-line no-console
        }

        next({ ...rest, error, type: FAILURE })
      })

      return actionPromise
    }
}
