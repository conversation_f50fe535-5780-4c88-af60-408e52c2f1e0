import { makeArgumentsString } from 'helpers/graphQL'

const LOAD = 'profiles/LOAD'
const LOAD_SUCCESS = 'profiles/LOAD_SUCCESS'
const LOAD_FAIL = 'profiles/LOAD_FAIL'

const initialState = {
  loading: true,
  loaded: false,
  action: 'load',
  data: [],
  error: null,
}

export function isLoaded(globalState) {
  return globalState.profiles && globalState.profiles.loaded
}

export function load(query = {}) {
  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client => client
      .get('/api/graphql', {
        params: {
          query: `{
            profiles${makeArgumentsString(query)} {
              id
              created
              updated
              account
              name
              slug
              approved
              type
              avatar
              cover
              height
              hairColor
              eyeColor
              gender
              compCardA
              compCardB
              compCardC
              compCardD
              compCardCover
              compCardLabelPosition
            }
          }`,
        },
      })
      .then(result => result),
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        action: 'load',
        loading: true,
        loaded: false,
        data: [],
        error: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        action: 'load',
        loading: false,
        loaded: true,
        data: action.result.data.profiles,
        error: action.result.errors || null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        action: 'load',
        loaded: false,
        loading: true,
        data: [],
        error: action.error,
      }

    default:
      return state
  }
}
