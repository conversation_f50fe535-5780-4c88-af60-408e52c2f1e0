import sanitize from 'helpers/sanitize'

const LOAD_JOBS = 'admin/LOAD_JOBS'
const LOAD_JOBS_SUCCESS = 'admin/LOAD_JOBS_SUCCESS'
const LOAD_JOBS_FAIL = 'admin/LOAD_JOBS_FAIL'
const LOAD_JOB = 'admin/LOAD_JOB'
const LOAD_JOB_SUCCESS = 'admin/LOAD_JOB_SUCCESS'
const LOAD_JOB_FAIL = 'admin/LOAD_JOB_FAIL'
const APPROVE_JOB = 'admin/APPROVE_JOB'
const APPROVE_JOB_SUCCESS = 'admin/APPROVE_JOB_SUCCESS'
const APPROVE_JOB_FAIL = 'admin/APPROVE_JOB_FAIL'
const DISAPPROVE_JOB = 'admin/DISAPPROVE_JOB'
const DISAPPROVE_JOB_SUCCESS = 'admin/DISAPPROVE_JOB_SUCCESS'
const DISAPPROVE_JOB_FAIL = 'admin/DISAPPROVE_JOB_FAIL'

const initialState = {
  loading: true,
  loaded: false,
  data: { jobs: [], job: {} },
}

const jobQueryFields = `
  id
  name
  approved
  visibility
  location
  objectives
  rateType
  rate
  currency
  buyout
  notes
  travel
  dates {
    start
    end
  }
  membership {
    id status profile
  }

  avatar
  projectName
  type
  usage
  otherUsage

  representative {
    id
    name
    companyName
    slug
  }

  castings {
    id
    name
    location
    notes
    dates {
      start
      end
    }

    membership {
      id status profile
    }
  }
`

export function loadJobs(profiles, query = {}) {
  // const fields = Object.keys(query)
  // const args = fields.map(key => `${key}: ${sanitize.byType(query[key])}`).join('\n')

  return {
    types: [LOAD_JOBS, LOAD_JOBS_SUCCESS, LOAD_JOBS_FAIL],
    promise: client =>
      client
        .get('/api/graphql', {
          params: {
            query: `{
            admin {
              jobs {
                ${jobQueryFields}
              }
            }
          }`,
          },
        })
        .then(result => ({ ...result, data: result.data.admin })),
  }
}

export function loadJob(jobId) {
  return {
    types: [LOAD_JOB, LOAD_JOB_SUCCESS, LOAD_JOB_FAIL],
    promise: client =>
      client
        .get('/api/graphql', {
          params: {
            query: `{
            admin {
              job(id: ${sanitize.byType(jobId)}) {
                ${jobQueryFields}
              }
            }
          }`,
          },
        })
        .then(result => ({ ...result, data: result.data.admin })),
  }
}

export function approveJob(jobId) {
  return {
    types: [APPROVE_JOB, APPROVE_JOB_SUCCESS, APPROVE_JOB_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          params: {
            query: `mutation ApproveJob($jobId: ID!) {
            admin {
              approveJob(id: $jobId) {
                ${jobQueryFields}
              }
            }
        }`,
            variables: JSON.stringify({ jobId }),
          },
        })
        .then(result => ({ ...result, data: { job: result.data.admin.approveJob } })),
  }
}

export function disapproveJob(jobId) {
  return {
    types: [DISAPPROVE_JOB, DISAPPROVE_JOB_SUCCESS, DISAPPROVE_JOB_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          params: {
            query: `mutation DisapproveJob($jobId: ID!) {
            admin {
              disapproveJob(id: $jobId) {
                ${jobQueryFields}
              }
            }
        }`,
            variables: JSON.stringify({ jobId }),
          },
        })
        .then(result => ({ ...result, data: { job: result.data.admin.disapproveJob } })),
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD_JOBS:
    case LOAD_JOB:
      return {
        ...state,
        loading: true,
        loaded: false,
        data: initialState.data,
        errors: null,
      }
    case LOAD_JOBS_SUCCESS:
    case LOAD_JOB_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        ...action.result,
      }
    case LOAD_JOBS_FAIL:
    case LOAD_JOB_FAIL:
      return {
        ...state,
        loaded: false,
        loading: true,
        data: initialState.data,
        errors: [action.error],
      }

    case APPROVE_JOB:
    case DISAPPROVE_JOB:
      return {
        ...state,
        errors: null,
      }
    case APPROVE_JOB_SUCCESS:
    case DISAPPROVE_JOB_SUCCESS:
      return {
        ...state,
        ...action.result,
      }
    case APPROVE_JOB_FAIL:
    case DISAPPROVE_JOB_FAIL:
      return {
        ...state,
        errors: [action.error],
      }

    default:
      return state
  }
}
