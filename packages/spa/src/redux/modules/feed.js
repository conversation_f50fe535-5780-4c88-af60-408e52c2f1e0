const LOAD = 'feed/LOAD'
const LOAD_SUCCESS = 'feed/LOAD_SUCCESS'
const LOAD_FAIL = 'feed/LOAD_FAIL'

const initialState = {
  loaded: false,
  loading: false,
  data: { feed: { items: [] } },
  errors: null,
}

export function isLoaded(globalState) {
  return globalState.feed && globalState.feed.loaded
}

export function load() {
  return dispatch => dispatch({
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client => client.get('/api/graphql', {
      params: {
        query: `{
          feed(limit: 3) {
            items {
              title
              summary
              description
              link
              guid
              pubDate
            }
          }
        }`,
      },
    }),
  })
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        loading: true,
        loaded: false,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        ...action.result,
      }
    case LOAD_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: [action.error],
      }

    default:
      return state
  }
}
