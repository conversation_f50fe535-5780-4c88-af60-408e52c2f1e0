import { combineReducers } from 'redux'
import { routeReducer } from 'react-router-redux'
import session from './session'
import profile from './profile'
import profiles from './profiles'
import project from './project'
import projects from './projects'
import data from './data'
import account from './account'
import job from './job'
import jobs from './jobs'
import feed from './feed'
import casting from './casting'
import castings from './castings'
import booking from './booking'
import bookings from './bookings'
import admin from './admin'

// ui
import snackbar from './snackbar'
import confirmPrompt from './confirmPrompt'

const stores = {
  routing: routeReducer,
  session,
  profile,
  profiles,
  project,
  projects,
  data,
  account,
  jobs,
  job,
  feed,
  casting,
  castings,
  booking,
  bookings,
  admin,

  // ui
  snackbar,
  confirmPrompt,
}

export default combineReducers(stores)
