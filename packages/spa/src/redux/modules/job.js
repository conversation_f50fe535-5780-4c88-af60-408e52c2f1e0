import sanitize from 'helpers/sanitize'

const LOAD = 'job/LOAD'
const LOAD_SUCCESS = 'job/LOAD_SUCCESS'
const LOAD_FAIL = 'job/LOAD_FAIL'
const CONFIRM_JOB = 'job/CONFIRM_JOB'
const CONFIRM_JOB_SUCCESS = 'job/CONFIRM_JOB_SUCCESS'
const CONFIRM_JOB_FAIL = 'job/CONFIRM_JOB_FAIL'
const DECLINE_JOB = 'job/DECLINE_JOB'
const DECLINE_JOB_SUCCESS = 'job/DECLINE_JOB_SUCCESS'
const DECLINE_JOB_FAIL = 'job/DECLINE_JOB_FAIL'
const APPLY_FOR_JOB = 'job/APPLY_FOR_JOB'
const APPLY_FOR_JOB_SUCCESS = 'job/APPLY_FOR_JOB_SUCCESS'
const APPLY_FOR_JOB_FAIL = 'job/APPLY_FOR_JOB_FAIL'
const CONFIRM_CASTING = 'job/CONFIRM_CASTING'
const CONFIRM_CASTING_SUCCESS = 'job/CONFIRM_CASTING_SUCCESS'
const CONFIRM_CASTING_FAIL = 'job/CONFIRM_CASTING_FAIL'
const DECLINE_CASTING = 'job/DECLINE_CASTING'
const DECLINE_CASTING_SUCCESS = 'job/DECLINE_CASTING_SUCCESS'
const DECLINE_CASTING_FAIL = 'job/DECLINE_CASTING_FAIL'

const initialState = {
  loading: true,
  loaded: false,
  data: {},
  errors: null,
}

const bookingQueryFields = `
  id
  name
  location
  objectives
  rateType
  rate
  currency
  buyout
  notes
  travel
  dates {
    start
    end
  }
  membership {
    id status profile
  }
`

const castingQueryFields = `
  id
  name
  location
  notes
  dates {
    start
    end
  }

  membership {
    id status profile
  }
`
const queryFields = `
  ${bookingQueryFields}

  avatar
  projectName
  projectNotes
  type
  usage
  otherUsage

  representative {
    id
    name
    companyName
    slug
  }

  castings {
    ${castingQueryFields}
  }
`

export function isLoaded(globalState) {
  return globalState.job && globalState.job.loaded
}

export function load(jobId) {
  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client =>
      client.get('/api/graphql', {
        params: {
          query: `{
          job(id: ${sanitize.byType(jobId)}) {
            ${queryFields}
          }
        }`,
        },
      }),
  }
}

export function confirm(jobId, membershipId) {
  return (dispatch, getState) =>
    dispatch({
      types: [CONFIRM_JOB, CONFIRM_JOB_SUCCESS, CONFIRM_JOB_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            params: {
              query: `mutation ConfirmJob($jobId: ID!, $membershipId: ID!) {
          confirmJob(id: $jobId membershipId: $membershipId) {
            ${bookingQueryFields}
          }
        }`,
              variables: JSON.stringify({ jobId, membershipId }),
            },
          })
          .then((result) => {
            const resultData = result.data.confirmJob
            const data = { ...getState().job.data }

            return { ...result, data: { ...data, ...resultData } }
          }),
    })
}

export function decline(jobId, membershipId) {
  return (dispatch, getState) =>
    dispatch({
      types: [DECLINE_JOB, DECLINE_JOB_SUCCESS, DECLINE_JOB_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            params: {
              query: `mutation DeclineJob($jobId: ID!, $membershipId: ID!) {
          declineJob(id: $jobId membershipId: $membershipId) {
            ${bookingQueryFields}
          }
        }`,
              variables: JSON.stringify({ jobId, membershipId }),
            },
          })
          .then((result) => {
            const resultData = result.data.declineJob
            const data = { ...getState().job.data }

            return { ...result, data: { ...data, ...resultData } }
          }),
    })
}

export function applyFor(jobId, profileId) {
  return (dispatch, getState) =>
    dispatch({
      types: [APPLY_FOR_JOB, APPLY_FOR_JOB_SUCCESS, APPLY_FOR_JOB_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            params: {
              query: `mutation DeclineJob($jobId: ID! $profileId: ID!) {
          applyForJob(id: $jobId profileId: $profileId) {
            ${bookingQueryFields}
          }
        }`,
              variables: JSON.stringify({ jobId, profileId }),
            },
          })
          .then((result) => {
            const resultData = result.data.applyForJob
            const data = { ...getState().job.data }

            return { ...result, data: { ...data, ...resultData } }
          }),
    })
}

export function confirmCasting(castingId, membershipId) {
  return (dispatch, getState) =>
    dispatch({
      types: [CONFIRM_CASTING, CONFIRM_CASTING_SUCCESS, CONFIRM_CASTING_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            params: {
              query: `mutation ConfirmCasting($castingId: ID!, $membershipId: ID!) {
          confirmCasting(id: $castingId membershipId: $membershipId) {
            ${castingQueryFields}
          }
        }`,
              variables: JSON.stringify({ castingId, membershipId }),
            },
          })
          .then((result) => {
            const resultData = result.data.confirmCasting
            const data = { ...getState().job.data }
            const index = data.castings.findIndex(({ id }) => id === resultData.id)
            data.castings[index] = { ...data.castings[index], ...resultData }

            return { ...result, data }
          }),
    })
}

export function declineCasting(castingId, membershipId) {
  return (dispatch, getState) =>
    dispatch({
      types: [DECLINE_CASTING, DECLINE_CASTING_SUCCESS, DECLINE_CASTING_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            params: {
              query: `mutation DeclineCasting($castingId: ID!, $membershipId: ID!) {
          declineCasting(id: $castingId membershipId: $membershipId) {
            ${castingQueryFields}
          }
        }`,
              variables: JSON.stringify({ castingId, membershipId }),
            },
          })
          .then((result) => {
            const resultData = result.data.declineCasting
            const data = { ...getState().job.data }
            const index = data.castings.findIndex(({ id }) => id === resultData.id)
            data.castings[index] = { ...data.castings[index], ...resultData }

            return { ...result, data }
          }),
    })
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        loading: true,
        loaded: false,
        errors: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: action.result.data.job,
        errors: action.result.errors || null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        loaded: false,
        loading: true,
        data: [],
        errors: [action.error],
      }

    case CONFIRM_JOB:
      return {
        ...state,
        errors: null,
      }
    case CONFIRM_JOB_SUCCESS:
      return {
        ...state,
        data: action.result.data,
        error: action.result.errors || null,
      }
    case CONFIRM_JOB_FAIL:
      return {
        ...state,
        errors: [action.error],
      }

    case DECLINE_JOB:
      return {
        ...state,
        errors: null,
      }
    case DECLINE_JOB_SUCCESS:
      return {
        ...state,
        data: action.result.data,
        errors: action.result.errors || null,
      }
    case DECLINE_JOB_FAIL:
      return {
        ...state,
        errors: [action.error],
      }

    case APPLY_FOR_JOB:
      return {
        ...state,
        errors: null,
      }
    case APPLY_FOR_JOB_SUCCESS:
      return {
        ...state,
        data: action.result.data,
        errors: action.result.errors || null,
      }
    case APPLY_FOR_JOB_FAIL:
      return {
        ...state,
        errors: [action.error],
      }

    case CONFIRM_CASTING:
      return {
        ...state,
        errors: null,
      }
    case CONFIRM_CASTING_SUCCESS:
      return {
        ...state,
        data: action.result.data,
        error: action.result.errors || null,
      }
    case CONFIRM_CASTING_FAIL:
      return {
        ...state,
        errors: [action.error],
      }

    case DECLINE_CASTING:
      return {
        ...state,
        errors: null,
      }
    case DECLINE_CASTING_SUCCESS:
      return {
        ...state,
        data: action.result.data,
        errors: action.result.errors || null,
      }
    case DECLINE_CASTING_FAIL:
      return {
        ...state,
        errors: [action.error],
      }

    default:
      return state
  }
}
