import sanitize from 'helpers/sanitize'

const LOAD = 'profile/LOAD'
const LOAD_SUCCESS = 'profile/LOAD_SUCCESS'
const LOAD_FAIL = 'profile/LOAD_FAIL'
const CREATE = 'profile/CREATE'
const CREATE_SUCCESS = 'profile/CREATE_SUCCESS'
const CREATE_FAIL = 'profile/CREATE_FAIL'
const SAVE = 'profile/SAVE'
const SAVE_SUCCESS = 'profile/SAVE_SUCCESS'
const SAVE_FAIL = 'profile/SAVE_FAIL'
const ATTACH_MEDIA = 'profile/ATTACH_MEDIA'
const ATTACH_MEDIA_SUCCESS = 'profile/ATTACH_MEDIA_SUCCESS'
const ATTACH_MEDIA_FAIL = 'profile/ATTACH_MEDIA_FAIL'
const DETACH_MEDIA = 'profile/DETACH_MEDIA'
const DETACH_MEDIA_SUCCESS = 'profile/DETACH_MEDIA_SUCCESS'
const DETACH_MEDIA_FAIL = 'profile/DETACH_MEDIA_FAIL'
const RESET = 'profile/RESET'
const GENERATE_PDF = 'profile/GENERATE_PDF'
const GENERATE_PDF_SUCCESS = 'profile/GENERATE_PDF_SUCCESS'
const GENERATE_PDF_FAIL = 'profile/GENERATE_PDF_FAIL'
const UPLOAD_PROGRESS = 'profile/UPLOAD_PROGRESS'
const UPLOAD_PROGRESS_DONE = 'profile/UPLOAD_PROGRESS_DONE'

const initialState = {
  loading: true,
  loaded: false,
  action: 'load',
  pdfGenerating: false,
  pdfUrl: '',
  uploads: [],
}

export function isLoaded(globalState) {
  return globalState.profile && globalState.profile.loaded
}

export function load(id, slug = false, fullProfile = false) {
  let args = `id: "${id}"`

  if (slug) {
    args = `slug: "${slug}"`
  }

  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client =>
      client
        .get('/api/graphql', {
          params: {
            query: `{
            profile(${args}) {
              id
              created
              updated
              account
              name
              slug
              approved
              type
              avatar
              cover
              country
              city
              gender
              birthYear
              height
              waist
              inseam
              suit
              hips
              bust
              dressSize
              chest
              cup
              eyeColor
              shoeSize
              hairColor
              hairLength
              bodyType
              piercings
              tattoos
              nationality
              background
              travelOk
              ethnicity
              compCardA
              compCardB
              compCardC
              compCardD
              compCardCover
              compCardLabelPosition
              media

              companyName
              fullAddress
              jobPosition
              jobPositionSinceDate
              bio
            }

            ${fullProfile ? `
              jobs(profiles: [${sanitize.byType(id)}] hasMembership: true) {
                id
                name
                projectName
                avatar
                dates { start end }
                membership { id status profile }
                castings {
                  id name location
                  dates { start end }
                  membership { id status profile }
                }
              }` : ''}
          }`,
          },
        })
        .then(result => ({ ...result.data.profile, jobs: result.data.jobs })),
  }
}

export function create(name, callback) {
  const payload = `
    mutation {
      createProfile(
        name: "${sanitize.text(name)}"
      ) {
        id
      }
    }
  `

  return {
    types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          data: {
            query: payload,
          },
        })
        .then((result) => {
          if (callback) {
            callback(result.errors || false, result.data.createProfile)
          }

          return result
        }),
  }
}

export function save(profile, callback) {
  const queryName = profile.id ? 'updateProfile' : 'createProfile'
  const fields = Object.keys(profile)
  const args = fields.map(key => `${key}: ${sanitize.byType(profile[key])}`).join('\n')

  const payload = `mutation {
    ${queryName}(
      ${args}
    ) {
      ${fields.join('\n')}
    }
  }`

  return {
    types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          data: {
            query: payload,
          },
        })
        .then((result) => {
          if (callback) {
            callback(result.errors || false, result.data)
          }

          return result.data[queryName]
        }),
  }
}

export function uploadProgress(uploadId, fieldName, { lengthComputable, loaded, total }) {
  let progress = 50

  if (lengthComputable) {
    progress = loaded / total * 100
  }

  const progressItem = { uploadId, progress, fieldName }

  return (dispatch, getState) => {
    let uploads = getState().profile.uploads || []

    const uploadItem = uploads.find(item => item.uploadId === uploadId)

    if (uploadItem) {
      uploadItem.progress = progress
    } else {
      uploads = [progressItem, ...uploads]
    }

    dispatch({
      type: UPLOAD_PROGRESS,
      uploads,
    })
  }
}

export function uploadProgressDone(uploadId) {
  return (dispatch, getState) => {
    const uploads = getState().profile.uploads || []

    dispatch({
      type: UPLOAD_PROGRESS_DONE,
      uploads: uploads.filter(item => item.uploadId !== uploadId),
    })
  }
}

let uploadsId = 0
export function attachMedia(profile) {
  const data = new FormData()

  let fieldName = ''
  const fields = Object.keys(profile)

  if (fields.length > 2) {
    return {
      type: ATTACH_MEDIA_FAIL,
      errors: ['Can only attach one file.'],
    }
  }

  fields.forEach((key) => {
    if (key !== 'id') {
      data.append(key, profile[key][0])
      fieldName = key
    }
  })

  const payload = `mutation {
    attachMedia(
      id: "${profile.id}"
      field: ${fieldName}
    ) {
      ${fieldName}
    }
  }`

  data.append('query', payload)

  uploadsId += 1
  const uploadId = Number(uploadsId)

  return dispatch =>
    dispatch({
      types: [ATTACH_MEDIA, ATTACH_MEDIA_SUCCESS, ATTACH_MEDIA_FAIL],
      promise: client =>
        client
          .post(
            '/api/graphql',
          {
            data,
          },
            event => dispatch(uploadProgress(uploadId, fieldName, event)),
          )
          .then((result) => {
            dispatch(uploadProgressDone(uploadId))
            return result
          }),
    })
}

export function detachMedia(profileId, fieldName, mediaId) {
  const data = new FormData()

  const payload = `mutation {
    detachMedia(
      id: "${profileId}"
      mediaId: "${mediaId}"
      field: ${fieldName}
    ) {
      ${fieldName}
    }
  }`

  data.append('query', payload)

  return {
    types: [DETACH_MEDIA, DETACH_MEDIA_SUCCESS, DETACH_MEDIA_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          data,
        })
        .then(result => result),
  }
}

export function reset() {
  return {
    type: RESET,
  }
}

export function generatePdf(slug) {
  return dispatch =>
    dispatch({
      types: [GENERATE_PDF, GENERATE_PDF_SUCCESS, GENERATE_PDF_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: `mutation {
              generateProfilePdf(slug: "${slug}") {
                url
              }
            }`,
            },
          })
          .then(result => result.data.generateProfilePdf),
    })
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        action: 'load',
        loading: true,
        loaded: false,
        data: null,
        error: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        action: 'load',
        loading: false,
        loaded: true,
        data: action.result,
        error: null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        action: 'load',
        loaded: false,
        loading: true,
        data: null,
        error: action.error,
      }

    case CREATE:
      return {
        ...state,
        action: 'create',
        loading: true,
        loaded: false,
        data: state.data,
      }
    case CREATE_SUCCESS:
      return {
        ...state,
        action: 'create',
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.createProfile },
        error: action.result.errors || null,
      }
    case CREATE_FAIL:
      return {
        ...state,
        action: 'create',
        loading: false,
        loaded: false,
        error: action.error,
      }

    case SAVE:
      return {
        ...state,
        action: 'save',
        loading: true,
        loaded: false,
        data: state.data,
      }
    case SAVE_SUCCESS:
      return {
        ...state,
        action: 'save',
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result },
        error: action.result.errors || null,
      }
    case SAVE_FAIL:
      return {
        ...state,
        action: 'save',
        loading: false,
        loaded: false,
        data: state.data,
        error: action.error,
      }

    case UPLOAD_PROGRESS:
      return {
        ...state,
        uploads: [...action.uploads],
      }

    case UPLOAD_PROGRESS_DONE:
      return {
        ...state,
        uploads: [...action.uploads],
      }

    case ATTACH_MEDIA:
      return {
        ...state,
        action: 'attach-media',
        loading: true,
        loaded: false,
        data: state.data,
      }
    case ATTACH_MEDIA_SUCCESS:
      return {
        ...state,
        action: 'attach-media',
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.attachMedia },
        error: action.result.errors || null,
      }
    case ATTACH_MEDIA_FAIL:
      return {
        ...state,
        action: 'attach-media',
        loading: false,
        loaded: false,
        data: state.data,
        error: action.error,
      }

    case DETACH_MEDIA:
      return {
        ...state,
        action: 'detach-media',
        loading: true,
        loaded: false,
        data: state.data,
      }
    case DETACH_MEDIA_SUCCESS:
      return {
        ...state,
        action: 'detach-media',
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.detachMedia },
        error: action.result.errors || null,
      }
    case DETACH_MEDIA_FAIL:
      return {
        ...state,
        action: 'detach-media',
        loading: false,
        loaded: false,
        data: state.data,
        error: action.error,
      }

    case RESET:
      return {
        ...initialState,
      }

    case GENERATE_PDF:
      return {
        ...state,
        pdfGenerating: true,
      }
    case GENERATE_PDF_SUCCESS:
      return {
        ...state,
        pdfGenerating: false,
        pdfUrl: action.result.url,
      }
    case GENERATE_PDF_FAIL:
      return {
        ...initialState,
        ...state,
        error: action.error,
      }

    default:
      return state
  }
}
