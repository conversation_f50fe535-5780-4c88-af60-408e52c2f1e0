const LOAD = 'data/LOAD'
const LOAD_SUCCESS = 'data/LOAD_SUCCESS'
const LOAD_FAIL = 'data/LOAD_FAIL'
const UPDATE = 'data/UPDATE'
const UPDATE_SUCCESS = 'data/UPDATE_SUCCESS'
const UPDATE_FAIL = 'data/UPDATE_FAIL'

const initialState = {
  loaded: false,
  loading: false,
  data: {},
}

export function isLoaded(globalState) {
  return globalState.data && globalState.data.loaded
}

export function load(field, ids = []) {
  return (dispatch, getState) => {
    if (ids.length) {
      dispatch({
        types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
        promise: (client) => client.get('/api/graphql', {
          params: {
            query: `{
              ${field}(ids: ["${ids.join('","')}"]) {
                id
                created
                updated
                name
                slug
                type
                avatar
                cover
                country
                city
                gender
              }
            }`,
          },
        }).then(result => {
          const stateData = getState().data[field] || {}
          const data = {}

          result.data[field].forEach(item => {
            data[item.id] = item
          })

          return { [field]: { ...stateData, ...data } }
        }),
      })
    }
  }
}

export function refresh(ids, getState) {
  const workspace = getState().workspace
  const documents = getState().documents.data
  let list = ids

  if (!ids) {
    list = workspace.list
  }

  if (list.length) {
    return {
      types: [UPDATE, UPDATE_SUCCESS, UPDATE_FAIL],
      promise: (client) => client.get('/api/graphql', {
        params: {
          query: `{
            getDocuments(ids: ["${list.join('","')}"]) {
              id name slug node master template
              branches {
                id name head
              }
            }
          }`,
        },
      }).then(result => {
        const data = { ...documents }

        result.data.getDocuments.forEach(document => {
          data[document.id] = document
        })

        return data
      }),
    }
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {

    case LOAD:
      return {
        ...state,
        loading: true,
        loaded: false,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        ...action.result,
        error: null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        error: action.error,
      }

    case UPDATE:
      return {
        ...state,
        updating: true,
        updated: false,
      }
    case UPDATE_SUCCESS:
      return {
        ...state,
        updating: false,
        updated: true,
        data: action.result,
        error: null,
      }
    case UPDATE_FAIL:
      return {
        ...state,
        updating: false,
        updated: false,
        error: action.error,
      }

    default:
      return state
  }
}
