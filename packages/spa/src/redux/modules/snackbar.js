const SHOW = 'snackbar/SHOW'
const HIDE = 'snackbar/HIDE'

const initialState = {
  open: false,
  message: '',
}

export function show(message) {
  return {
    type: SHOW,
    message,
  }
}

export function hide() {

  return {
    type: HIDE,
    message: '',
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case SHOW:
      return {
        ...state,
        message: action.message,
        open: true,
      }
    case HIDE:
      return {
        ...state,
        message: action.message,
        open: false,
      }

    default:
      return state
  }
}
