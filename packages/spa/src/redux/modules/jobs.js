import sanitize from 'helpers/sanitize'

const LOAD = 'jobs/LOAD'
const LOAD_SUCCESS = 'jobs/LOAD_SUCCESS'
const LOAD_FAIL = 'jobs/LOAD_FAIL'

const initialState = {
  loading: true,
  loaded: false,
  data: {},
}

const bookingQueryFields = `
  id
  name
  location
  objectives
  rateType
  rate
  currency
  buyout
  notes
  travel
  dates {
    start
    end
  }
  membership {
    id status profile
  }
`

const castingQueryFields = `
  id
  name
  location
  notes
  dates {
    start
    end
  }

  membership {
    id status profile
  }
`
const queryFields = `
  ${bookingQueryFields}

  avatar
  projectName
  type
  usage
  otherUsage

  castings {
    ${castingQueryFields}
  }



`

export function isLoaded(globalState) {
  return globalState.jobs && globalState.jobs.loaded
}

export function load(profiles, query = {}) {
  // const fields = Object.keys(query)
  // const args = fields.map(key => `${key}: ${sanitize.byType(query[key])}`).join('\n')

  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client =>
      client
        .get('/api/graphql', {
          params: {
            query: `{
            jobs(profiles: [${sanitize.byType(profiles)}] hasMembership: true) {
              ${queryFields}
            }
            search: jobs(profiles: [${sanitize.byType(profiles)}]) {
              ${queryFields}
            }
        }`,
          },
        })
        .then(result => result),
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        loading: true,
        loaded: false,
        errors: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        ...action.result,
      }
    case LOAD_FAIL:
      return {
        ...state,
        loaded: false,
        loading: true,
        data: initialState.data,
        errors: [action.error],
      }

    default:
      return state
  }
}
