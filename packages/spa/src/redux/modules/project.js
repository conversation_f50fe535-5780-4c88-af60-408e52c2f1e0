import sanitize from 'helpers/sanitize'
import noop from 'helpers/noop'
import { show as showSnackbar } from './snackbar'

const LOAD = 'project/LOAD'
const LOAD_SUCCESS = 'project/LOAD_SUCCESS'
const LOAD_FAIL = 'project/LOAD_FAIL'
const REFRESH = 'project/REFRESH'
const REFRESH_SUCCESS = 'project/REFRESH_SUCCESS'
const REFRESH_FAIL = 'project/REFRESH_FAIL'
const SAVE = 'project/SAVE'
const SAVE_SUCCESS = 'project/SAVE_SUCCESS'
const SAVE_FAIL = 'project/SAVE_FAIL'
const CREATE = 'project/CREATE'
const CREATE_SUCCESS = 'project/CREATE_SUCCESS'
const CREATE_FAIL = 'project/CREATE_FAIL'
const CANCEL = 'casting/CANCEL'
const CANCEL_SUCCESS = 'casting/CANCEL_SUCCESS'
const CANCEL_FAIL = 'casting/CANCEL_FAIL'
const ATTACH_MEDIA = 'project/ATTACH_MEDIA'
const ATTACH_MEDIA_SUCCESS = 'project/ATTACH_MEDIA_SUCCESS'
const ATTACH_MEDIA_FAIL = 'project/ATTACH_MEDIA_FAIL'
const UPDATE_STATE = 'project/UPDATE_STATE'

const initialState = {
  loading: true,
  loaded: false,
  action: 'load',
  errors: [],
  data: { bookings: [], castings: [] },
}

const queryFields = `
  id
  name
  approved
  avatar

  type
  usage
  otherUsage
  notes

  castings {
    id
    name
    booking { id approved }
    location
    notes
    dates {
      start
      end
    }
    members {
      id added updated status
      profile { id name slug avatar height hairColor eyeColor }
    }
  }

  bookings {
    id
    approved
    name
    location
    visibility
    seats
    objectives
    rateType
    rate
    currency
    buyout
    notes
    travel
    dates {
      start
      end
    }
    members {
      id added updated status
      profile { id name slug avatar height hairColor eyeColor }
    }
  }
`

export function isLoaded(globalState) {
  return globalState.project && globalState.project.loaded
}

export function load(id) {
  const args = `id: "${id}"`

  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client =>
      client.get('/api/graphql', {
        params: {
          query: `{
            project(${args}) {
              ${queryFields}
            }
          }`,
        },
      }),
  }
}

export function refresh() {
  return (dispatch, getState) => {
    const { id } = getState().project.data
    const args = `id: "${id}"`

    return dispatch({
      types: [REFRESH, REFRESH_SUCCESS, REFRESH_FAIL],
      promise: client =>
        client.get('/api/graphql', {
          params: {
            query: `{
            project(${args}) {
              ${queryFields}
            }
          }`,
          },
        }),
    })
  }
}

export function save(data, callback = noop) {
  const fields = Object.keys(data)
  const args = fields.map(key => `${key}: ${sanitize.byType(data[key])}`).join('\n')

  const payload = `
    mutation {
      updateProject(
        ${args}
      ) {
        ${fields.join('\n')}
      }
    }
  `

  return {
    types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          data: {
            query: payload,
          },
        })
        .then(result => ({ ...result, data: result.data.updateProject })),
  }
}

export function create(name, profileId, callback) {
  const payload = `
    mutation {
      createProject(
        name: "${sanitize.text(name)}"
        profile: "${sanitize.text(profileId)}"
      ) {
        id
      }
    }
  `

  return {
    types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          data: {
            query: payload,
          },
        })
        .then((result) => {
          if (callback) {
            callback(result.errors || false, result.data.createProject)
          }

          return result
        }),
  }
}

export function cancel(projectId) {
  const payload = `
    mutation {
      cancelProject(
        projectId: "${projectId}"
      )
    }
  `

  return dispatch =>
    dispatch({
      types: [CANCEL, CANCEL_SUCCESS, CANCEL_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(showSnackbar('Project has been deleted'))
            }

            return result
          }),
    })
}

export function attachMedia(project) {
  const data = new FormData()

  let fieldName = ''
  const fields = Object.keys(project)

  if (fields.length > 2) {
    return {
      type: ATTACH_MEDIA_FAIL,
      errors: ['Can only attach one file.'],
    }
  }

  fields.forEach((key) => {
    if (key !== 'id') {
      data.append(key, project[key][0])
      fieldName = key
    }
  })

  const payload = `mutation {
    attachProjectMedia(
      id: "${project.id}"
      field: ${fieldName}
    ) {
      ${fieldName}
    }
  }`

  data.append('query', payload)

  return {
    types: [ATTACH_MEDIA, ATTACH_MEDIA_SUCCESS, ATTACH_MEDIA_FAIL],
    promise: client =>
      client
        .post('/api/graphql', {
          data,
        })
        .then(result => result),
  }
}

export function updateStateData(field, id, update) {
  return (dispatch, getState) => {
    const { data } = getState().project

    const index = data[field].findIndex(item => item.id === id)

    if (index >= 0) {
      if (typeof update === 'undefined') delete data[field][index]
      else data[field][index] = { ...data[field][index], ...update }
    } else {
      data[field].push(update)
    }

    dispatch({
      type: UPDATE_STATE,
      result: { ...data, [field]: [...data[field]] },
    })
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        ...initialState,
        loading: true,
        loaded: false,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: action.result.data.project,
        errors: null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        loaded: false,
        loading: true,
        data: null,
        errors: action.error,
      }

    case REFRESH:
      return state
    case REFRESH_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: action.result.data.project,
        errors: null,
      }
    case REFRESH_FAIL:
      return {
        ...state,
        errors: [action.error],
      }

    case SAVE:
      return state
    case SAVE_SUCCESS:
      return {
        ...state,
        data: { ...state.data, ...action.result.data },
        errors: action.result.errors || null,
      }
    case SAVE_FAIL:
      return {
        ...state,
        data: state.data,
        errors: action.error,
      }

    case CREATE:
      return state
    case CREATE_SUCCESS:
      return {
        ...state,
        data: action.result.data.createProject,
        errors: action.result.errors || null,
      }
    case CREATE_FAIL:
      return {
        ...state,
        errors: action.error,
      }

    case CANCEL:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case CANCEL_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: null,
        errors: action.result.errors || null,
      }
    case CANCEL_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case ATTACH_MEDIA:
      return state
    case ATTACH_MEDIA_SUCCESS:
      return {
        ...state,
        data: { ...state.data, ...action.result.data.attachProjectMedia },
        error: action.result.errors || null,
      }
    case ATTACH_MEDIA_FAIL:
      return {
        ...state,
        data: state.data,
        error: [action.error],
      }

    case UPDATE_STATE:
      return {
        ...state,
        data: { ...action.result },
      }

    default:
      return state
  }
}
