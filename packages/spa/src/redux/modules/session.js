import sha512 from 'crypto-js/sha512'
import persistentStorage from 'helpers/persistentStorage'
import sanitize from 'helpers/sanitize'

const LOAD = 'session/LOAD'
const LOAD_SUCCESS = 'session/LOAD_SUCCESS'
const LOAD_FAIL = 'session/LOAD_FAIL'
const CREATE = 'session/CREATE'
const CREATE_SUCCESS = 'session/CREATE_SUCCESS'
const CREATE_FAIL = 'session/CREATE_FAIL'
const SET = 'session/SET'
const SET_SUCCESS = 'session/SET_SUCCESS'
const SET_FAIL = 'session/SET_FAIL'
const DESTROY = 'session/DESTROY'
const EXPIRE = 'session/EXPIRE'

const storage = persistentStorage('session')

const initialState = {
  loading: true,
  loaded: false,
  action: 'load',
  token: null,
  tokenExpired: false,
  account: null,
  units: 'metric',
  profileCriteria: { },
}

export function isLoaded(globalState) {
  return globalState.session && globalState.session.loaded
}

export function load() {
  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: () => storage.load(),
  }
}

export function set(property, value = 'toggle') {
  return (dispatch, getState) => {
    const state = getState().session

    dispatch({
      types: [SET, SET_SUCCESS, SET_FAIL],
      promise: () => new Promise((resolve, reject) => {
        const data = { ...state }

        if (value === 'toggle') {
          data[property] = !data[property] || false
        } else {
          data[property] = value
        }

        return storage.save(data).then(() => resolve(data)).catch(error => reject(error))
      }),
    })
  }
}

export function create(email, passPhrase, callback) {
  const payload = `
    mutation {
      createToken(
        email: "${sanitize.text(email)}"
        passPhrase: "${sanitize.text(sha512(passPhrase).toString())}"
      ) {
        token, account
      }
    }
  `

  return (dispatch, getState) => {
    const state = getState().session

    dispatch({
      types: [CREATE, CREATE_SUCCESS, CREATE_FAIL],
      promise: (client) => client.post('/api/graphql', {
        data: {
          query: payload,
        },
      }).then(result => {
        if (callback) {
          callback(result.errors || false, result.data.createToken)
        }

        if (result.data.createToken) {
          const data = { ...state }
          const { data: { createToken: { account, token } } } = result

          data.account = account
          data.token = token
          data.tokenExpired = false

          storage.save(data)
        }

        return result
      }),
    })
  }
}

export function destroy() {
  storage.delete()

  return {
    type: DESTROY,
  }
}

export function expire() {
  return {
    type: EXPIRE,
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        action: 'load',
        loading: true,
        loaded: false,
        data: null,
        error: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        ...action.result,
        loading: false,
        loaded: true,
        error: null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        action: 'load',
        loaded: false,
        loading: true,
        data: null,
        error: action.error,
      }

    case SET:
      return {
        ...state,
        loading: true,
        loaded: false,
      }
    case SET_SUCCESS:
      return {
        ...state,
        ...action.result,
        loading: false,
        loaded: true,
        error: action.errors || null,
      }
    case SET_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        error: action.errors,
      }

    case CREATE:
      return {
        loading: true,
        loaded: false,
        data: {},
      }
    case CREATE_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        token: action.result.data.createToken && action.result.data.createToken.token,
        account: action.result.data.createToken && action.result.data.createToken.account,
        tokenExpired: false,
        error: action.result.errors || null,
      }
    case CREATE_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        data: {},
        error: action.error,
      }

    case DESTROY:
      return {
        ...initialState,
      }

    case EXPIRE:
      return {
        ...state,
        tokenExpired: true,
      }

    default:
      return state
  }
}
