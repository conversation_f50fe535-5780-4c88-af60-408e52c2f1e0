import sanitize from 'helpers/sanitize'
import { makeArgumentsArray } from 'helpers/graphQL'
import noop from 'helpers/noop'
import { show as showSnackbar } from './snackbar'
import { updateStateData as updateProjectStateData, refresh as refreshProject } from './project'

const SAVE = 'casting/SAVE'
const SAVE_SUCCESS = 'casting/SAVE_SUCCESS'
const SAVE_FAIL = 'casting/SAVE_FAIL'
const CANCEL = 'casting/CANCEL'
const CANCEL_SUCCESS = 'casting/CANCEL_SUCCESS'
const CANCEL_FAIL = 'casting/CANCEL_FAIL'
const ADD_TO_LIST = 'casting/ADD_TO_LIST'
const ADD_TO_LIST_SUCCESS = 'casting/ADD_TO_LIST_SUCCESS'
const ADD_TO_LIST_FAIL = 'casting/ADD_TO_LIST_FAIL'
const REMOVE_FROM_LIST = 'casting/REMOVE_FROM_LIST'
const REMOVE_FROM_LIST_SUCCESS = 'casting/REMOVE_FROM_LIST_SUCCESS'
const REMOVE_FROM_LIST_FAIL = 'casting/REMOVE_FROM_LIST_FAIL'
const UPDATE_MEMBERSHIP = 'casting/UPDATE_MEMBERSHIP'
const UPDATE_MEMBERSHIP_SUCCESS = 'casting/UPDATE_MEMBERSHIP_SUCCESS'
const UPDATE_MEMBERSHIP_FAIL = 'casting/UPDATE_MEMBERSHIP_FAIL'
const UNLOAD = 'casting/UNLOAD'

const initialState = {
  loading: true,
  loaded: false,
  action: 'load',
  errors: [],
  data: {},
}

const enumFields = []
const subFields = ['project', 'booking']

export function save({ date: dates, ...data }) {
  let dateArgs
  const queryName = data.id ? 'updateCasting' : 'createCasting'
  const fields = Object.keys(data)
  const selection = fields.map(key => subFields.includes(key) ? `${key} { id }` : key).join('\n')
  const args = fields
    .map(
      key =>
        data[key]
          ? `${key}: ${enumFields.includes(key) ? data[key] : sanitize.byType(data[key])}`
          : '',
    )
    .join('\n')

  if (dates) {
    dateArgs = [dates].map(date => makeArgumentsArray(date).join(', ')).join('}, {')
  }

  const payload = `mutation {
    ${queryName}(
      ${args}
      ${dates ? `dates: [{${dateArgs}}]` : ''}
    ) {
      ${selection}
      ${dates ? 'dates { start end }' : ''}
    }
  }`

  return dispatch =>
    dispatch({
      types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            const resultData = result.data[queryName]

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(showSnackbar('Casting saved'))
              dispatch(updateProjectStateData('castings', resultData.id, resultData))
            }

            return { ...result, data: resultData }
          }),
    })
}

export function cancel(castingId) {
  const payload = `
    mutation {
      cancelCasting(
        castingId: "${castingId}"
      )
    }
  `

  return dispatch =>
    dispatch({
      types: [CANCEL, CANCEL_SUCCESS, CANCEL_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              // dispatch(updateProjectStateData('castings', castingId, undefined)),
              dispatch(refreshProject()) // stuuuuuupid
              dispatch(showSnackbar('Casting has been deleted'))
            }

            return result
          }),
    })
}

export function addMember(castingId, profileId, callback = noop) {
  const payload = `
    mutation {
      addMemberToCasting(
        castingId: "${castingId}"
        memberId: "${profileId}"
      ) {
        id
        members { id status }
      }
    }
  `

  return dispatch =>
    dispatch({
      types: [ADD_TO_LIST, ADD_TO_LIST_SUCCESS, ADD_TO_LIST_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            callback(result.errors || false, result.data.addMemberToCasting)

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(showSnackbar('Talent added to casting'))
            }

            return result
          }),
    })
}

export function removeMember(castingId, membershipId) {
  const payload = `
    mutation {
      removeCastingMembership(
        castingId: "${castingId}"
        membershipId: "${membershipId}"
      ) {
        id
        members { id status }
      }
    }
  `

  return dispatch =>
    dispatch({
      types: [REMOVE_FROM_LIST, REMOVE_FROM_LIST_SUCCESS, REMOVE_FROM_LIST_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            const resultData = result.data.removeCastingMembership

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(updateProjectStateData('castings', resultData.id, resultData))
              dispatch(refreshProject()) // stuuuuuupid
              dispatch(showSnackbar('Talent removed from casting'))
            }

            return result
          }),
    })
}

export function updateMembership(castingId, membershipId, status, callback = noop) {
  const payload = `
    mutation {
      updateCastingMembership(
        castingId: "${castingId}"
        membershipId: "${membershipId}"
        status: ${status}
      ) {
        id
        members {
          id added updated status
          profile { id name slug avatar }
        }
      }
    }
  `

  return dispatch =>
    dispatch({
      types: [UPDATE_MEMBERSHIP, UPDATE_MEMBERSHIP_SUCCESS, UPDATE_MEMBERSHIP_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            const data = result.data.updateCastingMembership

            callback(result.errors || false, data)

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(showSnackbar('Status updated'))
              dispatch(updateProjectStateData('castings', castingId, data))
            }

            return result
          }),
    })
}

export function unload() {
  return {
    type: UNLOAD,
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case SAVE:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case SAVE_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data },
        errors: action.result.errors || null,
      }
    case SAVE_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        data: state.data,
        errors: action.error,
      }

    case CANCEL:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case CANCEL_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: {},
        errors: action.result.errors || null,
      }
    case CANCEL_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case ADD_TO_LIST:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case ADD_TO_LIST_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.addMemberToCasting },
        errors: action.result.errors || null,
      }
    case ADD_TO_LIST_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case REMOVE_FROM_LIST:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case REMOVE_FROM_LIST_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.removeCastingMembership },
        errors: action.result.errors || null,
      }
    case REMOVE_FROM_LIST_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case UPDATE_MEMBERSHIP:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case UPDATE_MEMBERSHIP_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.updateCastingMembership },
        errors: action.result.errors || null,
      }
    case UPDATE_MEMBERSHIP_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case UNLOAD:
      return {
        ...initialState,
      }

    default:
      return state
  }
}
