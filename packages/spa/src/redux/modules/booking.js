import sanitize from 'helpers/sanitize'
import { makeArgumentsArray } from 'helpers/graphQL'
import noop from 'helpers/noop'
import { show as showSnackbar } from './snackbar'
import { updateStateData as updateProjectStateData, refresh as refreshProject } from './project'

const SAVE = 'booking/SAVE'
const SAVE_SUCCESS = 'booking/SAVE_SUCCESS'
const SAVE_FAIL = 'booking/SAVE_FAIL'
const CANCEL = 'casting/CANCEL'
const CANCEL_SUCCESS = 'casting/CANCEL_SUCCESS'
const CANCEL_FAIL = 'casting/CANCEL_FAIL'
const ADD_TO_LIST = 'booking/ADD_TO_LIST'
const ADD_TO_LIST_SUCCESS = 'booking/ADD_TO_LIST_SUCCESS'
const ADD_TO_LIST_FAIL = 'booking/ADD_TO_LIST_FAIL'
const REMOVE_FROM_LIST = 'booking/REMOVE_FROM_LIST'
const REMOVE_FROM_LIST_SUCCESS = 'booking/REMOVE_FROM_LIST_SUCCESS'
const REMOVE_FROM_LIST_FAIL = 'booking/REMOVE_FROM_LIST_FAIL'
const UPDATE_MEMBERSHIP = 'booking/UPDATE_MEMBERSHIP'
const UPDATE_MEMBERSHIP_SUCCESS = 'booking/UPDATE_MEMBERSHIP_SUCCESS'
const UPDATE_MEMBERSHIP_FAIL = 'booking/UPDATE_MEMBERSHIP_FAIL'
const UNLOAD = 'booking/UNLOAD'

const initialState = {
  loading: true,
  loaded: false,
  action: 'load',
  errors: [],
  data: {},
}

const enumFields = []
const subFields = ['project']

export function save({ dates, ...data }) {
  let dateArgs
  const queryName = data.id ? 'updateBooking' : 'createBooking'
  const fields = Object.keys(data)
  const selection = fields.map(key => subFields.includes(key) ? `${key} { id }` : key).join('\n')
  const args = fields
    .map(
      key =>
        data[key]
          ? `${key}: ${enumFields.includes(key) ? data[key] : sanitize.byType(data[key])}`
          : '',
    )
    .join('\n')

  if (dates) {
    dateArgs = dates.map(date => makeArgumentsArray(date).join(', ')).join('}, {')
  }

  const payload = `mutation {
    ${queryName}(
      ${args}
      ${dates ? `dates: [{${dateArgs}}]` : ''}
    ) {
      ${selection}
      ${dates ? 'dates { start end }' : ''}
    }
  }`

  return dispatch =>
    dispatch({
      types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            const resultData = result.data[queryName]

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(showSnackbar('Booking saved'))
              dispatch(updateProjectStateData('bookings', resultData.id, resultData))
            }

            return { ...result, data: resultData }
          }),
    })
}

export function cancel(bookingId) {
  const payload = `
    mutation {
      cancelBooking(
        bookingId: "${bookingId}"
      )
    }
  `

  return dispatch =>
    dispatch({
      types: [CANCEL, CANCEL_SUCCESS, CANCEL_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(refreshProject()) // laaaame
              dispatch(showSnackbar('Booking has been deleted'))
            }

            return result
          }),
    })
}

export function addMember(bookingId, profileId, callback = noop) {
  const payload = `
    mutation {
      addMemberToBooking(
        bookingId: "${bookingId}"
        memberId: "${profileId}"
      ) {
        id
        members { id status }
      }
    }
  `

  return dispatch =>
    dispatch({
      types: [ADD_TO_LIST, ADD_TO_LIST_SUCCESS, ADD_TO_LIST_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            callback(result.errors || false, result.data.addMemberToBooking)

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(showSnackbar('Talent added to booking'))
            }

            return result
          }),
    })
}

export function removeMember(bookingId, membershipId) {
  const payload = `
    mutation {
      removeBookingMembership(
        bookingId: "${bookingId}"
        membershipId: "${membershipId}"
      ) {
        id
        members { id status }
      }
    }
  `

  return dispatch =>
    dispatch({
      types: [REMOVE_FROM_LIST, REMOVE_FROM_LIST_SUCCESS, REMOVE_FROM_LIST_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            const resultData = result.data.removeBookingMembership

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(updateProjectStateData('castings', resultData.id, resultData))
              dispatch(refreshProject()) // stuuuuuupid
              dispatch(showSnackbar('Talent removed from booking'))
            }

            return result
          }),
    })
}

export function updateMembership(bookingId, membershipId, status, callback = noop) {
  const payload = `
    mutation {
      updateBookingMembership(
        bookingId: "${bookingId}"
        membershipId: "${membershipId}"
        status: ${status}
      ) {
        id
        members {
          id added updated status
          profile { id name slug avatar }
        }
      }
    }
  `

  return dispatch =>
    dispatch({
      types: [UPDATE_MEMBERSHIP, UPDATE_MEMBERSHIP_SUCCESS, UPDATE_MEMBERSHIP_FAIL],
      promise: client =>
        client
          .post('/api/graphql', {
            data: {
              query: payload,
            },
          })
          .then((result) => {
            const data = result.data.updateBookingMembership

            callback(result.errors || false, data)

            if (result.errors) {
              dispatch(showSnackbar(result.errors[0].message))
            } else {
              dispatch(showSnackbar('Status updated'))
              dispatch(updateProjectStateData('bookings', bookingId, data))
            }

            return result
          }),
    })
}

export function unload() {
  return {
    type: UNLOAD,
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case SAVE:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case SAVE_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data },
        errors: action.result.errors || null,
      }
    case SAVE_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        data: state.data,
        errors: action.error,
      }

    case CANCEL:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case CANCEL_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: {},
        errors: action.result.errors || null,
      }
    case CANCEL_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case ADD_TO_LIST:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case ADD_TO_LIST_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.addMemberToBooking },
        errors: action.result.errors || null,
      }
    case ADD_TO_LIST_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case REMOVE_FROM_LIST:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case REMOVE_FROM_LIST_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.removeMemberFromBooking },
        errors: action.result.errors || null,
      }
    case REMOVE_FROM_LIST_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case UPDATE_MEMBERSHIP:
      return {
        loading: true,
        loaded: false,
        data: state.data,
      }
    case UPDATE_MEMBERSHIP_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.updateBookingMembership },
        errors: action.result.errors || null,
      }
    case UPDATE_MEMBERSHIP_FAIL:
      return {
        ...state,
        loading: false,
        loaded: false,
        errors: action.error,
      }

    case UNLOAD:
      return {
        ...initialState,
      }

    default:
      return state
  }
}
