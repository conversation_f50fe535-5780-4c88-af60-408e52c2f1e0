import noop from 'helpers/noop'

const OPEN = 'confirmPrompt/OPEN'
const CLOSE = 'confirmPrompt/CLOSE'

const initialState = {
  open: false,
  title: '',
  message: '',
  onUserResponse: noop,
}

export function open(title, message, onUserResponse = noop) {
  return {
    type: OPEN,
    title,
    message,
    onUserResponse,
  }
}

export function close() {
  return {
    type: CLOSE,
    message: '',
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case OPEN:
      return {
        ...action,
        open: true,
      }
    case CLOSE:
      return {
        ...initialState,
      }

    default:
      return state
  }
}
