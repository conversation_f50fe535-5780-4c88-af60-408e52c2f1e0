import sanitize from 'helpers/sanitize'

const LOAD = 'account/LOAD'
const LOAD_SUCCESS = 'account/LOAD_SUCCESS'
const LOAD_FAIL = 'account/LOAD_FAIL'
const CREATE = 'account/CREATE'
const CREATE_SUCCESS = 'account/CREATE_SUCCESS'
const CREATE_FAIL = 'account/CREATE_FAIL'
const SAVE = 'account/SAVE'
const SAVE_SUCCESS = 'account/SAVE_SUCCESS'
const SAVE_FAIL = 'account/SAVE_FAIL'
const RESET = 'account/RESET'

const initialState = {
  loading: true,
  loaded: false,
  data: {},
}

export function isLoaded(globalState) {
  return globalState.account && globalState.account.loaded
}

export function load(id) {
  const args = `id: "${id}"`

  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: (client) => client.get('/api/graphql', {
      params: {
        query: `{
            account(${args}) {
              id
              created
              updated
              email
              firstName
              lastName
              permissions {
                admin representative
              }
              profiles {
                id slug name approved
              }

            }
          }`,
      },
    }),
  }
}

export function create(name, callback) {

  const payload = `
    mutation {
      createAccount(
        name: "${sanitize.text(name)}"
      ) {
        id
      }
    }
  `

  return {
    types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
    promise: (client) => client.post('/api/graphql', {
      data: {
        query: payload,
      },
    }).then(result => {
      if (callback) {
        callback(result.errors || false, result.data.createaccount)
      }

      return result
    }),
  }
}

export function save(account, callback) {
  const queryName = account.id ? 'updateAccount' : 'createAccount'
  const fields = Object.keys(account)
  const args = fields.map(key => `${key}: ${sanitize.byType(account[key])}`).join('\n')

  const payload = `mutation {
    ${queryName}(
      ${args}
    ) {
      ${fields.join('\n')}
    }
  }`

  return {
    types: [SAVE, SAVE_SUCCESS, SAVE_FAIL],
    promise: (client) => client.post('/api/graphql', {
      data: {
        query: payload,
      },
    }).then(result => {
      if (callback) callback(result)
      return result.data[queryName]
    }),
  }
}

export function reset() {
  return {
    type: RESET,
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {

    case LOAD:
      return {
        ...state,
        action: 'load',
        loading: true,
        loaded: false,
        data: null,
        errors: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        action: 'load',
        loading: false,
        loaded: true,
        data: action.result.data.account,
        errors: action.result.errors || null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        action: 'load',
        loaded: false,
        loading: true,
        data: null,
        errors: [action.error],
      }

    case CREATE:
      return {
        action: 'create',
        loading: true,
        loaded: false,
        data: state.data,
      }
    case CREATE_SUCCESS:
      return {
        ...state,
        action: 'create',
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result.data.createAccount },
        error: action.result.errors || null,
      }
    case CREATE_FAIL:
      return {
        ...state,
        action: 'create',
        loading: false,
        loaded: false,
        error: action.error,
      }

    case SAVE:
      return {
        action: 'save',
        loading: true,
        loaded: false,
        data: state.data,
      }
    case SAVE_SUCCESS:
      return {
        ...state,
        action: 'save',
        loading: false,
        loaded: true,
        data: { ...state.data, ...action.result },
        error: action.result.errors || null,
      }
    case SAVE_FAIL:
      return {
        ...state,
        action: 'save',
        loading: false,
        loaded: false,
        data: state.data,
        error: action.error,
      }

    case RESET:
      return {
        ...initialState,
      }

    default:
      return state
  }
}
