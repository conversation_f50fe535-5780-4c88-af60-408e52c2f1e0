import sanitize from 'helpers/sanitize'

const LOAD = 'castings/LOAD'
const LOAD_SUCCESS = 'castings/LOAD_SUCCESS'
const LOAD_FAIL = 'castings/LOAD_FAIL'

const initialState = {
  loading: true,
  loaded: false,
  data: [],
}


export function load(query = {}) {
  const fields = Object.keys(query)
  const args = fields.map(key => `${key}: ${sanitize.byType(query[key])}`).join('\n')

  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client => client.get('/api/graphql', {
      params: {
        query: `{
            castings ${fields.length ? `(${args})` : ''} {
              id
              name
              project { id name avatar }
            }
          }`,
      },
    }),
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {

    case LOAD:
      return {
        ...state,
        loading: true,
        loaded: false,
        data: [],
        error: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        loading: false,
        loaded: true,
        data: action.result.data.castings,
        error: action.result.errors || null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        loaded: false,
        loading: false,
        data: [],
        error: [action.error],
      }

    default:
      return state
  }
}
