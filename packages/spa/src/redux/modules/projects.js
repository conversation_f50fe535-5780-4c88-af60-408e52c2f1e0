import sanitize from 'helpers/sanitize'

const LOAD = 'projects/LOAD'
const LOAD_SUCCESS = 'projects/LOAD_SUCCESS'
const LOAD_FAIL = 'projects/LOAD_FAIL'

const initialState = {
  loading: true,
  loaded: false,
  action: 'load',
  data: [],
}

export function isLoaded(globalState) {
  return globalState.projects && globalState.projects.loaded
}

export function load(query = {}) {
  const fields = Object.keys(query)
  const args = fields.map(key => `${key}: ${sanitize.byType(query[key])}`).join('\n')

  return {
    types: [LOAD, LOAD_SUCCESS, LOAD_FAIL],
    promise: client =>
      client.get('/api/graphql', {
        params: {
          query: `{
            projects ${fields.length ? `(${args})` : ''} {
              id
              name
              avatar
              type
              usage
              otherUsage

              castings {
                id
                members {
                  id
                  status
                }
              }

              bookings {
                id
                approved
                members {
                  id
                  status
                }
              }
            }
          }`,
        },
      }),
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case LOAD:
      return {
        ...state,
        action: 'load',
        loading: true,
        loaded: false,
        data: [],
        error: null,
      }
    case LOAD_SUCCESS:
      return {
        ...state,
        action: 'load',
        loading: false,
        loaded: true,
        data: action.result.data.projects,
        error: null,
      }
    case LOAD_FAIL:
      return {
        ...state,
        action: 'load',
        loaded: false,
        loading: true,
        data: [],
        error: action.error,
      }

    default:
      return state
  }
}
