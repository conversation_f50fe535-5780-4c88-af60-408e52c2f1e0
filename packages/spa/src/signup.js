import 'babel-polyfill'
import React from 'react'
import <PERSON>actDOM from 'react-dom'
import injectTapEventPlugin from 'react-tap-event-plugin' // temporary React/Material-UI dependency for tap events: https://github.com/callemall/material-ui#react-tap-event-plugin

import SignUpApp from 'containers/Signup/Signup'
import MediaQueryContext from 'components/MediaQueryContext/MediaQueryContext'

injectTapEventPlugin()

ReactDOM.render(
  <MediaQueryContext>
    <SignUpApp />
  </MediaQueryContext>,
  document.getElementById('signup')
)

if (process.env.NODE_ENV !== 'production') {
  window.React = React // enable debugger
}
