import 'babel-polyfill'
import React from 'react'
import { render } from 'react-dom'
import { Provider } from 'react-redux'
import { Router, hashHistory } from 'react-router'
import injectTapEventPlugin from 'react-tap-event-plugin' // temporary React/Material-UI dependency for tap events: https://github.com/callemall/material-ui#react-tap-event-plugin

import createStore from 'redux/create'
import ApiClient from 'helpers/fetchClient'
import getRoutes from './routes'

injectTapEventPlugin()

const client = new ApiClient()

const store = createStore(getRoutes, hashHistory, client, window.__data)

render(
  <Provider store={store} key="provider">
    <Router history={hashHistory}>
      {getRoutes(store)}
    </Router>
  </Provider>,
  document.getElementById('root')
)

if (process.env.NODE_ENV !== 'production') {
  window.React = React // enable debugger
}
