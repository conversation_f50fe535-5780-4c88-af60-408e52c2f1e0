echo "Running deployment script..."

: "${AWS_ACCESS_KEY_ID:?Environment variable AWS_ACCESS_KEY_ID should be non-empty}"

: "${AWS_SECRET_ACCESS_KEY:?Environment variable AWS_SECRET_ACCESS_KEY should be non-empty}"

: "${AWS_DEFAULT_REGION:?Environment variable AWS_DEFAULT_REGION should be non-empty}"

: "${AWS_DEFAULT_BUCKET:?Environment variable AWS_DEFAULT_BUCKET should be non-empty}"

INDEX_NAME="index.html"

bash tasks/compress.sh

cd dist

cd compressed

echo "Pushing files to s3"

for file in *.*;
do
  aws s3 cp $file s3://$AWS_DEFAULT_BUCKET/dist/$file --content-encoding gzip --acl public-read
done

gzip -c "../../static/$INDEX_NAME" > "$INDEX_NAME"
aws s3 cp $INDEX_NAME s3://$AWS_DEFAULT_BUCKET/$INDEX_NAME --content-encoding gzip --acl public-read
rm "$INDEX_NAME"
echo "Deployemnt completed successfully"
