const webpack = require('webpack')
const path = require('path')

function getPlugins(env, app) {
  const GLOBALS = {
    'process.env.NODE_ENV': JSON.stringify(env),
    __DEV__: env === 'development',
    __DEVELOPMENT__: env === 'development',
    __DEVTOOLS__: true,
  }

  const plugins = [
    new webpack.ProvidePlugin({
      fetch: 'imports?this=>global!exports?global.fetch!whatwg-fetch', // http://mts.io/2015/04/08/webpack-shims-polyfills/
    }),
    new webpack.optimize.OccurenceOrderPlugin(),
    new webpack.DefinePlugin(GLOBALS), // Tells React to build in prod mode. https://facebook.github.io/react/downloads.html
  ]

  switch (env) {
    case 'production':
      plugins.push(new webpack.optimize.DedupePlugin())
      plugins.push(
        new webpack.optimize.UglifyJsPlugin({
          minimize: true,
          sourceMap: false,
          warnings: false,
        })
      )
      plugins.push(
        new webpack.optimize.CommonsChunkPlugin(
          'vendor',
          `vendor.${app || 'bundle'}.js`
        )
      )
      break
    case 'development':
    default:
      plugins.push(new webpack.optimize.DedupePlugin())
      // plugins.push(new webpack.HotModuleReplacementPlugin())
      plugins.push(
        new webpack.optimize.CommonsChunkPlugin(
          'vendor',
          `vendor.${app || 'bundle'}.js`
        )
      )
      plugins.push(new webpack.NoErrorsPlugin())
      break
  }

  return plugins
}

function getLoaders() {
  const loaders = [
    { test: /\.js$/, loader: 'babel-loader', exclude: /node_modules/ },
    { test: /\.json$/, loader: 'json-loader' },
    {
      test: /(\.css|\.scss)$/,
      include: path.join(__dirname, 'src'),
      loader: 'style!css?modules&importLoaders=2&sourceMap&localIdentName=[local]___[hash:base64:5]!autoprefixer?browsers=last 2 version!sass?outputStyle=expanded&sourceMap',
    },
    {
      test: /\.woff(\?v=\d+\.\d+\.\d+)?$/,
      loader: 'url?limit=10000&mimetype=application/font-woff',
    },
    {
      test: /\.woff2(\?v=\d+\.\d+\.\d+)?$/,
      loader: 'url?limit=10000&mimetype=application/font-woff',
    },
    {
      test: /\.ttf(\?v=\d+\.\d+\.\d+)?$/,
      loader: 'url?limit=10000&mimetype=application/octet-stream',
    },
    { test: /\.eot(\?v=\d+\.\d+\.\d+)?$/, loader: 'file' },
    {
      test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
      loader: 'url?limit=10000&mimetype=image/svg+xml',
    },
    { test: /(\.jpg|\.png|\.gif)$/, loader: 'url-loader?limit=10240' },
  ]

  return loaders
}

function getAppEntry(env, app) {
  switch (app) {
    case 'signup':
      return ['./src/signup']
    case 'compcard':
      return ['./src/compcard']
    default:
      return ['./src/index']
  }
}

function getVendorEntry(env, app) {
  if (app === 'signup') {
    return [
      'babel-polyfill',
      'material-ui', // ~433.6Kb
      'react',
      'react-dom',
      'react-tap-event-plugin', // ~1.52kb
    ]
  } else if (app === 'compcard') {
    return [
      'babel-polyfill',
      'material-ui', // ~433.6Kb
      'react',
      'react-dom',
    ]
  }

  const entry = [
    'babel-polyfill',
    'history',
    'localforage',
    'material-ui', // ~433.6Kb
    'react',
    'react-dnd', // ~54kb
    'react-dnd-html5-backend', // ~20kb
    'react-dom',
    'react-redux',
    'react-motion',
    'react-router', // ~26kb
    'react-router-redux', // ~1.38kb
    'react-tap-event-plugin', // ~1.52kb
    'recompose', // ~31.56kb ---?
    'redux',
  ]

  if (env === 'development') {
    entry.push('redux-devtools')
  }

  return entry
}

function getDevServer(env) {
  if (env === 'development') {
    return {
      proxy: {
        '/api*': {
          target: 'http://localhost:3000/',
          secure: false,
        },
      },
    }
  }

  return {}
}

module.exports = function getConfig(env, app) {
  return {
    debug: true,
    // more info on devtool: https://webpack.github.io/docs/build-performance.html#sourcemaps and https://webpack.github.io/docs/configuration.html#devtool
    devtool: env === 'production' ? 'source-map' : 'eval-source-map',
    noInfo: false, // set to false to see a list of every file being bundled.
    entry: {
      app: getAppEntry(env, app),
      vendor: getVendorEntry(env, app),
    },
    target: env === 'test' ? 'node' : 'web', // necessary per https://webpack.github.io/docs/testing.html#compile-and-test
    output: {
      path: path.join(__dirname, '..', '..', 'dist'),
      filename: `${app || 'bundle'}.js`,
      publicPath: 'dist/',
    },
    plugins: getPlugins(env, app),
    module: {
      loaders: getLoaders(env, app),
    },
    resolve: {
      fallback: [path.join(__dirname, '..', '..', 'node_modules')],
      modulesDirectories: [
        path.resolve(),
        'src',
        'node_modules',
        path.join(__dirname, '..', '..', 'node_modules'),
      ],
      extensions: ['', '.json', '.js'],
    },
    resolveLoader: {
      fallback: [path.join(__dirname, '..', '..', 'node_modules')],
      modulesDirectories: [
        path.resolve(),
        'src',
        'node_modules',
        path.join(__dirname, 'node_modules'),
      ],
    },
    devServer: getDevServer(env),
  }
}
