
// TODO: urls should really not be hard coded.
export const environment = {
  development: {
    apiHost: `${process.env.APIHOST || 'localhost'}:${process.env.APIPORT || 3001}`,
    mediaHost: 'rt-lookbook-dev.s3-website-us-west-2.amazonaws.com',
    mediaPrefix: 'media-images',
  },
  production: {
    apiHost: 'lb',
    apiPort: 80,
    mediaHost: 'rt-lookbook-dev.s3-website-us-west-2.amazonaws.com',
    mediaPrefix: 'media',
  },
}[process.env.NODE_ENV || 'development']


export default Object.assign({
  app: {
    title: 'Lookbook',
    description: 'Lookbook',
    meta: {
      charSet: 'utf-8',
      property: {
        'og:site_name': 'Lookbook',
        'og:image': '',
        'og:locale': 'en_US',
        'og:title': 'Lookbook',
        'og:description': 'Lookbook',
        'twitter:card': 'summary',
        'twitter:site': '@lookbookapp',
        'twitter:creator': '@lookbookapp',
        'twitter:title': 'Lookbook',
        'twitter:description': 'Lookbook',
        'twitter:image': '',
        'twitter:image:width': '200',
        'twitter:image:height': '200',
      },
    },
  },
}, environment)
