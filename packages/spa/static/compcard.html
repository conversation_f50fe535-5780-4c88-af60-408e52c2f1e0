<!DOCTYPE html>
<html lang="en">
  <head>
  <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui">
    <title>Lookbook Comp Card</title>

    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css">

    <style type="text/css">
      body {
        margin: 0;
        padding: 0;
        background: #eceff1;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <style type="text/css">
        body {
          background: #FF0303;
          font-size: 16px;
        }

        html, body, #root {
          height: 100%;
        }

        #root {
          display: flex;
          justify-content: space-around;
          align-items: center;

        }

        #loader {
          flex: 1 0 auto;
        }

        /* via http://projects.lukehaas.me/css-loaders/ */
        .loader:before,
        .loader:after,
        .loader {
          border-radius: 50%;
          width: 2.5em;
          height: 2.5em;
          -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
          -webkit-animation: spinner 1.8s infinite ease-in-out;
          animation: spinner 1.8s infinite ease-in-out;
        }

        .loader {
          font-size: 1em;
          margin: -3.25em auto;
          position: relative;
          text-indent: -9999em;
          -webkit-transform: translateZ(0);
          -ms-transform: translateZ(0);
          transform: translateZ(0);
          -webkit-animation-delay: -0.16s;
          animation-delay: -0.16s;
        }

        .loader:before {
          left: -3.5em;
          -webkit-animation-delay: -0.32s;
          animation-delay: -0.32s;
        }

        .loader:after {
          left: 3.5em;
        }

        .loader:before,
        .loader:after {
          content: '';
          position: absolute;
          top: 0;
        }

        @-webkit-keyframes spinner {
          0%,
          80%,
          100% {
            box-shadow: 0 2.5em 0 -1.3em #ffffff;
          }
          40% {
            box-shadow: 0 2.5em 0 0 #ffffff;
          }
        }

        @keyframes spinner {
          0%,
          80%,
          100% {
            box-shadow: 0 2.5em 0 -1.3em #ffffff;
          }
          40% {
            box-shadow: 0 2.5em 0 0 #ffffff;
          }
        }
      </style>
      <div id="loader"><div class="loader">Loading...</div></div>
    </div>

  <script src="dist/vendor.compcard.js"></script>
  <script src="dist/compcard.js"></script>

  <script type="text/javascript">
  WebFontConfig = {
  google: {
    families: [
      'Roboto:400,500italic,500,400italic,300italic,300,100italic,100:latin',
      'Source+Sans+Pro:400,900italic,900,700italic,700,600italic,600,400italic,300italic,300,200italic,200:latin',
    ],
  },
}
;(function () {
  var wf = document.createElement('script')
  wf.src = `${document.location.protocol == 'https:' ? 'https' : 'http'}://ajax.googleapis.com/ajax/libs/webfont/1/webfont.js`
  wf.type = 'text/javascript'
  wf.async = 'true'
  var s = document.getElementsByTagName('script')[0]
  s.parentNode.insertBefore(wf, s)
}())
</script>
  </body>
</html>
