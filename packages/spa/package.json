{"name": "lookbook-app", "version": "1.0.0", "description": "Lookbook App SPA & signup form", "config": {"jsSrc": "src/"}, "scripts": {"lint": "npm run lint:eslint -s", "lint:eslint": "eslint $npm_package_config_jsSrc", "watch:lint": "watch 'npm run lint -s' $npm_package_config_jsSrc", "test": "cross-env NODE_ENV=test mocha --reporter progress --compilers js:babel-core/register  --require ./testSetup.js --recursive \"./src/**/tests/**/*.js\"", "setup": "better-npm-run setup", "dev": "webpack-dev-server --config config/webpack/development.js --hot --progress --colors --port 8080 --inline --content-base static/", "dev:signup": "webpack-dev-server --config config/webpack/signup-development.js --hot --progress --colors --port 8080 --inline --content-base static/", "dev:compcard": "webpack-dev-server --config config/webpack/compcard-development.js --hot --progress --colors --port 8080 --inline --content-base static/", "build": "webpack --config config/webpack/production.js --progress --profile --colors", "build:signup": "webpack --config config/webpack/signup-production.js --progress --profile --colors", "build:compcard": "webpack --config config/webpack/compcard-production.js --progress --profile --colors", "compress": "better-npm-run compress", "deploy": "better-npm-run deploy", "postinstall": "npm run setup"}, "repository": {"type": "git"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"crypto-js": "3.1.9-1", "fbjs": "^0.8.12", "history": "2.0.1", "immutability-helper": "^2.2.3", "localforage": "^1.5.0", "material-ui": "^0.18.6", "mousetrap": "^1.6.1", "prop-types": "^15.5.10", "react": "^15.6.1", "react-addons-css-transition-group": "^15.6.0", "react-dnd": "^2.4.0", "react-dnd-html5-backend": "^2.4.1", "react-document-meta": "^2.1.2", "react-dom": "^15.6.1", "react-image-lightbox": "^4.1.0", "react-images": "^0.5.4", "react-motion": "^0.5.0", "react-redux": "^5.0.5", "react-router": "2.7.0", "react-router-redux": "2.1.0", "react-swipeable-views": "^0.12.3", "react-tap-event-plugin": "2.0.1", "recompose": "^0.23.5", "redux": "^3.7.1", "striptags": "^3.0.1", "sugar-date": "^2.0.4", "whatwg-fetch": "^2.0.3"}, "devDependencies": {"autoprefixer": "^6.7.7", "autoprefixer-loader": "3.2.0", "babel-core": "^6.25.0", "babel-loader": "^6.4.1", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "babel-register": "^6.24.1", "babel-runtime": "^6.23.0", "better-npm-run": "^0.0.15", "colors": "1.1.2", "cross-env": "^4.0.0", "css-loader": "0.23.1", "exports-loader": "0.6.3", "file-loader": "0.8.5", "imports-loader": "0.6.5", "jsdom": "^9.12.0", "json-loader": "0.5.4", "mocha": "^3.4.2", "node-sass": "3.4.2", "postcss-loader": "0.8.0", "postcss-nested": "1.0.0", "react-addons-test-utils": "^15.6.0", "react-hot-loader": "^1.3.1", "react-transform-catch-errors": "^1.0.2", "react-transform-hmr": "^1.0.4", "redux-devtools": "^3.4.0", "sass-loader": "3.1.2", "strip-loader": "0.1.1", "style-loader": "0.13.0", "url-loader": "0.5.7", "watch": "^1.0.2", "webpack": "1.12.12", "webpack-dev-server": "1.14.1", "webpack-hot-middleware": "2.6.4"}, "betterScripts": {"setup": {"command": "tasks/make-config.sh"}, "build": {"command": "node tasks/build.js", "env": {"NODE_ENV": "production"}}, "dev": {"command": "", "env": {"NODE_ENV": "development"}}, "compress": {"command": "tasks/compress.sh", "env": {"NODE_ENV": "production"}}, "deploy": {"command": "tasks/deploy.sh", "env": {"NODE_ENV": "production"}}}}