{"name": "lookbook-api", "version": "1.0.0", "description": "Lookbook GraphQL API", "config": {"jsSrc": "src/"}, "scripts": {"start": "better-npm-run start", "dev": "better-npm-run dev", "lint": "npm run lint:eslint -s", "lint:eslint": "eslint $npm_package_config_jsSrc", "watch:lint": "watch 'npm run lint -s' $npm_package_config_jsSrc", "test": "npm run test:mocha -s", "test:mocha": "cross-env NODE_ENV=test mocha --reporter progress --compilers js:babel-core/register,js:babel-polyfill --recursive \"./src/**/tests/**/*.js\"", "setup": "better-npm-run setup", "postinstall": "npm run setup"}, "repository": {"type": "git"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"aws-sdk": "^2.82.0", "babel-core": "^6.25.0", "babel-plugin-transform-regenerator": "^6.24.1", "babel-polyfill": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2015-node5": "^1.2.0", "babel-preset-es2016-node5": "1.1.2", "babel-preset-stage-0": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "babel-register": "^6.24.1", "crypto-js": "3.1.9-1", "feedparser": "^2.2.1", "got": "^7.1.0", "graphql": "0.5.0", "iconv-lite": "0.6.3", "jsonwebtoken": "^7.4.1", "kcors": "1.1.0", "koa": "1.1.2", "koa-compose": "3.0.0", "koa-conditional-get": "1.0.3", "koa-etag": "2.1.1", "koa-graphql": "0.5.1", "koa-helmet": "1.0.0", "koa-mount": "1.3.0", "koa-multer": "1.0.0", "koa-passport": "1.3.1", "mongoose": "^4.11.1", "multer": "1.1.0", "node-fetch": "2.7.0", "passport-jwt": "^2.2.1", "passport-local": "1.0.0", "ramda": "^0.24.1", "request": "^2.81.0", "shortid": "^2.2.8", "slug": "0.9.1", "uuid": "11.1.0"}, "devDependencies": {"babel-eslint": "^7.2.3", "better-npm-run": "^0.0.15", "chai": "3.5.0", "cross-env": "^5.0.1", "mocha": "^3.4.2", "nodemon": "^1.11.0", "watch": "^1.0.2"}, "betterScripts": {"setup": {"command": "tasks/make-config.sh"}, "start": {"command": "node tasks/serve.js", "env": {"NODE_ENV": "production"}}, "dev": {"command": "nodemon --ext \".js\" --watch src/ --watch config/ --watch tasks/serve.js", "env": {"NODE_ENV": "development"}}}}