import { Graph<PERSON>ID, GraphQLString, GraphQLList } from 'graphql/type'

import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'

import { ProjectType } from './types'
import Projects from '../models/projects'
import { requireAdminOrRepresentative, isAdmin, getUserId } from '../authentication'

const projectFields = modelToGraphQLFields(Projects, true)

delete projectFields.id
delete projectFields._id
delete projectFields.account

export default {
  project: {
    type: ProjectType,
    args: {
      id: { type: GraphQLID },
      slug: { type: GraphQLString },
    },
    resolve: (root, { id, slug }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      if (!id && !slug) {
        throw new Error('Must provide either id or slug')
      }

      const select = getSelectFieldsFromAST(fieldASTs)

      const account = getUserId(context)
      const query = Object.assign({ account }, id ? { _id: id } : { slug })

      if (isAdmin(context)) {
        delete query.account
      }

      return Projects.findOne(query).select(select)
    },
  },

  projects: {
    type: new GraphQLList(ProjectType),
    args: {
      ...projectFields,
    },
    resolve: (root, args, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const select = getSelectFieldsFromAST(fieldASTs)
      const account = getUserId(context)
      const query = { ...args, account }

      if (isAdmin(context)) {
        delete query.account
      }

      return Projects.find(query).select(select)
    },
  },
}
