import {
  GraphQLBoolean,
  GraphQLID,
  GraphQLEnumType,
  GraphQLNonNull,
  GraphQLList,
} from 'graphql/type'

import sleep from '../utils/sleep'
import { requireRepresentative, requireAdminOrRepresentative, getUserId } from '../authentication'
import { getSessionUserId } from '../utils/session'
import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { ProjectType } from './types'
import Projects from '../models/projects'
import { Booking } from '../models/bookings'
import { createMedia, deleteMedia } from '../models/media'

const projectMediaType = new GraphQLEnumType({
  name: 'projectMedia<PERSON>ield',
  values: {
    avatar: { value: 'avatar', description: 'Avatar/Profile Photo' },
  },
})

const projectFields = modelToGraphQLFields(Projects)

delete projectFields.id
delete projectFields._id
delete projectFields.created
delete projectFields.updated
delete projectFields.account
delete projectFields.profile

export default {
  createProject: {
    type: ProjectType,
    args: {
      ...projectFields,
      profile: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: (root, data, context) => {
      requireRepresentative(context)

      const account = getUserId(context)

      return Projects.create({ ...data, account })
    },
  },

  updateProject: {
    type: ProjectType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      ...projectFields,
    },
    resolve: async (root, { id, ...updates }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      // TODO: include scoping project to this user, so only user can update their own projects....

      return Projects.findById(id).select(getSelectFieldsFromAST(fieldASTs)).then((doc) => {
        if (!doc) {
          throw new Error('Profile does not exist.')
        }

        doc.set(updates)
        return doc.save()
      })
    },
  },

  cancelProject: {
    type: GraphQLBoolean,
    args: {
      projectId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { projectId }, context) => {
      requireRepresentative(context)
      const account = getSessionUserId(context)

      const [project, hasBookings] = await Promise.all([
        Projects.findOne({
          _id: projectId,
          account,
        }),
        Booking.find({ project: projectId }).then(bookings => !!bookings.length),
      ])

      if (!project) {
        throw new Error('Not a valid project.')
      }

      if (hasBookings) {
        throw new Error('Cannot cancel project which has existing bookings.')
      }

      try {
        await deleteMedia(project.avatar)
      } catch (error) {
        throw new Error('Error deleting associated project media.')
      }

      return !!await project.remove()
    },
  },

  attachProjectMedia: {
    type: ProjectType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      field: { type: new GraphQLNonNull(projectMediaType) },
    },
    resolve: async (root, { id, field }, ast, { fieldASTs }) => {
      const select = getSelectFieldsFromAST(fieldASTs)

      const project = await Projects.findById(id).select(`_id ${field}`)
      if (!project) {
        throw new Error('Project does not exist.')
      }

      const file = root.request.files[field][0]

      const deletePreviousMediaId = project[field] ? project[field] : false

      const mediaDoc = await createMedia({ useAs: field }, file.path, deletePreviousMediaId)

      await sleep(3000) // kinda lameo pause here to wait for lambda to process our images before returning to user.

      return Projects.findByIdAndUpdate(
        id,
        { $set: { [field]: mediaDoc.id } },
        { new: true },
      ).select(select)
    },
  },
}
