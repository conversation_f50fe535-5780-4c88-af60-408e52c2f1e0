import {
  GraphQLObjectType,
  GraphQLID,
  GraphQLString,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLList,
  GraphQLEnumType,
} from 'graphql/type'

import { requireAdminOrRepresentative } from '../authentication'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import Projects from '../models/projects'
import { Casting } from '../models/castings'
import { Booking } from '../models/bookings'
import { CastingType } from '../casting/types'
import { BookingType } from '../booking/types'

const projectFields = modelToGraphQLFields(Projects, false, true)

export const ProjectType = new GraphQLObjectType({
  name: 'Project',
  fields: () => ({
    ...projectFields,

    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    castings: {
      type: new GraphQLList(CastingType),
      resolve: ({ _id }, junk, context, { fieldASTs }) => {
        requireAdminOrRepresentative(context)
        // todo: only let rep access their own castings...

        const query = {
          project: _id,
        }
        const select = getSelectFieldsFromAST(fieldASTs)

        return Casting.find(query)
          .select(`${select} project booking members`)
          .populate('booking members.profile')
      },
    },

    bookings: {
      type: new GraphQLList(BookingType),
      resolve: ({ _id }, junk, context, { fieldASTs }) => {
        requireAdminOrRepresentative(context)
        // todo: only let rep access their own castings...

        const query = {
          project: _id,
        }
        const select = getSelectFieldsFromAST(fieldASTs)

        return Booking.find(query)
          .select(`${select} project booking members`)
          .populate('members.profile')
      },
    },
  }),
})
