import { Graph<PERSON><PERSON>, Graph<PERSON>String, GraphQLList } from 'graphql/type'

import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import minMaxArguments from '../utils/minMaxArguments'

import { ProfileType, CompCardType } from './types'
import { ModelProfile } from '../models/profiles'
import { requireAuthentication, isAdmin } from '../authentication'

const modelProfileFields = modelToGraphQLFields(ModelProfile, true)

delete modelProfileFields.id
delete modelProfileFields._id

const minMaxFields = ['height', 'bust', 'waist', 'hips', 'shoeSize', 'suit', 'inseam', 'birthYear']

export default {
  profile: {
    type: ProfileType,
    args: {
      id: { type: GraphQLID },
      slug: { type: GraphQLString },
    },
    resolve: (root, { id, slug }, context, { fieldASTs }) => {
      requireAuthentication(context)

      if (!id && !slug) {
        throw new Error('Must provide either id or slug')
      }

      const select = getSelectFieldsFromAST(fieldASTs)
      return ModelProfile.findOne(id ? { _id: id } : { slug }).select(select)
    },
  },

  profiles: {
    type: new GraphQLList(ProfileType),
    args: {
      ids: { type: new GraphQLList(GraphQLID) },
      ...modelProfileFields,
      ...minMaxArguments(minMaxFields),
    },
    resolve: (root, args, context, { fieldASTs }) => {
      requireAuthentication(context)

      const select = getSelectFieldsFromAST(fieldASTs)
      const query = args

      if (!isAdmin(context)) {
        query.approved = true
      }

      minMaxFields.forEach((field) => {
        const minMaxField = {}
        const fieldName = `${field.substr(0, 1).toUpperCase()}${field.substr(1)}`

        if (args[`min${fieldName}`]) {
          minMaxField.$gte = args[`min${fieldName}`]
          delete query[`min${fieldName}`]
        }

        if (args[`max${fieldName}`]) {
          minMaxField.$lte = args[`max${fieldName}`]
          delete query[`max${fieldName}`]
        }

        if (Object.keys(minMaxField).length) {
          query[field] = minMaxField
        }
      })

      if (query.ids) {
        query._id = { $in: query.ids }
        delete query.ids
      }

      return ModelProfile.find(query).select(select)
    },
  },

  publicCompCard: {
    type: CompCardType,
    args: {
      id: { type: GraphQLID },
      slug: { type: GraphQLString },
    },
    resolve: (root, { id, slug }, context, { fieldASTs }) => {
      if (!id && !slug) {
        throw new Error('Must provide either id or slug')
      }

      const select = getSelectFieldsFromAST(fieldASTs)
      return ModelProfile.findOne(
        id ? { _id: id, approved: true } : { slug, approved: true },
      ).select(select)
    },
  },
}
