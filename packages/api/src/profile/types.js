import mongoose from 'mongoose'
import {
  GraphQLObjectType,
  GraphQLID,
  GraphQLString,
  GraphQLFloat,
  GraphQLList,
  GraphQLEnumType,
} from 'graphql/type'

import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { ModelProfile } from '../models/profiles'
import Projects from '../models/projects'
import { JobType } from '../project/types'

const modelProfileFields = modelToGraphQLFields(ModelProfile)

export const ProfileTypes = new GraphQLEnumType({
  name: 'profileType',
  values: {
    model: { value: 'model', description: 'Talent: model' },
    representative: { value: 'representative', description: 'Representative' },
    company: { value: 'company', description: 'Company' },
  },
})

export const RepresentativeType = new GraphQLObjectType({
  name: 'Representative',
  fields: {
    ...modelProfileFields,

    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },
  },
})

export const ProfileType = new GraphQLObjectType({
  name: 'Profile',
  fields: {
    ...modelProfileFields,

    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    // TODO: remove this cuz it's a hack, email is actually on account model
    email: {
      type: GraphQLID,
      resolve: null,
    },

    /*
    projects: {
      type: new GraphQLList(JobProjectType),
      resolve: ({ _id }, args, ast, { fieldASTs }) => {
        const select = getSelectFieldsFromAST(fieldASTs)
        const query = {
          $or: [
            { castingList: { $elemMatch: { _id: { $in: [_id] } } } },
            { bookingList: { $elemMatch: { _id: { $in: [_id] } } } },
          ],
        }

        return Projects.find(query).select(`${select} profile`).populate('profile').then((docs) => {
          const profileId = mongoose.Types.ObjectId(_id) // eslint-disable-line new-cap
          const result = docs.map((_doc) => {
            const doc = _doc
            doc.profileId = profileId
            return doc
          })

          return result
        })
      },
    },

    castings: {
      type: new GraphQLList(ProfileProjectType),
      resolve: ({ _id }, args, ast, { fieldASTs }) => {
        const select = getSelectFieldsFromAST(fieldASTs)
        const query = {
          $or: [{ castingList: { $elemMatch: { _id: { $in: [_id] } } } }],
        }

        return Projects.find(query).select(select).then((docs) => {
          const profileId = mongoose.Types.ObjectId(_id) // eslint-disable-line new-cap
          const result = docs.map((_doc) => {
            const doc = _doc
            doc.profileId = profileId
            return doc
          })

          return result
        })
      },
    },
*/
  },
})

// This type is accessible publicly, so only include fields we want public..
// we explicitly declare fields here to avoid leaking something private
export const CompCardType = new GraphQLObjectType({
  name: 'CompCard',
  fields: {
    name: {
      type: GraphQLString,
    },
    slug: {
      type: GraphQLString,
    },
    gender: {
      type: GraphQLString,
    },
    height: {
      type: GraphQLFloat,
    },
    waist: {
      type: GraphQLFloat,
    },
    inseam: {
      type: GraphQLFloat,
    },
    suit: {
      type: GraphQLFloat,
    },
    hips: {
      type: GraphQLFloat,
    },
    bust: {
      type: GraphQLFloat,
    },
    dressSize: {
      type: GraphQLFloat,
    },
    chest: {
      type: GraphQLFloat,
    },
    cup: {
      type: GraphQLFloat,
    },
    eyeColor: {
      type: GraphQLString,
    },
    shoeSize: {
      type: GraphQLFloat,
    },
    hairColor: {
      type: GraphQLString,
    },
    hairLength: {
      type: GraphQLFloat,
    },
    bodyType: {
      type: GraphQLString,
    },

    compCardA: {
      type: GraphQLString,
    },
    compCardB: {
      type: GraphQLString,
    },
    compCardC: {
      type: GraphQLString,
    },
    compCardD: {
      type: GraphQLString,
    },
    compCardCover: {
      type: GraphQLString,
    },
    compCardLabelPosition: {
      type: GraphQLString,
    },
  },
})

export const ProfilePdfType = new GraphQLObjectType({
  name: 'ProfilePdf',
  fields: {
    url: {
      type: GraphQLString,
    },
  },
})
