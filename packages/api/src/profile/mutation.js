import { GraphQLID, GraphQLEnumType, GraphQLNonNull } from 'graphql/type'
import got from 'got'

import { api as apiConfig } from '../../config'
import sleep from '../utils/sleep'
import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'

import Account from '../models/accounts'
import { ProfileType, ProfilePdfType } from './types'
import { ModelProfile } from '../models/profiles'
import { createMedia, deleteMedia } from '../models/media'

const profileMediaType = new GraphQLEnumType({
  name: 'profileMedia<PERSON>ield',
  values: {
    cover: { value: 'cover', description: 'Cover Photo' },
    avatar: { value: 'avatar', description: 'Avatar/Profile Photo' },
    compCardCover: { value: 'compCardCover', description: 'Comp Card Cover Photo' },
    compCardA: { value: 'compCardA', description: 'Comp Card Photo A' },
    compCardB: { value: 'compCardB', description: 'Comp Card Photo B' },
    compCardC: { value: 'compCardC', description: 'Comp Card Photo C' },
    compCardD: { value: 'compCardD', description: 'Comp Card Photo D' },
    media: { value: 'media', description: 'Media gallery item' },
  },
})

const modelProfileFields = modelToGraphQLFields(ModelProfile)

delete modelProfileFields.id
delete modelProfileFields._id
delete modelProfileFields.created
delete modelProfileFields.updated
delete modelProfileFields.account

export default {
  createProfile: {
    type: ProfileType,
    args: {
      ...modelProfileFields,
      email: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { email, ...data }) => {
      const account = await Account.create({ email })
      const profile = { ...data, account: account.id, slug: account.id }

      return ModelProfile.create(profile)
    },
  },

  updateProfile: {
    type: ProfileType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      ...modelProfileFields,
    },
    resolve: async (root, { id, ...updates }, ast, { fieldASTs }) =>
      ModelProfile.findById(id).select(getSelectFieldsFromAST(fieldASTs)).then((doc) => {
        if (!doc) {
          throw new Error('Profile does not exist.')
        }

        doc.set(updates)
        return doc.save()
      }),
  },

  attachMedia: {
    type: ProfileType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      field: { type: new GraphQLNonNull(profileMediaType) },
    },
    resolve: async (root, { id, field }, ast, { fieldASTs }) =>
      ModelProfile.findById(id)
        .select(`${getSelectFieldsFromAST(fieldASTs)} media`)
        .then(async (doc) => {
          if (!doc) {
            throw new Error('Profile does not exist.')
          }

          const file = root.request.files[field][0]

          let previousId = false
          if (field !== 'media') {
            previousId = doc[field] ? doc[field] : false
          }

          const mediaDoc = await createMedia({ useAs: field }, file.path, previousId)

          await sleep(3000) // kinda lameo pause here to wait for lambda to process our images before returning to user.

          if (field === 'media') {
            return ModelProfile.findByIdAndUpdate(
              id,
              { $push: { media: { $each: [mediaDoc.id], $position: 0 } } },
              { new: true, safe: true },
            )
          }

          return ModelProfile.findByIdAndUpdate(
            id,
            { $set: { [field]: mediaDoc.id } },
            { new: true },
          )
        }),
  },

  detachMedia: {
    type: ProfileType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      mediaId: { type: new GraphQLNonNull(GraphQLID) },
      field: { type: new GraphQLNonNull(profileMediaType) },
    },
    resolve: async (root, { id, mediaId, field }, ast, { fieldASTs }) =>
      ModelProfile.findById(id)
        .select(`${getSelectFieldsFromAST(fieldASTs)} media`)
        .then(async (doc) => {
          if (!doc) {
            throw new Error('Profile does not exist.')
          }

          await deleteMedia(mediaId)

          if (field === 'media') {
            return ModelProfile.findByIdAndUpdate(
              id,
              { $pull: { media: mediaId } },
              { new: true, safe: true },
            )
          }

          return ModelProfile.findByIdAndUpdate(id, { $set: { [field]: null } }, { new: true })
        }),
  },

  generateProfilePdf: {
    type: ProfilePdfType,
    args: {
      slug: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { slug }) =>
      got(`${apiConfig.pdfBackendUrl}?slug=${slug}`).then(response => JSON.parse(response.body)),
  },
}
