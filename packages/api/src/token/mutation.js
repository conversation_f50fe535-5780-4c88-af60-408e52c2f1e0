import {
  GraphQLString,
  GraphQLNonNull,
  GraphQLBoolean,
} from 'graphql/type'
import jwt from 'jsonwebtoken'
import hmacSha512 from 'crypto-js/hmac-sha512'
import { api as apiConfig, secrets } from '../../config'
import { JwtType } from './types'
import Token from '../models/tokens'
import Account from '../models/accounts'

const tokenMutationField = {

  createToken: {
    type: JwtType,
    args: {
      email: { type: new GraphQLNonNull(GraphQLString) },
      passPhrase: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: async (root, { email, passPhrase, ...rest }, context) => {
      const hashedPassPhrase = hmacSha512(passPhrase, secrets.passphrase).toString()

      return await Account.findOne({ email, passPhrase: hashedPassPhrase }).then(doc => {
        if (!doc) {
          throw new Error('Invalid credentials.')
        }

        const token = jwt.sign({ id: doc.id, permissions: doc.permissions }, secrets.jwt, apiConfig.jwt)
        const tokenDocument = new Token({
          token,
          account: doc.id,
        })

        if (process.env.NODE_ENV === 'development' && rest.cookie) {
          context.cookies.set('jwt', token, { expires: new Date('3000-01-01') })
        }

        return tokenDocument.save()
      })
    },
  },

}

if (process.env.NODE_ENV === 'development') {
  tokenMutationField.createToken.args.cookie = { type: GraphQLBoolean, default: false }
}

export default tokenMutationField
