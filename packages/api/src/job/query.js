import { GraphQLBoolean, GraphQLID, GraphQLNonNull, GraphQLList } from 'graphql/type'

import { requireMatchingAccountOrAdmin } from '../permissions'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { selectionContains } from '../utils/graphql'
import { getSessionUserId } from '../utils/session'
import { Profile } from '../models/profiles'
import { Booking } from '../models/bookings'
import { Casting } from '../models/castings'
import { JobType } from './types'

const getJobMembership = (job, profiles) =>
  job.members.find(
    member =>
      ['applied', 'invited', 'confirmed', 'declined', 'released'].includes(member.status) &&
      profiles.includes(member.profile.toString()),
  )

const getCastingMembership = (casting, profiles) =>
  casting.members.find(member => profiles.includes(member.profile.toString()))

export default {
  job: {
    type: JobType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { id }, context, { fieldASTs }) => {
      const account = getSessionUserId(context)
      requireMatchingAccountOrAdmin(context, account)

      const select = getSelectFieldsFromAST(fieldASTs)

      const [job, currentUserProfiles] = await Promise.all([
        Booking.findOne({ _id: id, approved: true })
          .select(`${select} project members visibility`)
          .populate('project'),
        Profile.find({ account }).select('_id'),
      ]).then(([_job, _currentUserProfiles]) => [
        _job && _job.toObject(),
        _currentUserProfiles.map(profile => profile.id),
      ])

      if (!job) {
        throw new Error('Invalid Job')
      }

      job.membership = getJobMembership(job, currentUserProfiles)

      if (selectionContains('castings', fieldASTs) || !job.membership) {
        job.castings = await Casting.find({
          booking: id,
          members: {
            $elemMatch: {
              profile: { $in: currentUserProfiles },
              status: { $in: ['invited', 'confirmed', 'declined', 'released'] },
            },
          },
        }).select('name location notes dates members')

        job.castings = job.castings.map(casting => ({
          ...casting.toObject(),
          membership: getCastingMembership(casting, currentUserProfiles),
        }))
      }

      /*
        the profile exists in neither the booking membership, nor in the casting,
        at all or in a non-pending status, so they don't have access to the job
        (the fact that this code is executed probably means someone is trying to
        access something they shouldn't be accessing)
      */
      if (
        job.visibility !== 'public' &&
        !job.membership &&
        (!job.castings || job.castings.length === 0)
      ) {
        throw new Error('Invalid Job')
      }

      return job
    },
  },

  jobs: {
    type: new GraphQLList(JobType),
    args: {
      profiles: { type: new GraphQLNonNull(new GraphQLList(GraphQLID)) },
      hasMembership: { type: GraphQLBoolean, default: false },
    },
    resolve: async (root, { profiles, hasMembership }, context, { fieldASTs }) => {
      const account = getSessionUserId(context)
      requireMatchingAccountOrAdmin(context, account)

      const select = getSelectFieldsFromAST(fieldASTs)

      if (hasMembership) {
        const castingsQuery = {
          members: {
            $elemMatch: {
              profile: { $in: profiles },
              status: { $in: ['invited', 'confirmed', 'declined', 'released'] },
            },
          },
        }

        const castings = await Casting.find(castingsQuery).select(
          'booking name location notes dates members',
        )
        const bookingsOfCastingMembership = castings.map(casting => casting.booking)

        /*  Find jobs (bookings) where the profile either has membership on the booking,
            or in one of the castings associated with the booking */
        const jobsQuery = {
          approved: true,
          $or: [
            {
              members: {
                $elemMatch: {
                  profile: { $in: profiles },
                  status: {
                    $in: ['invited', 'confirmed', 'declined', 'released'],
                  },
                },
              },
            },
            { _id: { $in: bookingsOfCastingMembership } },
          ],
        }

        const jobs = await Booking.find(jobsQuery)
          .select(`${select} project members`)
          .populate('project')

        return jobs.map(job => ({
          ...job.toObject(),
          membership: getJobMembership(job, profiles),
          castings: castings
            .filter(casting => job.id.toString() === casting.booking.toString())
            .map(casting => ({
              ...casting.toObject(),
              membership: getCastingMembership(casting, profiles),
            })),
        }))
      }

      const query = { approved: true, visibility: 'public' }

      const jobs = await Booking.find(query)
        .select(`${select} project members`)
        .populate('project')

      return jobs.map(job => ({
        ...job.toObject(),
        membership: getJobMembership(job, profiles),
      }))
    },
  },
}
