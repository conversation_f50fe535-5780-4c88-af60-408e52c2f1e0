import { GraphQLID, GraphQLEnumType, GraphQLNonNull } from 'graphql/type'

import { requireMatchingAccountOrAdmin } from '../permissions'
import { getSessionUserId } from '../utils/session'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { Profile } from '../models/profiles'
import { Booking } from '../models/bookings'
import { Casting } from '../models/castings'
import { JobType, JobCastingType } from './types'

function jobConfirmDeclineResolver(status) {
  return async function jobConfirmDecline(root, { id, membershipId }, context, { fieldASTs }) {
    const account = getSessionUserId(context)
    requireMatchingAccountOrAdmin(context, account)

    const select = getSelectFieldsFromAST(fieldASTs)
    const currentUserProfiles = (await Profile.find({ account }).select('_id')).map(
      profile => profile.id,
    )

    const update = {
      $set: {
        'members.$.status': status,
      },
    }

    const job = await Booking.findOneAndUpdate(
      {
        _id: id,
        members: {
          $elemMatch: {
            _id: membershipId,
            profile: { $in: currentUserProfiles },
            status: { $in: ['invited', 'confirmed', 'declined'] },
          },
        },
      },
        update,
        { new: true },
      )
      .select(`${select} members`)

    if (!job) {
      throw new Error('Invalid job')
    }

    job.membership = job.members.find(member =>
      currentUserProfiles.includes(member.profile.toString()))

    return job
  }
}

function castingConfirmDeclineResolver(status) {
  return async function castingConfirmDecline(
    parent,
    { id, membershipId },
    context,
    { fieldASTs },
  ) {
    const account = getSessionUserId(context)
    requireMatchingAccountOrAdmin(context, account)

    const select = getSelectFieldsFromAST(fieldASTs)
    const currentUserProfiles = (await Profile.find({ account }).select('_id')).map(
      profile => profile.id,
    )

    const update = {
      $set: {
        'members.$.status': status,
      },
    }

    const casting = await Casting.findOneAndUpdate(
      {
        _id: id,
        members: {
          $elemMatch: {
            _id: membershipId,
            profile: { $in: currentUserProfiles },
            status: { $in: ['invited', 'confirmed', 'declined'] },
          },
        },
      },
        update,
        { new: true },
      )
      .select(`${select} members`)

    if (!casting) {
      throw new Error('Invalid casting')
    }

    casting.membership = casting.members.find(member =>
      currentUserProfiles.includes(member.profile.toString()))

    return casting
  }
}

export default {
  confirmCasting: {
    type: JobCastingType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: castingConfirmDeclineResolver('confirmed'),
  },

  declineCasting: {
    type: JobCastingType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: castingConfirmDeclineResolver('declined'),
  },

  confirmJob: {
    type: JobType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: jobConfirmDeclineResolver('confirmed'),
  },

  declineJob: {
    type: JobType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: jobConfirmDeclineResolver('declined'),
  },

  applyForJob: {
    type: JobType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      profileId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { id, profileId }, context, { fieldASTs }) => {
      const account = getSessionUserId(context)
      requireMatchingAccountOrAdmin(context, account)

      const select = getSelectFieldsFromAST(fieldASTs)

      /*  First, try to add the profile to the members set,
          with the assumption it hasn't already been adding. */
      let job = await Booking.findOneAndUpdate(
        {
          _id: id,
          approved: true,
          visibility: 'public',
          'members.profile': { $ne: profileId },
        },
        {
          $addToSet: {
            members: { profile: profileId, created: new Date(), status: 'applied' },
          },
        },
          { new: true },
        )
        .select(`${select} members`)

      /*  previous query didn't work, so perhaps the profile has already been added
          (probably in 'pending' status when added by the representative) */
      if (!job) {
        job = await Booking.findOneAndUpdate(
          {
            _id: id,
            approved: true,
            visibility: 'public',
            members: {
              $elemMatch: {
                profile: profileId,
                status: 'pending',
              },
            },
          },
          {
            $set: {
              'members.$.status': 'applied',
            },
          },
            { new: true },
          )
          .select(`${select} members`)
      }

      if (!job) {
        throw new Error('Invalid job')
      }

      job.membership = job.members.find(member => member.profile.toString() === profileId)

      return job
    },
  },
}
