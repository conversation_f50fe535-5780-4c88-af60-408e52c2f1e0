import {
  GraphQLBoolean,
  GraphQLString,
  GraphQLFloat,
  GraphQLObjectType,
  GraphQLList,
  GraphQLID,
} from 'graphql/type'

import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { requireAuthentication } from '../authentication'
import { BookingDateType } from '../booking/types'
import { CastingDateType } from '../casting/types'
import { RepresentativeType } from '../profile/types'
import { RepresentativeProfile } from '../models/profiles'

export const MembershipType = new GraphQLObjectType({
  name: 'Membership',
  fields: () => ({
    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    status: { type: GraphQLString },
    profile: { type: GraphQLID },
  }),
})

export const JobCastingType = new GraphQLObjectType({
  name: 'JobCasting',
  fields: () => ({
    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    name: { type: GraphQLString },
    location: { type: GraphQLString },
    notes: { type: GraphQLString },

    dates: {
      type: new GraphQLList(CastingDateType),
    },

    membership: {
      type: MembershipType,
    },
  }),
})

export const JobType = new GraphQLObjectType({
  name: 'Job',
  fields: () => ({
    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    approved: { type: GraphQLBoolean },
    visibility: { type: GraphQLString },
    name: { type: GraphQLString },
    location: { type: GraphQLString },
    objectives: { type: new GraphQLList(GraphQLString) },
    rateType: { type: GraphQLString },
    rate: { type: GraphQLFloat },
    currency: { type: GraphQLString },
    buyout: { type: GraphQLFloat },
    notes: { type: GraphQLString },
    travel: { type: GraphQLString },

    dates: {
      type: new GraphQLList(BookingDateType),
    },

    membership: {
      type: MembershipType,
    },

    castings: {
      type: new GraphQLList(JobCastingType),
    },

    // fields from project... should probably be it's own ObjectType but fuck it
    avatar: {
      type: GraphQLID,
      resolve: ({ project }) => project.avatar,
    },
    projectName: {
      type: GraphQLID,
      resolve: ({ project }) => project.name,
    },
    projectNotes: {
      type: GraphQLID,
      resolve: ({ project }) => project.notes,
    },
    type: {
      type: new GraphQLList(GraphQLString),
      resolve: ({ project }) => project.type,
    },
    usage: {
      type: new GraphQLList(GraphQLString),
      resolve: ({ project }) => project.usage,
    },
    otherUsage: {
      type: GraphQLID,
      resolve: ({ project }) => project.otherUsage,
    },
    representative: {
      type: RepresentativeType,
      resolve: ({ project }, args, context, { fieldASTs }) => {
        requireAuthentication(context)

        const select = getSelectFieldsFromAST(fieldASTs)

        return RepresentativeProfile.findById(project.profile).select(select)
      },
    },
  }),
})
