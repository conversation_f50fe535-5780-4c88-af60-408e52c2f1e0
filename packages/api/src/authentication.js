import passport from 'koa-passport'
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt'

import { api as apiConfig, secrets } from '../config'

passport.serializeUser((data, done) => done(null, data))

const jwtExtractor = req => {
  if (req.headers.authorization) {
    return ExtractJwt.fromAuthHeader()(req)
  }

  if (process.env.NODE_ENV === 'development') {
    if (req && req.cookies && req.cookies.get('jwt')) {
      return req.cookies.get('jwt')
    }
  }

  return null
}

passport.use(new JwtStrategy({ ...apiConfig.jwt, secretOrKey: secrets.jwt, jwtFromRequest: jwtExtractor }, (jwtPayload, done) => {
  done(null, jwtPayload)
}))

export async function authenticate() {
  await passport.authenticate('jwt', { session: false })
}

export function isAuthenticated({ req: request }) {
  return !request.isUnauthenticated() && request.user
}

export function requireAuthentication(context) {
  const response = context.response

  if (!isAuthenticated(context)) {
    response.status = 401
    throw new Error('Requires authentication')
  }
}

export function isAdmin({ req: request }) {
  return request.user && request.user.permissions && request.user.permissions.admin === true
}

export function requireAdmin(context) {
  const response = context.response

  if (!isAdmin(context)) {
    response.status = 401
    throw new Error('Requires admin permissions')
  }
}

export function isRepresentative({ req: request }) {
  return request.user && request.user.permissions && request.user.permissions.representative === true
}

export function requireRepresentative(context) {
  const response = context.response

  if (!isRepresentative(context)) {
    response.status = 401
    throw new Error('Requires representative permissions')
  }
}

export function requireAdminOrRepresentative(context) {
  const response = context.response

  if (!(isAdmin(context) || isRepresentative(context))) {
    response.status = 401
    throw new Error('Requires admin or representative permissions')
  }
}

export function getUserId({ req: request }) {
  return request.user && request.user.id
}
