import { GraphQLBoolean, GraphQLID, GraphQLNonNull, GraphQLList } from 'graphql/type'

import { requireRepresentative, requireAdminOrRepresentative } from '../authentication'
import { getSessionUserId } from '../utils/session'
import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { CastingType, CastingMemberStatusType, CastingDateInputType } from './types'
import { Casting } from '../models/castings'
import { ModelProfile } from '../models/profiles'
import sendCastingInviteEmail from '../emails/casting-invite'
import sendCastingReleaseEmail from '../emails/casting-release'
import sendCastingCancellationEmail from '../emails/casting-cancellation'
import sendCastingDetailsChangeEmail from '../emails/casting-details-change'

const modelFields = modelToGraphQLFields(Casting)

delete modelFields.id
delete modelFields._id
delete modelFields.created
delete modelFields.updated
delete modelFields.project
delete modelFields.booking
delete modelFields.account

export default {
  createCasting: {
    type: CastingType,
    args: {
      ...modelFields,
      project: { type: new GraphQLNonNull(GraphQLID) },
      booking: { type: new GraphQLNonNull(GraphQLID) },
      dates: { type: new GraphQLList(CastingDateInputType) },
    },
    resolve: (root, data, context) => {
      requireRepresentative(context)
      const account = getSessionUserId(context)

      return Casting.create({ ...data, account })
    },
  },

  updateCasting: {
    type: CastingType,
    args: {
      ...modelFields,
      id: { type: new GraphQLNonNull(GraphQLID) },
      booking: { type: GraphQLID },
      dates: { type: new GraphQLList(CastingDateInputType) },
    },
    resolve: async (root, { id, ...updates }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)

      if (updates.dates || updates.location || updates.notes) {
        const casting = await Casting.findOne({ _id: id, account }).populate(
          'project booking members.profile',
        )

        casting.members.forEach(async (membership) => {
          if (membership.status === 'confirmed') {
            await sendCastingDetailsChangeEmail(casting, membership, updates)
          }
        })
      }

      return Casting.findOneAndUpdate({ _id: id, account }, updates, { new: true })
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },

  cancelCasting: {
    type: GraphQLBoolean,
    args: {
      castingId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { castingId }, context) => {
      requireRepresentative(context)
      const account = getSessionUserId(context)

      const casting = await Casting.findOne({
        _id: castingId,
        account,
      }).populate('project members.profile')

      if (!casting) {
        throw new Error('Not a valid casting.')
      }

      casting.members.forEach(async (membership) => {
        await sendCastingCancellationEmail(casting, membership)
      })

      return !!await casting.remove()
    },
  },

  addMemberToCasting: {
    type: CastingType,
    args: {
      castingId: { type: new GraphQLNonNull(GraphQLID) },
      memberId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { castingId, memberId }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)
      const isMemberQuery = {
        _id: castingId,
        'members.profile': { $in: [memberId] },
      }

      const [castingExists, profileExists, isAlreadyMember] = await Promise.all([
        Casting.find({ _id: castingId, account }).select('_id').then(casting => !!casting.length),
        ModelProfile.findById(memberId).select('_id').then(profile => !!profile),
        Casting.find(isMemberQuery).select('_id').then(casting => !!casting.length),
      ])

      if (!castingExists) {
        throw new Error('Casting does not exist.')
      }

      if (!profileExists) {
        throw new Error('Member profile does not exists.')
      }

      if (isAlreadyMember) {
        throw new Error('Talent already added to this casting.')
      }

      const update = {
        $addToSet: {
          members: { profile: memberId, added: new Date(), status: 'pending' },
        },
      }

      return Casting.findByIdAndUpdate(castingId, update, { new: true })
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },

  removeCastingMembership: {
    type: CastingType,
    args: {
      castingId: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { castingId, membershipId }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)

      const query = {
        _id: castingId,
        account,
        'members._id': membershipId,
      }

      const update = {
        $pull: {
          members: { _id: membershipId },
        },
      }

      return Casting.findOneAndUpdate(query, update, { new: true })
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },

  updateCastingMembership: {
    type: CastingType,
    args: {
      castingId: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
      status: { type: new GraphQLNonNull(CastingMemberStatusType) },
    },
    resolve: async (
      root,
      { castingId, membershipId, status: newStatus },
      context,
      { fieldASTs },
    ) => {
      requireAdminOrRepresentative(context)

      if (['confirmed', 'declined'].includes(newStatus)) {
        throw new Error(`Only talent may set status to ${newStatus}`)
      }

      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)

      const casting = await Casting.findOne({
        _id: castingId,
        account,
        'members._id': membershipId,
      })
        .select('_id booking members')
        .populate('booking', 'approved')

      if (!casting) {
        throw new Error('Not a valid casting.')
      }

      const membership = casting.members.id(membershipId)

      if (!membership) {
        throw new Error('Not a valid membership.')
      }

      const oldStatus = membership.status

      if (oldStatus === newStatus) {
        throw new Error(`Already ${newStatus}`)
      }

      if (newStatus === 'invited') {
        if (['invited', 'confirmed', 'declined'].includes(oldStatus)) {
          throw new Error(`Cannot invite someone who is already ${oldStatus}`)
        }
        if (!casting.booking.approved) {
          throw new Error('Cannot invite talent to a casting with an unapproved booking.')
        }
      }

      const update = {
        $set: {
          'members.$.status': newStatus,
        },
      }

      const updateResult = await Casting.findOneAndUpdate(
        {
          _id: castingId,
          account,
          'members._id': membershipId,
        },
        update,
        { new: true },
      )
        .select(`${select} name project booking members`)
        .populate('project booking members.profile')

      if (updateResult) {
        const membershipData = updateResult.members.id(membershipId)

        if (newStatus === 'invited') await sendCastingInviteEmail(updateResult, membershipData)
        if (newStatus === 'released' && ['invited', 'confirmed'].includes(oldStatus)) {
          await sendCastingReleaseEmail(updateResult, membershipData)
        }
      }

      return updateResult
    },
  },
}
