import {
  GraphQLObjectType,
  GraphQLID,
  GraphQLString,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLList,
  GraphQLEnumType,
  GraphQLInputObjectType,
} from 'graphql/type'

import { isValidObjectId } from '../utils/mongoose'
import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import { Casting } from '../models/castings'
import { Booking } from '../models/bookings'
import Project from '../models/projects'
import { ProfileType } from '../profile/types'
import { ProjectType } from '../project/types'
import { BookingType } from '../booking/types'
import { requireAdminOrRepresentative } from '../authentication'
import { selectionContainsOnlyId } from '../utils/graphql'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'

const modelFields = modelToGraphQLFields(Casting, false, true)

export const CastingMemberStatusType = new GraphQLEnumType({
  name: 'CastingMemberStatus',
  values: {
    pending: { value: 'pending' },
    invited: { value: 'invited' },
    confirmed: { value: 'confirmed' },
    declined: { value: 'declined' },
    released: { value: 'released' },
  },
})

export const CastingMemberType = new GraphQLObjectType({
  name: 'CastingMember',
  fields: () => ({
    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },
    added: {
      type: GraphQLString,
    },
    updated: {
      type: GraphQLString,
    },
    status: {
      type: CastingMemberStatusType,
    },
    profile: {
      type: ProfileType,
    },
  }),
})

export const CastingDateType = new GraphQLObjectType({
  name: 'CastingDate',
  fields: () => ({
    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },
    start: {
      type: GraphQLString,
    },
    end: {
      type: GraphQLString,
    },
  }),
})

export const CastingDateInputType = new GraphQLInputObjectType({
  name: 'CastingDateInput',
  fields: {
    start: {
      type: GraphQLString,
    },
    end: {
      type: GraphQLString,
    },
  },
})

export const CastingType = new GraphQLObjectType({
  name: 'Casting',
  fields: () => ({
    ...modelFields,

    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    dates: {
      type: new GraphQLList(CastingDateType),
    },

    approved: {
      type: GraphQLBoolean,
      resolve: async ({ booking }) => {
        if (!isValidObjectId(booking)) return booking.approved
        console.log('hahahahahah', booking)

        const { approved } = await Booking.findById(booking).select('approved')

        return approved
      },
    },

    members: {
      type: new GraphQLList(CastingMemberType),
      resolve: ({ members }, args, context) => {
        requireAdminOrRepresentative(context)

        return members
      },
    },

    project: {
      type: ProjectType,
      resolve: ({ project }, args, context, { fieldASTs }) => {
        if (!isValidObjectId(project)) return project
        if (selectionContainsOnlyId(fieldASTs)) return { _id: project }

        const select = getSelectFieldsFromAST(fieldASTs)

        return Project.findById(project).select(select)
      },
    },

    booking: {
      type: BookingType,
      resolve: ({ booking }, args, context, { fieldASTs }) => {
        if (!isValidObjectId(booking)) return booking
        if (selectionContainsOnlyId(fieldASTs)) return { _id: booking }

        const select = getSelectFieldsFromAST(fieldASTs)
        return Booking.findById(booking).select(select)
      },
    },
  }),
})
