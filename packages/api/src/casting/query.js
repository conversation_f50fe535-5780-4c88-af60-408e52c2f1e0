import { <PERSON>raph<PERSON>ID, GraphQLString, GraphQLList } from 'graphql/type'

import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { getSessionUserId } from '../utils/session'

import { CastingType } from './types'
import { Profile } from '../models/profiles'
import { Casting } from '../models/castings'
import { requireAdminOrRepresentative, isAdmin, isRepresentative } from '../authentication'

const modelFields = modelToGraphQLFields(Casting, true)

delete modelFields.id
delete modelFields._id
delete modelFields.account

export default {
  casting: {
    type: CastingType,
    args: {
      id: { type: GraphQLID },
    },
    resolve: async (root, { id }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)
      const account = getSessionUserId(context)
      const select = getSelectFieldsFromAST(fieldASTs)

      return Casting.findOne({ id, account })
        .select(`${select} project booking members`)
        .populate('project booking members.profile')
    },
  },

  castings: {
    type: new GraphQLList(CastingType),
    args: {
      ...modelFields,
    },
    resolve: (root, args, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const select = getSelectFieldsFromAST(fieldASTs)
      const account = getSessionUserId(context)
      const query = { ...args, account }

      return Casting.find(query)
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },
}
