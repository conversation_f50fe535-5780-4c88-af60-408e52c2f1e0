import AWS from 'aws-sdk'

import { aws } from '../config'

const SES = new AWS.SES(aws.ses)

function send(params) {
  return new Promise((resolve, reject) => SES.sendEmail(params, (err, data) => {
    if (err) return reject(err)

    resolve(data)
  }))
}

export default async function ses(source, to, replyTo, subject, text, html) {
  const params = {
    ...aws.ses.params,
    Destination: {
      ToAddresses: to,
    },
    Message: {
      Body: { },
      Subject: {
        Data: subject,
        Charset: 'UTF-8',
      },
    },
    Source: source || aws.ses.params.Source,
    ReplyToAddresses: replyTo || undefined,
  }

  if (html) {
    params.Message.Body.Html = {
      Data: html,
      Charset: 'UTF-8',
    }
  }

  if (text) {
    params.Message.Body.Text = {
      Data: text,
      Charset: 'UTF-8',
    }
  }

  return await send(params)
}
