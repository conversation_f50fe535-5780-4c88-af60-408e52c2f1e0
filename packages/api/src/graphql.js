import { GraphQLSchema, GraphQLObjectType } from 'graphql/type'

import accountQueryFields from './account/query'
import accountMutationFields from './account/mutation'
import profileQueryFields from './profile/query'
import profileMutationFields from './profile/mutation'
import projectQueryFields from './project/query'
import projectMutationFields from './project/mutation'
import tokenMutationFields from './token/mutation'
import jobQueryFields from './job/query'
import jobMutationFields from './job/mutation'
import feedQueryFields from './feed/query'
import castingQueryFields from './casting/query'
import castingMutationFields from './casting/mutation'
import bookingQueryFields from './booking/query'
import bookingMutationFields from './booking/mutation'
import adminQueryFields from './admin/query'
import adminMutationFields from './admin/mutation'

const queryFields = [
  accountQueryFields,
  profileQueryFields,
  projectQueryFields,
  jobQueryFields,
  feedQueryFields,
  castingQueryFields,
  bookingQueryFields,
  adminQueryFields,
]

const mutationFields = [
  accountMutationFields,
  profileMutationFields,
  projectMutationFields,
  jobMutationFields,
  tokenMutationFields,
  castingMutationFields,
  bookingMutationFields,
  adminMutationFields,
]

export default new GraphQLSchema({
  query: new GraphQLObjectType({
    name: 'Query',
    fields: Object.assign(...queryFields),
  }),
  mutation: new GraphQLObjectType({
    name: 'Mutation',
    fields: Object.assign(...mutationFields),
  }),
})
