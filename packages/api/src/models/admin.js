import mongoose, { Schema } from 'mongoose'
import { boolean, string, date } from '../utils/modelTypes'

const adminSchema = new Schema({
  created: date('Date admin was created', { default: Date.now }),
  updated: date('Date admin was last updated'),

  // Basic admin info
  name: string('Full name of the admin', { required: true }),
  email: string('Email address', { required: true, unique: true }),
  password: string('Hashed password', { required: true }),

  // Admin role and permissions
  role: string('Admin role', {
    enum: ['super_admin', 'admin', 'moderator'],
    default: 'admin'
  }),
  isActive: boolean('Whether the admin account is active', { default: true }),

  // Associated projects
  projects: [{
    type: Schema.Types.ObjectId,
    ref: 'Project'
  }],

  // Additional fields
  avatar: string('Profile picture URL'),
  phone: string('Contact phone number'),
  lastLogin: date('Last login timestamp'),

  // Permissions
  permissions: {
    canManageProjects: boolean('Can manage projects', { default: true }),
    canManageUsers: boolean('Can manage users', { default: false }),
    canManageAdmins: boolean('Can manage other admins', { default: false })
  }
}, { autoIndex: true })

// Pre-save middleware to update the 'updated' field
adminSchema.pre('save', function preSave(next) {
  this.updated = new Date()
  next()
})

export default mongoose.model('Admin', adminSchema, 'admins')
