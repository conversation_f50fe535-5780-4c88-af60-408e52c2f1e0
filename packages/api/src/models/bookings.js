import mongoose, { Schema } from 'mongoose'

import { boolean, number, string, date } from '../utils/modelTypes'

const datesSchema = new Schema({
  start: date('', { default: null }),
  end: date('', { default: null }),
})

const memberSchema = new Schema({
  status: string('', { default: 'pending', index: true }),
  added: date('Date item was added', { default: Date.now }),
  updated: date('Date item was last modified'),
  profile: {
    type: Schema.Types.ObjectId,
    ref: 'ModelProfile',
    required: true,
  },
})

memberSchema.pre('save', function preSave(next) {
  this.updated = new Date()
  next()
})

const baseFields = {
  created: date('Date project was created'),
  updated: date('Date project was last updated'),
  account: {
    description: 'Id of account (user) this booking was created by',
    type: Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
  },
  project: {
    description: 'Id of project this casting was created for',
    type: Schema.Types.ObjectId,
    ref: 'Project',
    required: true,
  },
  name: string('The full name of the project', { index: true }),
  approved: boolean('', { index: true, default: false }),
  visibility: string('', { index: true, default: 'public' }),

  location: string('', { index: true }),
  notes: string(''),
  objectives: [string('')],
  seats: number('', { default: 1, min: 1 }),
  rateType: string('', { default: 'day' }),
  rate: number('', { min: 0 }),
  currency: string('', { default: 'usd' }),
  buyout: number('', { min: 0 }),
  travel: string('', { default: 'talent' }),

  dates: [datesSchema],
  members: [memberSchema],
}

const schema = new Schema({ ...baseFields }, { autoIndex: true })

schema.pre('save', function preSave(next) {
  this.updated = new Date()
  next()
})

export const Booking = mongoose.model('Booking', schema, 'bookings')
