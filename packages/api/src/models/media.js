import fs from 'fs'
import mongoose, { Schema } from 'mongoose'
import AWS from 'aws-sdk'

import { aws } from '../../config'
import { string, date } from '../utils/modelTypes'

const s3bucket = new AWS.S3({ params: { Bucket: aws.s3.bucket } })

const schema = {
  created: date('Date media was created'),
  updated: date('Date media was last updated'),
  /* account: {
    type: Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
  },*/
  title: string(''),
  description: string(''),
  useAs: string(''),
}

const mediaSchema = new Schema(schema)

mediaSchema.pre('save', function preSave(next) {
  this.updated = new Date
  next()
})

mediaSchema.methods.s3Key = function s3Key(size = 'original') {
  return `media-data/${this.id}/${size}`
}

const Media = mongoose.model('Media', mediaSchema)


async function readFile(path) {
  return await new Promise((resolve, reject) => {
    fs.readFile(path, (error, data) => {
      if (error) {
        return reject(error)
      }

      return resolve(data)
    })
  })
}

async function putObject(data, params) {
  const s3Params = {
    ...params,
    Body: data,
  }

  return await new Promise((resolve, reject) => {
    s3bucket.upload(s3Params, (error, response) => {
      if (error) {
        return reject(error)
      }

      return resolve(response)
    })
  })
}

async function deleteMediaObjects(media) {
  const s3Params = {
    Delete: {
      Objects: [
        { Key: media.s3Key() },
        // { Key: media.s3Key('thumbnail') },
      ],
    },
  }

  return await new Promise((resolve, reject) => {
    s3bucket.deleteObjects(s3Params, (error, response) => {
      if (error) {
        return reject(error)
      }

      return resolve(response)
    })
  })
}

async function removeMedia(id) {
  const mediaDoc = await Media.findById(id)

  if (mediaDoc) {
    await deleteMediaObjects(mediaDoc)
    return await Media.findByIdAndRemove(id).exec()
  }

  return false
}

export async function createMedia(doc = {}, localFile, previousId = false) {
  const mediaDoc = await Media.create(doc)
  const fileData = await readFile(localFile)
  const upload = await putObject(fileData, { Key: mediaDoc.s3Key() })

  // TODO: if can't put object, rollback and remove mediDoc

  if (previousId && upload) {
    await removeMedia(previousId)
  }

  return mediaDoc
}

export const deleteMedia = removeMedia
export default Media
