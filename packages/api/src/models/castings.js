import mongoose, { Schema } from 'mongoose'

import { boolean, number, string, date } from '../utils/modelTypes'

const datesSchema = new Schema({
  start: date('', { default: null }),
  end: date('', { default: null }),
})

const memberSchema = new Schema({
  status: string('', { default: 'pending', index: true }),
  added: date('Date item was added', { default: Date.now }),
  updated: date('Date item was last modified'),
  profile: {
    type: Schema.Types.ObjectId,
    ref: 'ModelProfile',
    required: true,
  },
})

memberSchema.pre('save', function preSave(next) {
  this.updated = new Date()
  next()
})

const baseFields = {
  created: date('Date project was created'),
  updated: date('Date project was last updated'),
  account: {
    description: 'Id of account (user) this casting was created by',
    type: Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
  },
  project: {
    description: 'Id of project this casting was created for',
    type: Schema.Types.ObjectId,
    ref: 'Project',
    required: true,
  },
  booking: {
    description: 'Id of booking this casting is associated with',
    type: Schema.Types.ObjectId,
    ref: 'Booking',
    required: true,
  },
  name: string('The full name of the project', { index: true }),
  location: string('', { index: true }),
  notes: string(''),

  dates: [datesSchema],
  members: [memberSchema],
}

const schema = new Schema({ ...baseFields }, { autoIndex: true })

schema.pre('save', function preSave(next) {
  this.updated = new Date()
  next()
})

export const Casting = mongoose.model('Casting', schema, 'castings')
