import mongoose, { Schema } from 'mongoose'

import { boolean, string, date } from '../utils/modelTypes'

const baseFields = {
  created: date('Date project was created', { default: Date.now }),
  updated: date('Date project was last updated'),
  company: {
    description: 'Id of company this project belongs to',
    type: Schema.Types.ObjectId,
    ref: 'Company',
    // required: true,
  },
  account: {
    description: 'Id of account (user) this project was created by',
    type: Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
  },
  profile: {
    description: 'Id of profile this project was created for',
    type: Schema.Types.ObjectId,
    ref: 'Profile',
    required: true,
  },
  approved: boolean('', { index: true, default: false }),
  avatar: string(''),
  name: string('The full name of the project', { index: true }),

  type: [string('')],
  usage: [string('')],
  otherUsage: string(''),
  notes: string(''),
}

const schema = new Schema(baseFields, { autoIndex: true })

schema.pre('save', function preSave(next) {
  this.updated = new Date()
  next()
})

export default mongoose.model('Project', schema, 'projects')
