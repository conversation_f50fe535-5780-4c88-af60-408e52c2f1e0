import mongoose, { Schema } from 'mongoose'

import { api as apiConfig } from '../../config'
import { string, date } from '../utils/modelTypes'

const baseFields = {
  created: date('Date token was created'),
  updated: date('Date token was last updated'),
  token: string('The token', { index: true, required: true }),
  account: {
    description: 'Id of account this token belongs to',
    type: Schema.Types.ObjectId,
    ref: 'Accounts',
    required: true,
  },
  _timestamp: { type: Date, expires: apiConfig.jwt.expiresIn, default: () => Date.now() },

}

const schema = new Schema({ ...baseFields }, { autoIndex: true })

schema.pre('save', function preSave(next) {
  this.updated = new Date
  next()
})

const Tokens = mongoose.model('Tokens', schema, 'tokens')
export default Tokens
