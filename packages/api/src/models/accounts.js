import mongoose, { Schema } from 'mongoose'
import hmacSha512 from 'crypto-js/hmac-sha512'
import sha512 from 'crypto-js/sha512'

import { secrets } from '../../config'
import { string, date } from '../utils/modelTypes'

const baseFields = {
  created: date('Date account was created'),
  updated: date('Date account was last updated'),

  email: string('Account email', { unique: true }),
  passPhrase: string('Account pass phrase', { index: true, select: false }),
  permissions: { type: Schema.Types.Mixed },
  firstName: string('Account first name', { index: true }),
  lastName: string('Account last name', { index: true }),

}

const schema = new Schema({ ...baseFields }, { autoIndex: true })

schema.pre('save', function preSave(next) {
  this.updated = new Date
  next()
})

const Accounts = mongoose.model('Account', schema, 'accounts')
export default Accounts

Accounts.findOne({ email: '<EMAIL>' }, (err, account) => {
  if (!account) {
    console.log('Admin user did not exist creating admin user...')
    const adminAccount = new Accounts({
      email: '<EMAIL>',
      passPhrase: hmacSha512(sha512('1234').toString(), secrets.passphrase).toString(),
      permissions: {
        admin: true,
        representative: true,
      },
    })
    adminAccount.save()
  }
})
