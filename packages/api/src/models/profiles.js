import mongoose, { Schema } from 'mongoose'
import slug from 'slug'
import { boolean, number, string, date } from '../utils/modelTypes'

slug.defaults.mode = 'rfc3986'

const baseFields = {
  created: date('Date profile was created'),
  updated: date('Date profile was last updated'),
  account: {
    description: 'Id of account this profile belongs to',
    type: Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
  },
  name: string('The full display name of the profile', { index: true }),
  slug: string('The profile\'s slug', { unique: true }),
  approved: boolean('', { index: true, default: false }),
  type: { type: String, default: 'model', index: true },
  avatar: string(''),
  cover: string(''),
  media: [
    {
      type: Schema.Types.ObjectId,
      ref: 'Media',
    },
  ],
}

const modelFields = {
  compCardCover: string(''),
  compCardLabelPosition: string('', { default: 'middle' }),
  compCardA: string(''),
  compCardB: string(''),
  compCardC: string(''),
  compCardD: string(''),
  country: string('', { index: true }),
  city: string('', { index: true }),
  gender: string('', { index: true }),
  birthYear: number('', { index: true }),
  height: number('', { index: true }),
  waist: number('', { index: true }),
  inseam: number('', { index: true }),
  suit: number('', { index: true }),
  hips: number('', { index: true }),
  bust: number('', { index: true }),
  dressSize: number('', { index: true }),
  chest: number('', { index: true }),
  cup: string('', { index: true }),
  eyeColor: string('', { index: true }),
  shoeSize: number('', { index: true }),
  hairColor: string('', { index: true }),
  hairLength: string('', { index: true }),
  bodyType: string('', { index: true }),
  piercings: boolean('', { index: true }),
  tattoos: boolean('', { index: true }),
  nationality: string(''),
  background: string(''),
  travelOk: boolean('', { index: true, default: false }),
  ethnicity: string('', { index: true }),
}

const representativeFields = {
  companyName: string(''),
  fullAddress: string(''),
  jobPosition: string(''),
  jobPositionSinceDate: date(''),
  bio: string(''),
}

const modelProfileSchema = new Schema({ ...baseFields, ...modelFields, ...representativeFields }, { autoIndex: true })

function preSave(next) {
  this.updated = new Date

  if (!this.slug || !this.slug.length) {
    if (this.name) {
      this.slug = slug(this.name)
    }
  } /* else {
    this.slug = slug(this.slug)
  }*/


  next()
}

modelProfileSchema.pre('save', preSave)

const ProfileModel = mongoose.model('Profile', modelProfileSchema, 'profiles')
const TalentModel = mongoose.model('ModelProfile', modelProfileSchema, 'profiles')
const RepresentativeModel = mongoose.model('RepresentativeProfile', modelProfileSchema, 'profiles')

export const Profile = ProfileModel
export const ModelProfile = TalentModel
export const RepresentativeProfile = RepresentativeModel
