const makeType = (type, description = '', options = {}) => ({
  description,
  type,
  ...options,
})

export const boolean = (description, options) => makeType(Boolean, description, options)

export const number = (description, options) => makeType(Number, description, options)

export const string = (description, options) =>
  makeType(String, description, { trim: true, ...options })

export const date = (description, options) =>
  makeType(Date, description, { default: Date.now, ...options })
