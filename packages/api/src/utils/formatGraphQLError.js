import { v4 as uuidv4 } from 'uuid'
import { isApiError } from './apiError'

export default function formatGraphQLError(_error) {
  const error = _error

  if (
    !error.originalError ||
    error.message.match('Syntax Error') ||
    error.name === 'BadRequestError' ||
    error.originalError && error.originalError.name === 'Error' ||
    error[isApiError]
  ) {
    return {
      message: error.message,
      locations: error.locations,
    }
  }

  const errorId = uuidv4()

  console.error(
    `\n\nCaught an error (this is non-fatal). Error reference ID ${errorId}:\n`,
    error && error.stack || error,
    '\n',
  )

  return {
    message: `Internal Error: ${errorId}`,
    locations: error.locations,
  }
}
