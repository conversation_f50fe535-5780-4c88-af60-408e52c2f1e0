export function getFieldSelection(fieldASTs) {
  const selections = fieldASTs[0].selectionSet.selections
  const select = selections.map(field => field.kind === 'Field' ? field.name.value : field.kind)

  return select
}

export function selectionContains(field, fieldASTs) {
  const selection = getFieldSelection(fieldASTs)

  return !!selection.find(item => field === item)
}

export function selectionContainsOnly(field, fieldASTs) {
  const selection = getFieldSelection(fieldASTs)

  return selection.length === 1 && selection[0] === field
}

export function selectionContainsOnlyId(fieldASTs) {
  return selectionContainsOnly('id', fieldASTs)
}
