import {
  GraphQLBoolean,
  GraphQLFloat,
  GraphQLID,
  GraphQLString,
  GraphQLNonNull,
  GraphQLList,
  GraphQLObjectType,
} from 'graphql/type'

function schemaNonNullType(type, required) {
  return required ? new GraphQLNonNull(type) : type
}

function schemaTypeToGraphQLScalarType({ type, required = false }) {
  if (type.toString().search(/ObjectId/) > -1) {
    return schemaNonNullType(GraphQLID, required)
  }

  switch (typeof type()) {
    case 'boolean':
      return schemaNonNullType(GraphQLBoolean, required)
    case 'number':
      return schemaNonNullType(GraphQLFloat, required)
    case 'string':
    default:
      return schemaNonNullType(GraphQLString, required)
  }
}

export default function modelToGraphQLFields(model, indexedOnly = false, includeSubDocs = false) {
  const fields = {}
  const { tree } = model.schema || model

  Object.keys(tree).forEach((key) => {
    const field = tree[key]

    if (
      indexedOnly && (field.index || field.unique || field.text || field.sparse) || !indexedOnly
    ) {
      if (!Array.isArray(field) && key !== 'id' && key !== '__v' && key !== '_id') {
        // TODO: this will break on any other virtual types
        const description = field.description
        const type = schemaTypeToGraphQLScalarType(field)

        fields[key] = {
          type,
          description,
        }
      } else if (Array.isArray(field)) {
        if (field[0].tree) {
          if (includeSubDocs) {
            const fieldType = new GraphQLObjectType({
              name: `${model.modelName}${key}Type`,
              fields: {
                ...modelToGraphQLFields(field[0], indexedOnly),

                id: {
                  type: GraphQLID,
                  resolve: ({ _id }) => _id,
                },
              },
            })
            fields[key] = {
              type: new GraphQLList(fieldType),
            }
          }
        } else {
          const description = field.description
          const type = new GraphQLList(schemaTypeToGraphQLScalarType(field[0]))

          fields[key] = {
            type,
            description,
          }
        }
      }
    }
  })

  return fields
}
