import { api } from '../../config'
import mail from '../ses'
import Account from '../models/accounts'

function acceptDeclineLink(bookingId, castingId) {
  return `${api.url}#/job/${bookingId}/casting/${castingId}`
}

function htmlMail(casting, { profile: { name } }) {
  const link = acceptDeclineLink(casting.booking.id, casting.id)

  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        You've been invited to particpate in the job for ${casting.project.name} casting ${casting.name}.
        <br/>
        To review the invitation and accept or decline, visit the following URL:
        <a href="${link}">${link}</a>
    </body>
  </html>`
}

function textMail(casting, { profile: { name } }) {
  return `Hi ${name},
You've been invited to particpate in the job for ${casting.project.name} casting ${casting.name}.
To review the invitation and accept or decline, visit the following URL:
${acceptDeclineLink(casting.booking.id, casting.id)}
  `
}

export default (async function castingInvite(casting, membership) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Job invitation to ${casting.project.name} casting ${casting.name}`
  const text = textMail(casting, membership)
  const html = htmlMail(casting, membership)

  const result = await mail(source, to, replyTo, subject, text, html)

  console.log(result)
});
