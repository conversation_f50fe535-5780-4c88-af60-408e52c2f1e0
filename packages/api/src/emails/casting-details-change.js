import { api } from '../../config'
import mail from '../ses'
import Account from '../models/accounts'

function getLink(bookingId, castingId) {
  return `${api.url}#/job/${bookingId}/casting/${castingId}`
}

function htmlMail(casting, { profile: { name } }, updates) {
  const link = getLink(casting.booking.id, casting.id)

  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        This email is to let you know that the details (${Object.keys(updates).join(', ')}) for the job ${casting.project.name} casting ${casting.name} have changed.
        <br/>
        To review the changes, visit the casting details page here:
        <a href="${link}">${link}</a>
    </body>
  </html>`
}

function textMail(casting, { profile: { name } }, updates) {
  return `Hi ${name},
This email is to let you know that the details (${Object.keys(updates).join(', ')}) for the job ${casting.project.name} casting ${casting.name} have changed.
To review the changes, visit the casting details page here:
${getLink(casting.booking.id, casting.id)}
  `
}

export default (async function castingDetailsChange(casting, membership, updates) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Details Changed: ${casting.project.name} casting ${casting.name}`
  const text = textMail(casting, membership, updates)
  const html = htmlMail(casting, membership, updates)

  const result = await mail(source, to, replyTo, subject, text, html)

  console.log(result)
});
