import { api } from '../../config'
import mail from '../ses'
import Account from '../models/accounts'

function getLink(bookingId) {
  return `${api.url}#/job/${bookingId}`
}

function htmlMail(booking, { profile: { name } }, updates) {
  const link = getLink(booking.id)

  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        This email is to let you know that the details (${Object.keys(updates).join(', ')}) for the job ${booking.project.name}: ${booking.name} have changed.
        <br/>
        To review the changes, visit the job details page here:
        <a href="${link}">${link}</a>
    </body>
  </html>`
}

function textMail(booking, { profile: { name } }, updates) {
  return `Hi ${name},
This email is to let you know that the details (${Object.keys(updates).join(', ')}) for the job ${booking.project.name}: ${booking.name} have changed.
To review the changes, visit the job details page here:
${getLink(booking.id)}
  `
}

export default (async function bookingInvite(booking, membership, updates) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Details Changed: ${booking.project.name} job ${booking.name}`
  const text = textMail(booking, membership, updates)
  const html = htmlMail(booking, membership, updates)

  const result = await mail(source, to, replyTo, subject, text, html)

  console.log(result)
});
