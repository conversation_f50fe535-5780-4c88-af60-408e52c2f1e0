import mail from '../ses'
import Account from '../models/accounts'

function htmlMail(booking, { name }) {
  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        We regret to inform you that the ${booking.project.name} job ${booking.name} has been cancelled.
    </body>
  </html>`
}

function textMail(booking, { name }) {
  return `Hi ${name},
We regret to inform you that the ${booking.project.name} job ${booking.name} has been cancelled.
  `
}

export default (async function bookingCancellation(booking, membership) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Job Cancellation: ${booking.project.name} job ${booking.name}`
  const text = textMail(booking, profile)
  const html = htmlMail(booking, profile)

  const result = await mail(source, to, replyTo, subject, text, html)

  console.log(result)
});
