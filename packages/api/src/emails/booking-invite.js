import { api } from '../../config'
import mail from '../ses'
import Account from '../models/accounts'

function acceptDeclineLink(bookingId) {
  return `${api.url}#/job/${bookingId}`
}

function htmlMail(booking, { profile: { name } }) {
  const link = acceptDeclineLink(booking.id)

  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        You've been invited to particpate in the job for ${booking.project.name} booking ${booking.name}.
        <br/>
        To review the invitation and accept or decline, visit the following URL:
        <a href="${link}">${link}</a>
    </body>
  </html>`
}

function textMail(booking, { profile: { name } }) {
  return `Hi ${name},
You've been invited to particpate in the job for ${booking.project.name} booking ${booking.name}.
To review the invitation and accept or decline, visit the following URL:
${acceptDeclineLink(booking.id)}
  `
}

export default (async function bookingInvite(booking, membership) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Job invitation to ${booking.project.name} job ${booking.name}`
  const text = textMail(booking, membership)
  const html = htmlMail(booking, membership)

  const result = await mail(source, to, replyTo, subject, text, html)

  console.log(result)
});
