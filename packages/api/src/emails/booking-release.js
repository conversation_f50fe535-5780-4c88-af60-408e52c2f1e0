import mail from '../ses'
import Account from '../models/accounts'

function htmlMail(booking, { name }) {
  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        You've been released from your participation in the job for ${booking.project.name} booking ${booking.name}.
    </body>
  </html>`
}

function textMail(booking, { name }) {
  return `Hi ${name},
You've been released from your participation in the job for ${booking.project.name} booking ${booking.name}.
  `
}

export default (async function bookingRelease(booking, membership) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Released from ${booking.project.name} job ${booking.name}`
  const text = textMail(booking, profile)
  const html = htmlMail(booking, profile)

  const result = await mail(source, to, replyTo, subject, text, html)

  console.log(result)
});
