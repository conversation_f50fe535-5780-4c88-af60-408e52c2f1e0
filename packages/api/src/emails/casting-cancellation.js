import mail from '../ses'
import Account from '../models/accounts'

function htmlMail(casting, { name }) {
  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        We regret to inform you that the ${casting.project.name} casting ${casting.name} has been cancelled.
    </body>
  </html>`
}

function textMail(casting, { name }) {
  return `Hi ${name},
We regret to inform you that the ${casting.project.name} casting ${casting.name} has been cancelled.
  `
}

export default (async function castingCancellation(casting, membership) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Casting Cancellation: ${casting.project.name} casting ${casting.name}`
  const text = textMail(casting, profile)
  const html = htmlMail(casting, profile)

  await mail(source, to, replyTo, subject, text, html)
});
