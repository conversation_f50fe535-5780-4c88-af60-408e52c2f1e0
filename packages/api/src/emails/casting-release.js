import mail from '../ses'
import Account from '../models/accounts'

function htmlMail(casting, { name }) {
  return `<html>
    <body>
      <p>Hi ${name},</p>
      <p>
        You've been released from your participation in the casting for ${casting.project.name} casting ${casting.name}.
    </body>
  </html>`
}

function textMail(casting, { name }) {
  return `Hi ${name},
You've been released from your participation in the casting for ${casting.project.name} casting ${casting.name}.
  `
}

export default (async function castingRelease(casting, membership) {
  const profile = membership.profile
  const account = await Account.findById(profile.account)

  const source = undefined
  const to = [account.email]
  const replyTo = undefined
  const subject = `Released from ${casting.project.name} casting ${casting.name}`
  const text = textMail(casting, profile)
  const html = htmlMail(casting, profile)

  const result = await mail(source, to, replyTo, subject, text, html)

  console.log(result)
});
