import os from 'os'
import Ko<PERSON> from 'koa'
import mount from 'koa-mount'
import cors from 'kcors'
import etag from 'koa-etag'
import conditional from 'koa-conditional-get'
import helmet from 'koa-helmet'
import graphqlHTTP from 'koa-graphql'
import mongoose from 'mongoose'
import multer from 'multer'
import koa<PERSON><PERSON> from 'koa-multer'
import passport from 'koa-passport'

import graphQLSchema from './graphql'
import { api as apiConfig } from '../config'
import './authentication'
import formatGraphQLError from './utils/formatGraphQLError'

mongoose.Promise = global.Promise
mongoose.connect('mongodb://localhost/lookbook')

const app = new Koa()

app.use(cors(apiConfig.cors))

app.use(function* logger(next) {
  const start = new Date()

  yield next

  const ms = new Date() - start

  console.log('%s %s - %sms', this.method, this.url, ms)
})

app.use(passport.initialize())

const storage = multer.diskStorage({
  destination(req, file, cb) {
    cb(null, os.tmpdir())
  },

  filename(req, file, cb) {
    cb(null, file.originalname)
  },
})
const multerMiddleware = koaMulter({
  storage,
}).fields([
  { name: 'cover', maxCount: 1 },
  { name: 'avatar', maxCount: 1 },
  { name: 'compCardCover', maxCount: 1 },
  { name: 'compCardA', maxCount: 1 },
  { name: 'compCardB', maxCount: 1 },
  { name: 'compCardC', maxCount: 1 },
  { name: 'compCardD', maxCount: 1 },
  { name: 'media', maxCount: 10 },
])

// Middleware for the route
// Multer Middleware- For handling file upload
app.use(mount('/api/graphql', function* gogogadet(next) {
  yield multerMiddleware(this.request, this.response)

  yield next
}))

app.use(conditional())

app.use(etag())

app.use(helmet())

app.use(function* authenticateWithJsonWebToken(next) {
  const thisContext = this
  const ctx = thisContext

  yield passport.authenticate('jwt', function* customAuth(err, user) {
    if (user) {
      yield ctx.login(user)
    }
  }).call(this, next)

  yield next
})

app.use(mount('/api/graphql', graphqlHTTP((req, ctx) => ({
  formatError: formatGraphQLError,
  schema: graphQLSchema,
  graphiql: true,
  rootValue: {
    request: ctx.req,
  },
  context: ctx,
}))))

app.listen(apiConfig.port)
console.log(`CMS API listening on port ${apiConfig.port}.`)
