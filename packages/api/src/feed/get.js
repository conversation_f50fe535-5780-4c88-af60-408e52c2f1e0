/* Originally from https://github.com/danmactough/node-feedparser/blob/master/examples/iconv.js */

/**
 * Tips
 * ====
 * - Set `user-agent` and `accept` headers when sending requests. Some services will not respond as expected without them.
 * - Set `pool` to false if you send lots of requests using "request" library.
 */

import fetch from 'node-fetch'
import FeedParser from 'feedparser'
import iconv from 'iconv-lite'
import { Transform } from 'stream'

function maybeTranslate(res, charset) {
  let stream = res
  // Use iconv-lite if its not utf8 already.
  if (charset && !/utf-*8/i.test(charset)) {
    try {
      const iconvStream = new Transform({
        transform(chunk, encoding, callback) {
          try {
            const decoded = iconv.decode(chunk, charset)
            const encoded = iconv.encode(decoded, 'utf-8')
            callback(null, encoded)
          } catch (err) {
            callback(err)
          }
        },
      })
      iconvStream.on('error', (err) => {
        throw new Error(`Iconv error: ${err.message}`)
      })
      stream = res.pipe(iconvStream)
    } catch (err) {
      res.emit('error', err)
    }
  }

  return stream
}

function getParams(str) {
  const params = str.split(';').reduce((object, param) => {
    const parts = param.split('=').map(part => part.trim())
    if (parts.length === 2) {
      return { ...object, [parts[0]]: parts[1] }
    }
    return object
  }, {})

  return params
}

export default async function feedPromise(feed, limit = 10) {
  try {
    const response = await fetch(feed, {
      headers: {
        'user-agent': 'Mozilla/5.0 (Macintosh Intel Mac OS X 10_8_5) AppleWebKit/537.36 (KHTML, like Gecko) ' +
          'Chrome/31.0.1650.63 Safari/537.36',
        accept: 'text/html,application/xhtml+xml',
      },
      timeout: 10000,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const items = []
    const feedparser = new FeedParser({ addmeta: false })

    return new Promise((resolve, reject) => {
      feedparser.on('error', reject)
      feedparser.on('end', function end() {
        resolve({ ...this.meta, items })
      })

      feedparser.on('readable', function readItems() {
        if (items.length >= limit) {
          feedparser.emit('end')
          return
        }

        let post = this.read()
        while (post && items.length < limit) {
          items.push(post)
          post = this.read()
        }
      })

      const charset = getParams(response.headers.get('content-type') || '').charset
      const feedStream = maybeTranslate(response.body, charset)
      feedStream.pipe(feedparser)
    })
  } catch (error) {
    throw new Error(`Feed error: ${error.message}`)
  }
}
