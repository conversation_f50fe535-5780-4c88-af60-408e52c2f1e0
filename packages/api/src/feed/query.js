import {
  GraphQLInt,
} from 'graphql/type'

import { requireAuthentication } from '../authentication'
import { FeedType } from './types'
import loadFeed from './get'

export default {

  feed: {
    type: FeedType,
    args: {
      limit: { type: GraphQLInt, defaultValue: 10 },
    },
    resolve: async (root, { limit }, context) => {
      requireAuthentication(context)

      const data = await loadFeed('http://feeds.feedburner.com/businessmodelmag/ItTo', limit) // http://www.businessmodelmag.com/news?format=rss

      return data
    },
  },

}
