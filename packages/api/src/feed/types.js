import {
  GraphQLObjectType,
  GraphQLString,
  GraphQLList,
  GraphQLID,
} from 'graphql/type'

export const FeedItemType = new GraphQLObjectType({
  name: 'FeedItem',
  fields: {
    title: { type: GraphQLString },
    summary: { type: GraphQLString },
    description: { type: GraphQLString },
    link: { type: GraphQLString },
    guid: { type: GraphQLID },
    pubDate: { type: GraphQLString },

  },
})

export const FeedType = new GraphQLObjectType({
  name: 'Feed',
  fields: {
    title: { type: GraphQLString },
    items: { type: new GraphQLList(FeedItemType) },
  },
})
