import { <PERSON><PERSON>h<PERSON>ID, GraphQLString, GraphQLList } from 'graphql/type'

import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { getSessionUserId } from '../utils/session'

import { BookingType } from './types'
import { Booking } from '../models/bookings'
import { requireAdminOrRepresentative } from '../authentication'

const modelFields = modelToGraphQLFields(Booking, true)

delete modelFields.id
delete modelFields._id
delete modelFields.account

export default {
  booking: {
    type: BookingType,
    args: {
      id: { type: GraphQLID },
    },
    resolve: (root, { id }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)
      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)

      return Booking.findOne({ id, account })
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },

  bookings: {
    type: new GraphQLList(BookingType),
    args: {
      ...modelFields,
    },
    resolve: (root, args, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const select = getSelectFieldsFromAST(fieldASTs)
      const account = getSessionUserId(context)
      const query = { ...args, account }

      return Booking.find(query)
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },
}
