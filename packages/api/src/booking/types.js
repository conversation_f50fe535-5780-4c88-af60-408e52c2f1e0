import {
  GraphQLObjectType,
  GraphQLID,
  GraphQLString,
  GraphQLInt,
  GraphQLBoolean,
  GraphQLList,
  GraphQLEnumType,
  GraphQLInputObjectType,
} from 'graphql/type'

import { isValidObjectId } from '../utils/mongoose'
import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import { Booking } from '../models/bookings'
import Project from '../models/projects'
import { ProfileType } from '../profile/types'
import { ProjectType } from '../project/types'
import { requireAdminOrRepresentative } from '../authentication'
import { selectionContainsOnlyId } from '../utils/graphql'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'

const modelFields = modelToGraphQLFields(Booking, false, true)

export const BookingMemberStatusType = new GraphQLEnumType({
  name: 'BookingMemberStatus',
  values: {
    pending: { value: 'pending' },
    applied: { value: 'applied' },
    invited: { value: 'invited' },
    confirmed: { value: 'confirmed' },
    declined: { value: 'declined' },
    released: { value: 'released' },
  },
})

export const BookingMemberType = new GraphQLObjectType({
  name: 'BookingMember',
  fields: () => ({
    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },
    added: {
      type: GraphQLString,
    },
    updated: {
      type: GraphQLString,
    },
    status: {
      type: BookingMemberStatusType,
    },
    profile: {
      type: ProfileType,
    },
  }),
})

export const BookingDateType = new GraphQLObjectType({
  name: 'BookingDate',
  fields: () => ({
    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },
    start: {
      type: GraphQLString,
    },
    end: {
      type: GraphQLString,
    },
  }),
})

export const BookingDateInputType = new GraphQLInputObjectType({
  name: 'BookingDateInput',
  fields: {
    start: {
      type: GraphQLString,
    },
    end: {
      type: GraphQLString,
    },
  },
})

export const BookingType = new GraphQLObjectType({
  name: 'Booking',
  fields: () => ({
    ...modelFields,

    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    dates: {
      type: new GraphQLList(BookingDateType),
    },

    members: {
      type: new GraphQLList(BookingMemberType),
      resolve: ({ members }, args, context) => {
        requireAdminOrRepresentative(context)

        return members
      },
    },

    project: {
      type: ProjectType,
      resolve: ({ project }, args, context, { fieldASTs }) => {
        if (!isValidObjectId(project)) return project
        if (selectionContainsOnlyId(fieldASTs)) return { _id: project }

        const select = getSelectFieldsFromAST(fieldASTs)

        return Project.findById(project).select(select)
      },
    },
  }),
})
