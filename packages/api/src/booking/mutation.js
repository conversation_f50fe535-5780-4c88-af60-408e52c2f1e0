import { GraphQLBoolean, GraphQLID, GraphQLNonNull, GraphQLList } from 'graphql/type'
import {
  getUserId,
  requireAuthentication,
  requireRepresentative,
  requireAdminOrRepresentative,
} from '../authentication'
import { requireMatchingAccountOrAdmin } from '../permissions'
import { getSessionUserId } from '../utils/session'
import modelToGraph<PERSON>Fields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { BookingType, BookingMemberStatusType, BookingDateInputType } from './types'
import { Booking } from '../models/bookings'
import { Casting } from '../models/castings'
import { ModelProfile } from '../models/profiles'
import sendBookingInviteEmail from '../emails/booking-invite'
import sendBookingReleaseEmail from '../emails/booking-release'
import sendBookingCancellationEmail from '../emails/booking-cancellation'
import sendBookingDetailsChangeEmail from '../emails/booking-details-change'

const modelFields = modelToGraphQLFields(Booking)

delete modelFields.id
delete modelFields._id
delete modelFields.created
delete modelFields.updated
delete modelFields.project
delete modelFields.account

export default {
  createBooking: {
    type: BookingType,
    args: {
      ...modelFields,
      project: { type: new GraphQLNonNull(GraphQLID) },
      dates: { type: new GraphQLList(BookingDateInputType) },
    },
    resolve: (root, data, context) => {
      requireRepresentative(context)
      const account = getSessionUserId(context)

      return Booking.create({ ...data, account })
    },
  },

  updateBooking: {
    type: BookingType,
    args: {
      ...modelFields,
      id: { type: new GraphQLNonNull(GraphQLID) },
      dates: { type: new GraphQLList(BookingDateInputType) },
    },
    resolve: async (root, { id, ...updates }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)
      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)

      if (updates.dates || updates.location || updates.notes) {
        const booking = await Booking.findOne({ _id: id, account }).populate(
          'project members.profile',
        )

        booking.members.forEach(async (membership) => {
          if (membership.status === 'confirmed') {
            await sendBookingDetailsChangeEmail(booking, membership, updates)
          }
        })
      }

      return Booking.findOneAndUpdate({ _id: id, account }, updates, { new: true })
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },

  cancelBooking: {
    type: GraphQLBoolean,
    args: {
      bookingId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { bookingId }, context) => {
      requireRepresentative(context)
      const account = getSessionUserId(context)

      const [booking, hasCastings] = await Promise.all([
        Booking.findOne({
          _id: bookingId,
          account,
        }).populate('project members.profile'),
        Casting.find({ booking: bookingId }).then(castings => !!castings.length),
      ])

      if (!booking) {
        throw new Error('Not a valid booking.')
      }

      if (hasCastings) {
        throw new Error('Cannot cancel booking which has an associated castings.')
      }

      booking.members.forEach(async (membership) => {
        await sendBookingCancellationEmail(booking, membership)
      })

      return !!await booking.remove()
    },
  },

  addMemberToBooking: {
    type: BookingType,
    args: {
      bookingId: { type: new GraphQLNonNull(GraphQLID) },
      memberId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { bookingId, memberId }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)
      const isMemberQuery = {
        _id: bookingId,
        'members.profile': { $in: [memberId] },
      }

      const [bookingExists, profileExists, isAlreadyMember] = await Promise.all([
        Booking.find({ _id: bookingId, account }).select('_id').then(booking => !!booking.length),
        ModelProfile.findById(memberId).select('_id').then(profile => !!profile),
        Booking.find(isMemberQuery).select('_id').then(booking => !!booking.length),
      ])

      if (!bookingExists) {
        throw new Error('Booking does not exist.')
      }

      if (!profileExists) {
        throw new Error('Member profile does not exists.')
      }

      if (isAlreadyMember) {
        throw new Error('Talent already added to this booking.')
      }

      const update = {
        $addToSet: {
          members: { profile: memberId, added: new Date(), status: 'pending' },
        },
      }

      return Booking.findByIdAndUpdate(bookingId, update, { new: true })
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },

  removeBookingMembership: {
    type: BookingType,
    args: {
      bookingId: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
    },
    resolve: async (root, { bookingId, membershipId }, context, { fieldASTs }) => {
      requireAdminOrRepresentative(context)

      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)

      const query = {
        _id: bookingId,
        account,
        'members._id': membershipId,
      }

      const update = {
        $pull: {
          members: { _id: membershipId },
        },
      }

      return Booking.findOneAndUpdate(query, update, { new: true })
        .select(`${select} project members`)
        .populate('project members.profile')
    },
  },

  updateBookingMembership: {
    type: BookingType,
    args: {
      bookingId: { type: new GraphQLNonNull(GraphQLID) },
      membershipId: { type: new GraphQLNonNull(GraphQLID) },
      status: { type: new GraphQLNonNull(BookingMemberStatusType) },
    },
    resolve: async (
      root,
      { bookingId, membershipId, status: newStatus },
      context,
      { fieldASTs },
    ) => {
      requireAdminOrRepresentative(context)

      if (['applied', 'confirmed', 'declined'].includes(newStatus)) {
        throw new Error(`Only talent may set status to ${newStatus}`)
      }

      const account = getSessionUserId(context)

      const select = getSelectFieldsFromAST(fieldASTs)

      const booking = await Booking.findOne({
        _id: bookingId,
        account,
        'members._id': membershipId,
      }).select('_id approved members')

      if (!booking) {
        throw new Error('Not a valid booking.')
      }

      const membership = booking.members.id(membershipId)

      if (!membership) {
        throw new Error('Not a valid membership.')
      }

      const oldStatus = membership.status

      if (oldStatus === newStatus) {
        throw new Error(`Already ${newStatus}`)
      }

      if (newStatus === 'invited') {
        if (['invited', 'confirmed', 'declined'].includes(oldStatus)) {
          throw new Error(`Cannot invite someone who is already ${oldStatus}`)
        }

        if (!booking.approved) {
          throw new Error('Cannot invite talent to an unapproved booking.')
        }
      }

      const update = {
        $set: {
          'members.$.status': newStatus,
        },
      }

      const updateResult = await Booking.findOneAndUpdate(
        {
          _id: bookingId,
          account,
          'members._id': membershipId,
        },
        update,
        { new: true },
      )
        .select(`${select} name project members`)
        .populate('project members.profile')

      if (updateResult) {
        const membershipData = updateResult.members.id(membershipId)

        if (newStatus === 'invited') await sendBookingInviteEmail(updateResult, membershipData)
        if (newStatus === 'released' && ['applied', 'invited', 'confirmed'].includes(oldStatus)) {
          await sendBookingReleaseEmail(updateResult, membershipData)
        }
      }

      return updateResult
    },
  },
}
