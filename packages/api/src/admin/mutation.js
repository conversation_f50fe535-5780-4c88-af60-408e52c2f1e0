import {
  GraphQLString,
  GraphQLFloat,
  GraphQLObjectType,
  GraphQLList,
  GraphQLID,
  GraphQLNonNull,
} from 'graphql/type'

import { requireAdmin } from '../authentication'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { Booking } from '../models/bookings'
import { Casting } from '../models/castings'
import { JobType } from '../job/types'

// import { AdminQueryType } from './types'

function updateJobApprovalFactory(approved = false) {
  return async function updateJobApproval(root, { id }) {
    const job = await Booking.findByIdAndUpdate(id, { approved }, { new: true })
      .populate('project')

    if (!job) {
      throw new Error('Invalid job')
    }

    job.castings = await Casting.find({
      booking: id,
    })

    return job
  }
}

export const AdminMutationType = new GraphQLObjectType({
  name: 'AdminMutation',
  fields: () => ({
    approveJob: {
      type: JobType,
      args: {
        id: { type: new GraphQLNonNull(GraphQLID) },
      },
      resolve: updateJobApprovalFactory(true),
    },
    disapproveJob: {
      type: JobType,
      args: {
        id: { type: new GraphQLNonNull(GraphQLID) },
      },
      resolve: updateJobApprovalFactory(false),
    },
  }),
})

export default {
  admin: {
    type: AdminMutationType,
    args: {},
    resolve: (root, args, context) => {
      requireAdmin(context)

      return {}
    },
  },
}
