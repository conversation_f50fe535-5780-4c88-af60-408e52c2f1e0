import {
  GraphQLString,
  GraphQLFloat,
  GraphQLObjectType,
  GraphQLList,
  GraphQLID,
  GraphQLNonNull,
} from 'graphql/type'

import { requireAdmin } from '../authentication'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { Booking } from '../models/bookings'
import { Casting } from '../models/castings'
import { JobType } from '../job/types'

// import { AdminQueryType } from './types'

export const AdminQueryType = new GraphQLObjectType({
  name: 'AdminQuery',
  fields: () => ({
    job: {
      type: JobType,
      args: {
        id: { type: new GraphQLNonNull(GraphQLID) },
      },
      resolve: async (root, { id }) => {
        const job = await Booking.findById(id).populate('project')

        job.castings = await Casting.find({
          booking: id,
        })

        return job
      },
    },
    jobs: {
      type: new GraphQLList(JobType),
      args: {},
      resolve: async (root, args, context, { fieldASTs }) => {
        const select = getSelectFieldsFromAST(fieldASTs)
        const query = {}

        return Booking.find(query).select(`${select} project`).populate('project')
      },
    },
  }),
})

export default {
  admin: {
    type: AdminQueryType,
    args: {},
    resolve: (root, args, context) => {
      requireAdmin(context)

      return {}
    },
  },
}
