import { expect } from 'chai'
import { describe, it } from 'mocha'
import getPackageData from './getPackageData'

/**
  * Sample Test Suite
  * <AUTHOR>
  * @since 2016-02
  * @see {@link http://chaijs.com/api/bdd/|Chai BDD API Reference} for more
*/
describe('Sample', () => {
  it('adds 1 and 2', () => {
    const result = 1 + 2

    expect(result).not.to.have.property('errors')
    expect(result).to.equal(3)
  })

  it('checks integrity of package.json', () => {
    expect(getPackageData).to.not.throw(Error)

    const result = getPackageData()
    expect(result).to.contain.all.keys('dependencies', 'devDependencies')
  })

})
