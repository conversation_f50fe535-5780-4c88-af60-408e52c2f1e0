import fs from 'fs'
import path from 'path'

/**
  * Gets project's package.json file and returns as object
  * <AUTHOR>
  * @since 2016-02
  * @function getPackageData
*/
export default function getPackageData() {
  let data

  try {
    const packagePath = path.resolve(__dirname, '..', '..', '..', 'package.json')
    const packageContents = fs.readFileSync(packagePath, 'utf8')

    data = JSON.parse(packageContents)
  } catch (error) {
    throw new Error('file read or parse error')
  }

  return data
}
