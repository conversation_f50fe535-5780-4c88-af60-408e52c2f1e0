import hmacSha512 from 'crypto-js/hmac-sha512'
import {
  GraphQLID,
  GraphQLString,
  GraphQLNonNull,
} from 'graphql/type'

import { secrets } from '../../config'
import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import Account from '../models/accounts'
import { AccountType } from './types'
import { ProfileTypes } from '../profile/types'
import { ModelProfile } from '../models/profiles'

const accountFields = modelToGraphQLFields(Account)

delete accountFields.id
delete accountFields._id
delete accountFields.created
delete accountFields.updated
delete accountFields.permissions

export default {

  // TODO: most definitely need to throttle this mutation query field
  createAccount: {
    type: AccountType,
    args: {
      ...accountFields,
      type: { type: new GraphQLNonNull(ProfileTypes) },
      email: { type: new GraphQLNonNull(GraphQLString) },
      passPhrase: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: async (root, { type, ...args }) => {
      const data = {
        ...args,
        passPhrase: hmacSha512(args.passPhrase, secrets.passphrase).toString(),
        permissions: { admin: false, representative: type === 'representative' },
      }

      const account = await Account.create(data)
      const profileData = {
        type,
        account: account.id,
        slug: account.id,
        name: `${data.firstName} ${data.lastName}`,
      }

      switch (type) {
        case 'model':
          await ModelProfile.create(profileData)
          break
        case 'representative':
          await ModelProfile.create(profileData)
          break
        default:
          break
      }

      return account
    },
  },

  updateAccount: {
    type: AccountType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      ...accountFields,
    },
    resolve: async (root, { id, ...updates }, ast, { fieldASTs }) =>
      await Account.findById(id).select(getSelectFieldsFromAST(fieldASTs)).then(doc => {
        if (!doc) {
          throw new Error('Account does not exist.')
        }

        doc.set(updates)
        return doc.save()
      }),
  },

}
