import {
  GraphQLID,
  GraphQLString,
  GraphQLList,
  GraphQLNonNull,
} from 'graphql/type'

import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import { requireAuthentication } from '../authentication'
import { requireMatchingAccountOrAdmin } from '../permissions'

import { AccountType } from './types'
import Accounts from '../models/accounts'

const fields = modelToGraphQLFields(Accounts, true)

delete fields.id
delete fields._id

export default {

  account: {
    type: AccountType,
    args: {
      id: { type: new GraphQLNonNull(GraphQLID) },
      slug: { type: GraphQLString },
    },
    resolve: (root, { id, slug }, context, { fieldASTs }) => {
      requireAuthentication(context)
      requireMatchingAccountOrAdmin(context, id)

      const select = getSelectFieldsFromAST(fieldASTs)
      return Accounts.findOne(id ? { _id: id } : { slug }).select(select)
    },
  },

  accounts: {
    type: new GraphQLList(AccountType),
    args: {
      ...fields,
    },
    resolve: (root, args, ast, { fieldASTs }) => {
      const select = getSelectFieldsFromAST(fieldASTs)
      return Accounts.find(args).select(select)
    },
  },

}
