import {
  GraphQLObjectType,
  GraphQLID,
  GraphQLList,
  GraphQLBoolean,
} from 'graphql/type'

import modelToGraphQLFields from '../utils/modelToGraphQLFields'
import getSelectFieldsFromAST from '../utils/getSelectFieldsFromAST'
import Accounts from '../models/accounts'
import { ModelProfile as Profiles } from '../models/profiles'
import { ProfileType } from '../profile/types'

const fields = modelToGraphQLFields(Accounts, false, true)

delete fields.passPhrase

export const AccountPermissionsType = new GraphQLObjectType({
  name: 'AccountPermissions',
  fields: {

    admin: {
      type: GraphQLBoolean,
    },
    representative: {
      type: GraphQLBoolean,
    },
  },

})

export const AccountType = new GraphQLObjectType({
  name: 'Account',
  fields: {
    ...fields,

    id: {
      type: GraphQLID,
      resolve: ({ _id }) => _id,
    },

    profiles: {
      type: new GraphQLList(ProfileType),
      resolve: ({ _id }, args, ast, { fieldASTs }) => {
        const select = getSelectFieldsFromAST(fieldASTs)
        return Profiles.find({ account: { $in: [_id] } }).select(select)
      },
    },

    permissions: {
      type: AccountPermissionsType,
    },

  },

})
