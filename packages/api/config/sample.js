export const api = {
  port: 3000,
  cors: {
    // more options here: https://github.com/koajs/cors
    origin: function* origin(context) {
      return context.request.header.origin || '*'
    },

    allowMethods: 'GET,HEAD,PUT,POST,DELETE',
    credentials: true,
  },
  jwt: {
    algorithm: 'HS512',
    expiresIn: '24 hours',
    notBefore: '0ms',
    issuer: 'lookbook.io',
    audience: 'lookbook.io',
    subject: 'lookbook.io',
  },
  url: 'http://localhost:8080/',
  pdfBackendUrl: 'https://gkjiea03d6.execute-api.us-west-2.amazonaws.com/dev/profile/pdf',
}

export const aws = {
  region: 'us-west-2',
  s3: {
    bucket: 'sample',
    endpoint: 'https://s3-us-west-2.amazonaws.com',
  },
  ses: {
    region: 'us-east-1',
    endpoint: 'email.us-east-1.amazonaws.com',
    params: {
      ReturnPath: '<EMAIL>',
    },
  },
}

export const secrets = {
  passphrase: 'CHANGEMEORFAIL',
  jwt: 'Changeme, too',
}
