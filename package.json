{"name": "lookbook", "version": "1.0.0", "description": "Lookbook Monorepository", "main": "index.js", "config": {"jsSrc": "packages/api/src packages/spa/src"}, "scripts": {"postinstall": "cd packages/api; yarn install; cd ../spa; yarn install", "test": "npm run lint", "dev": "concurrent --kill-others --prefix none \"yarn run api\" \"yarn run spa\" \"yarn run watch:lint -s\" ", "start": "echo \"nothing to run in production here\"", "api": "cd packages/api; yarn run dev", "spa": "cd packages/spa; yarn run dev", "lint": "yarn run lint:eslint -s", "lint:eslint": "eslint $npm_package_config_jsSrc", "watch:lint": "watch 'yarn run lint -s' $npm_package_config_jsSrc", "build:spa": "cd packages/spa; yarn run build", "deploy:spa": "cd packages/spa; yarn run deploy"}, "author": "<PERSON>, Robotag Media K.K, et. al.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/adieuadieu/lookbook"}, "devDependencies": {"babel-eslint": "^7.2.3", "concurrently": "^3.5.0", "eslint": "^4.2.0", "eslint-config-airbnb": "^15.0.2", "eslint-plugin-import": "^2.7.0", "eslint-plugin-jsx-a11y": "^6.0.2", "eslint-plugin-react": "^7.1.0", "mocha": "^3.4.2", "watch": "^1.0.2"}, "dependencies": {"iconv-lite": "0.6.3"}}